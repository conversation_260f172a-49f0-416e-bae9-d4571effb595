/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Link} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Link | undefined}
 *   mdast node.
 */
export function iframe(state: State, node: Readonly<Element>): Link | undefined;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Link } from 'mdast';
//# sourceMappingURL=iframe.d.ts.map