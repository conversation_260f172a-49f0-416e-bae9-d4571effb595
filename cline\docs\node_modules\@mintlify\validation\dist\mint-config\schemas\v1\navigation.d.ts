import { NavigationGroup } from '@mintlify/models';
import { z } from 'zod';
export declare const baseNavigationGroupSchema: z.ZodObject<{
    group: z.ZodString;
    icon: z.ZodOptional<z.ZodString>;
    iconType: z.<PERSON><z.<PERSON><PERSON><["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
    version: z.ZodOptional<z.ZodString>;
}, "strict", z.ZodTypeAny, {
    group: string;
    icon?: string | undefined;
    iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    version?: string | undefined;
}, {
    group: string;
    icon?: string | undefined;
    iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    version?: string | undefined;
}>;
export declare const navigationSchema: z.ZodArray<z.ZodType<NavigationGroup, z.ZodTypeDef, NavigationGroup>, "many">;
