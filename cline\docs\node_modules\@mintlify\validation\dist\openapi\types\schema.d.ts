import { OpenAPIV3_1 } from 'openapi-types';
export type Schema3_1 = OpenAPIV3_1.SchemaObject;
export type Ref = OpenAPIV3_1.ReferenceObject;
export type SchemaOrRef = Schema3_1 | Ref;
export type ComponentsSection = keyof OpenAPIV3_1.ComponentsObject;
export type Compositions = 'allOf' | 'oneOf' | 'anyOf' | 'not';
export type SimpleSchema = Omit<OpenAPIV3_1.SchemaObject, Compositions> & {
    items?: OpenAPIV3_1.SchemaObject;
    refIdentifier?: string;
    _depth?: number;
    isOneOf?: string;
    isAnyOf?: string;
    isAllOf?: string;
};
export type SumOfProducts = SimpleSchema[][];
export type SimpleSchemaWithSubschemas = SimpleSchema & {
    properties?: Record<string, {
        allOf: SchemaOrRef[];
    }>;
    items?: {
        allOf: SchemaOrRef[];
    };
    additionalProperties?: {
        allOf: SchemaOrRef[];
    } | false;
};
export type SchemaOrRefComposition = SchemaOrRef & {
    isOneOf?: string;
    isAnyOf?: string;
    isAllOf?: string;
};
