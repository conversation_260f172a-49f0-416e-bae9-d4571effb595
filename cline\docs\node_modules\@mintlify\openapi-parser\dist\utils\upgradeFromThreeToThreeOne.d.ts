import type { AnyObject } from '../types/index.js';
/**
 * Upgrade from OpenAPI 3.0.x to 3.1.0
 *
 * https://www.openapis.org/blog/2021/02/16/migrating-from-openapi-3-0-to-3-1-0
 */
export declare function upgradeFromThreeToThreeOne(originalSpecification: AnyObject): AnyObject;
/** Determine if the current path is within a schema */
export declare function isSchemaPath(path: string[]): boolean;
//# sourceMappingURL=upgradeFromThreeToThreeOne.d.ts.map