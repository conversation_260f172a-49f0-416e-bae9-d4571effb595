/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {undefined}
 *   Nothing.
 */
export function base(state: State, node: Readonly<Element>): undefined;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
//# sourceMappingURL=base.d.ts.map