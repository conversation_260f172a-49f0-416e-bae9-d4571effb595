import { z } from 'zod';
export declare const hexColor: z.ZodString;
export declare const colorSchemaWithOptionalLightAndDark: z.ZodObject<{
    light: z.ZodOptional<z.ZodString>;
    dark: z.ZodOptional<z.ZodString>;
}, "strict", z.<PERSON>ny, {
    light?: string | undefined;
    dark?: string | undefined;
}, {
    light?: string | undefined;
    dark?: string | undefined;
}>;
export type Color = z.infer<typeof colorSchemaWithOptionalLightAndDark>;
