{"type": "object", "properties": {"authorizationUrl": {"type": "string", "format": "uri"}, "tokenUrl": {"type": "string", "format": "uri"}, "refreshUrl": {"type": "string", "format": "uri"}, "scopes": {"$ref": "http://asyncapi.com/definitions/2.0.0/oauth2Scopes.json"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.0.0/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0/oauth2Flow.json"}