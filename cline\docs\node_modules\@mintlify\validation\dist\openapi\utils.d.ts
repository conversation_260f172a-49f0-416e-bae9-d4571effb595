import { OpenAPIV3_1 } from 'openapi-types';
import { DataSchema } from './types/endpoint.js';
import { ComponentsSection, Schema3_1, SimpleSchema, SimpleSchemaWithSubschemas } from './types/schema.js';
export declare const stringFileFormats: string[];
export declare const structuredDataContentTypes: string[];
export declare function dereference<S extends ComponentsSection>(section: S, $ref: string, components: Record<string, NonNullable<OpenAPIV3_1.ComponentsObject[S]>[string]> | undefined, maxDepth?: number): NonNullable<OpenAPIV3_1.ComponentsObject[S]>[string] | undefined;
export declare const addKeyIfDefined: <D, K extends keyof D>(key: K, value: D[K] | undefined, destination: D) => void;
export declare const copyKeyIfDefined: <D, K extends keyof D>(key: K, source: Pick<D, K>, destination: D) => void;
export declare const copyExampleIfDefined: (source: Pick<OpenAPIV3_1.SchemaObject, "example" | "examples">, destination: Pick<DataSchema, "example">) => void;
export declare function recursivelyFindDescription(schema: SimpleSchema, name: string | undefined): string | undefined;
export declare function sortSchemas(schemas: SimpleSchema[]): void;
export declare function mergeTypes(acc: SimpleSchemaWithSubschemas, curr: SimpleSchema): void;
export declare function normalizeMinMax(schema: SimpleSchema): void;
export declare function combineTitle(acc: SimpleSchemaWithSubschemas, curr: SimpleSchema): void;
export declare function copyAndCombineKeys(acc: SimpleSchemaWithSubschemas, curr: SimpleSchema): void;
export declare function combineExamples(acc: SimpleSchemaWithSubschemas, curr: SimpleSchema): void;
export declare function combineProperties(acc: SimpleSchemaWithSubschemas, curr: SimpleSchema, componentSchemas: Record<string, Schema3_1> | undefined, location?: 'request' | 'response'): void;
export declare function combineDescription(acc: SimpleSchemaWithSubschemas, curr: SimpleSchema, schemas: SimpleSchema[]): void;
export declare function combineAdditionalProperties(acc: SimpleSchemaWithSubschemas, curr: SimpleSchema): void;
export declare function discriminatorAndSchemaRefsMatch(schema: SimpleSchema): boolean;
