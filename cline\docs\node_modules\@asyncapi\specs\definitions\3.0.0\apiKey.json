{"type": "object", "required": ["type", "in"], "properties": {"type": {"type": "string", "description": "The type of the security scheme", "enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "in": {"type": "string", "description": " The location of the API key.", "enum": ["user", "password"]}, "description": {"type": "string", "description": "A short description for security scheme. CommonMark syntax MAY be used for rich text representation."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/apiKey.json"}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/apiKey.json"}