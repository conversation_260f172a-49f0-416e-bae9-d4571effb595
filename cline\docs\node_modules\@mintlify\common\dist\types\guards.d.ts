export declare function isString(val: unknown): val is string;
export declare function isNumber(val: unknown): val is number;
export declare function isInteger(val: unknown): val is number;
export declare function isObject(val: unknown): val is Record<string, unknown>;
export declare function isArray(val: unknown): val is Array<unknown>;
export declare function isBoolean(val: unknown): val is boolean;
export declare function isNull(val: unknown): val is null;
/** File is a web API - must do window check to avoid server-side error */
export declare function isFile(val: unknown): val is File;
