import { OpenAPIV3_1 } from 'openapi-types';
import { BaseConverter } from './BaseConverter.js';
import { DataSchemaArray, IncrementalDataSchemaArray, ParameterSections } from './types/endpoint.js';
export declare class ParametersConverter extends BaseConverter {
    readonly method: string;
    readonly pathParameters: OpenAPIV3_1.ParameterObject[] | undefined;
    readonly operationParameters: OpenAPIV3_1.ParameterObject[] | undefined;
    readonly path: string[];
    readonly safeParse: boolean;
    private parameterSections;
    private constructor();
    private convert;
    private addParameter;
    static convert(method: string, pathParameters: OpenAPIV3_1.ParameterObject[] | undefined, operationParameters: OpenAPIV3_1.ParameterObject[] | undefined, path: string[], safeParse?: boolean): ParameterSections<DataSchemaArray>;
}
export declare class IncrementalParametersConverter extends BaseConverter {
    readonly rawDocument: OpenAPIV3_1.Document;
    readonly method: string;
    readonly pathParameters: (OpenAPIV3_1.ParameterObject | OpenAPIV3_1.ReferenceObject)[] | undefined;
    readonly operationParameters: (OpenAPIV3_1.ParameterObject | OpenAPIV3_1.ReferenceObject)[] | undefined;
    readonly path: string[];
    readonly safeParse: boolean;
    private parameterSections;
    private constructor();
    private convert;
    private addParameter;
    static convert(rawDocument: OpenAPIV3_1.Document, method: string, pathParameters: (OpenAPIV3_1.ParameterObject | OpenAPIV3_1.ReferenceObject)[] | undefined, operationParameters: (OpenAPIV3_1.ParameterObject | OpenAPIV3_1.ReferenceObject)[] | undefined, path: string[], safeParse?: boolean): ParameterSections<IncrementalDataSchemaArray>;
}
