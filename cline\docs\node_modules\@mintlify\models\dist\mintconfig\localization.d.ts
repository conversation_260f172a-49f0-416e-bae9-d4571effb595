export declare const locales: readonly ["en", "cn", "zh", "zh-Hans", "zh-Hant", "es", "fr", "fr-CA", "ja", "jp", "pt", "pt-BR", "de", "ko", "it", "ru", "id", "ar", "tr", "hi"];
export declare const localeDisplayNames: {
    readonly en: "English";
    readonly cn: "Chinese";
    readonly zh: "Chinese";
    readonly 'zh-Hans': "Simplified Chinese";
    readonly 'zh-Hant': "Traditional Chinese";
    readonly es: "Spanish";
    readonly fr: "French";
    readonly 'fr-CA': "Canadian French";
    readonly ja: "Japanese";
    readonly jp: "Japanese";
    readonly pt: "Portuguese";
    readonly 'pt-BR': "Brazilian Portuguese";
    readonly de: "German";
    readonly ko: "Korean";
    readonly it: "Italian";
    readonly ru: "Russian";
    readonly id: "Indonesian";
    readonly ar: "Arabic";
    readonly tr: "Turkish";
    readonly hi: "Hindi";
};
export declare const localeDisplayFlags: {
    readonly en: "🇺🇸";
    readonly cn: "🇨🇳";
    readonly zh: "🇨🇳";
    readonly 'zh-Hans': "🇨🇳";
    readonly 'zh-Hant': "🇹🇼";
    readonly es: "🇪🇸";
    readonly fr: "🇫🇷";
    readonly 'fr-CA': "🇨🇦";
    readonly ja: "🇯🇵";
    readonly jp: "🇯🇵";
    readonly pt: "🇧🇷";
    readonly 'pt-BR': "🇧🇷";
    readonly de: "🇩🇪";
    readonly ko: "🇰🇷";
    readonly it: "🇮🇹";
    readonly ru: "🇷🇺";
    readonly id: "🇮🇩";
    readonly ar: "🇸🇦";
    readonly tr: "🇹🇷";
    readonly hi: "🇮🇳";
};
export type LocaleType = (typeof locales)[number];
export type LocaleDisplayName = (typeof localeDisplayNames)[keyof typeof localeDisplayNames];
export type LocaleDisplayFlag = (typeof localeDisplayFlags)[keyof typeof localeDisplayFlags];
export declare const getLocaleDisplayName: (locale: LocaleType) => LocaleDisplayName;
export declare const getLocaleDisplayFlag: (locale: LocaleType) => LocaleDisplayFlag;
export declare const LocaleCodes: LocaleType[];
export declare const LocaleDisplayNames: LocaleDisplayName[];
export declare const LocaleDisplayFlags: LocaleDisplayFlag[];
export declare const LlmSupportedLocaleCodes: ("en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "id" | "ar" | "tr" | "hi")[];
export declare const LlmSupportedLocaleDisplayNames: ("English" | "Simplified Chinese" | "Traditional Chinese" | "Spanish" | "French" | "Canadian French" | "Japanese" | "Portuguese" | "Brazilian Portuguese" | "German" | "Korean" | "Italian" | "Russian" | "Indonesian" | "Arabic" | "Turkish" | "Hindi")[];
export declare const LlmSupportedLocaleDisplayFlags: ("🇺🇸" | "🇹🇼" | "🇪🇸" | "🇫🇷" | "🇨🇦" | "🇯🇵" | "🇧🇷" | "🇩🇪" | "🇰🇷" | "🇮🇹" | "🇷🇺" | "🇮🇩" | "🇸🇦" | "🇹🇷" | "🇮🇳")[];
export type LlmSupportedLocaleType = (typeof LlmSupportedLocaleCodes)[number];
export type LlmSupportedLocaleDisplayName = (typeof LlmSupportedLocaleDisplayNames)[number];
export type LlmSupportedLocaleDisplayFlag = (typeof LlmSupportedLocaleDisplayFlags)[number];
