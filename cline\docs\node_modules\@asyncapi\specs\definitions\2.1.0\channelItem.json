{"type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.1.0/specificationExtension.json"}}, "properties": {"$ref": {"$ref": "http://asyncapi.com/definitions/2.1.0/ReferenceObject.json"}, "parameters": {"$ref": "http://asyncapi.com/definitions/2.1.0/parameters.json"}, "description": {"type": "string", "description": "A description of the channel."}, "publish": {"$ref": "http://asyncapi.com/definitions/2.1.0/operation.json"}, "subscribe": {"$ref": "http://asyncapi.com/definitions/2.1.0/operation.json"}, "deprecated": {"type": "boolean", "default": false}, "bindings": {"$ref": "http://asyncapi.com/definitions/2.1.0/bindingsObject.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.1.0/channelItem.json"}