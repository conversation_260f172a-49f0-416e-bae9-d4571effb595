{"oneOf": [{"$ref": "http://asyncapi.com/definitions/1.2.0/message.json"}, {"type": "object", "required": ["oneOf"], "additionalProperties": false, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.2.0/vendorExtension.json"}}, "properties": {"oneOf": {"type": "array", "minItems": 2, "items": {"$ref": "http://asyncapi.com/definitions/1.2.0/message.json"}}}}], "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.2.0/operation.json"}