import { z } from 'zod';
export declare function validateMintConfig(value: unknown): z.SafeParseError<{
    name: string;
    favicon: string;
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
        background?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
        anchors?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        ultraLight?: any;
        ultraDark?: any;
    };
    navigation: import("@mintlify/models").NavigationGroup[];
    $schema?: string | undefined;
    mintlify?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    theme?: "venus" | "quill" | "prism" | undefined;
    layout?: "topnav" | "sidenav" | "solidSidenav" | undefined;
    openapi?: string | string[] | undefined;
    topbar?: {
        style?: "gradient" | "default" | undefined;
    } | undefined;
    sidebar?: {
        items?: "container" | "card" | "border" | "undecorated" | undefined;
    } | undefined;
    rounded?: "default" | "sharp" | undefined;
    api?: {
        baseUrl?: string | string[] | undefined;
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
            inputPrefix?: string | undefined;
        } | undefined;
        playground?: {
            mode?: "show" | "simple" | "hide" | undefined;
            disableProxy?: boolean | undefined;
        } | undefined;
        request?: {
            example?: {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            } | undefined;
        } | undefined;
        maintainOrder?: boolean | undefined;
        paramFields?: {
            expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
        } | undefined;
    } | undefined;
    modeToggle?: {
        default?: "light" | "dark" | undefined;
        isHidden?: boolean | undefined;
    } | undefined;
    versions?: (string | {
        name: string;
        url?: string | undefined;
        default?: true | undefined;
        locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
    })[] | undefined;
    metadata?: Record<string, string> | undefined;
    codeBlock?: {
        mode?: "dark" | "auto" | undefined;
    } | undefined;
    eyebrow?: {
        display?: "section" | "breadcrumbs" | undefined;
    } | undefined;
    topbarCtaButton?: {
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    } | {
        type: "github";
        url: string;
    } | undefined;
    topbarLinks?: ({
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    } | {
        type: "github";
        url: string;
    })[] | undefined;
    primaryTab?: {
        name: string;
        isDefaultHidden?: boolean | undefined;
    } | undefined;
    topAnchor?: {
        name: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    } | undefined;
    anchors?: {
        name: string;
        url: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        color?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        isDefaultHidden?: boolean | undefined;
        version?: string | undefined;
        openapi?: string | undefined;
    }[] | undefined;
    tabs?: {
        name: string;
        url: string;
        version?: string | undefined;
        isDefaultHidden?: boolean | undefined;
        openapi?: string | undefined;
    }[] | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
            type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
            url: string;
        }[] | undefined;
        links?: {
            links: {
                url: string;
                label: string;
            }[];
            title?: string | undefined;
        }[] | undefined;
    } | undefined;
    background?: {
        style?: "gradient" | "grid" | "windows" | undefined;
    } | undefined;
    backgroundImage?: string | undefined;
    font?: {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        headings?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    feedback?: {
        thumbsRating?: boolean | undefined;
        suggestEdit?: boolean | undefined;
        raiseIssue?: boolean | undefined;
    } | undefined;
    analytics?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
    } | undefined;
    integrations?: {
        intercom?: string | undefined;
        frontchat?: string | undefined;
        osano?: string | undefined;
    } | undefined;
    isWhiteLabeled?: boolean | undefined;
    search?: {
        prompt?: string | undefined;
        location?: "side" | "top" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    seo?: {
        indexHiddenPages?: boolean | undefined;
    } | undefined;
    footerSocials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }[] | undefined;
}> | {
    warnings: z.ZodIssue[];
    success: true;
    data: {
        name: string;
        $schema: string;
        favicon: string;
        colors: {
            primary: string;
            light?: string | undefined;
            dark?: string | undefined;
            background?: {
                light?: string | undefined;
                dark?: string | undefined;
            } | undefined;
            anchors?: string | {
                from: string;
                to: string;
                via?: string | undefined;
            } | undefined;
            ultraLight?: any;
            ultraDark?: any;
        };
        navigation: import("@mintlify/models").NavigationGroup[];
        mintlify?: string | undefined;
        logo?: string | {
            light: string;
            dark: string;
            href?: string | undefined;
        } | undefined;
        theme?: "venus" | "quill" | "prism" | undefined;
        layout?: "topnav" | "sidenav" | "solidSidenav" | undefined;
        openapi?: string | string[] | undefined;
        topbar?: {
            style?: "gradient" | "default" | undefined;
        } | undefined;
        sidebar?: {
            items?: "container" | "card" | "border" | "undecorated" | undefined;
        } | undefined;
        rounded?: "default" | "sharp" | undefined;
        api?: {
            baseUrl?: string | string[] | undefined;
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
                inputPrefix?: string | undefined;
            } | undefined;
            playground?: {
                mode: "show" | "simple" | "hide";
                disableProxy?: boolean | undefined;
            } | undefined;
            request?: {
                example?: {
                    showOptionalParams?: boolean | undefined;
                    languages?: string[] | undefined;
                } | undefined;
            } | undefined;
            maintainOrder?: boolean | undefined;
            paramFields?: {
                expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
            } | undefined;
        } | undefined;
        modeToggle?: {
            default?: "light" | "dark" | undefined;
            isHidden?: boolean | undefined;
        } | undefined;
        versions?: (string | {
            name: string;
            url?: string | undefined;
            default?: true | undefined;
            locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
        })[] | undefined;
        metadata?: Record<string, string> | undefined;
        codeBlock?: {
            mode?: "dark" | "auto" | undefined;
        } | undefined;
        eyebrow?: {
            display?: "section" | "breadcrumbs" | undefined;
        } | undefined;
        topbarCtaButton?: {
            name: string;
            url: string;
            type?: "link" | undefined;
            style?: "pill" | "roundedRectangle" | undefined;
            arrow?: boolean | undefined;
        } | {
            type: "github";
            url: string;
        } | undefined;
        topbarLinks?: ({
            name: string;
            url: string;
            type?: "link" | undefined;
            style?: "pill" | "roundedRectangle" | undefined;
            arrow?: boolean | undefined;
        } | {
            type: "github";
            url: string;
        })[] | undefined;
        primaryTab?: {
            name: string;
            isDefaultHidden?: boolean | undefined;
        } | undefined;
        topAnchor?: {
            name: string;
            icon?: string | undefined;
            iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        } | undefined;
        anchors?: {
            name: string;
            url: string;
            icon?: string | undefined;
            iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            color?: string | {
                from: string;
                to: string;
                via?: string | undefined;
            } | undefined;
            isDefaultHidden?: boolean | undefined;
            version?: string | undefined;
            openapi?: string | undefined;
        }[] | undefined;
        tabs?: {
            name: string;
            url: string;
            version?: string | undefined;
            isDefaultHidden?: boolean | undefined;
            openapi?: string | undefined;
        }[] | undefined;
        footer?: {
            socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
                type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
                url: string;
            }[] | undefined;
            links?: {
                links: {
                    url: string;
                    label: string;
                }[];
                title?: string | undefined;
            }[] | undefined;
        } | undefined;
        background?: {
            style?: "gradient" | "grid" | "windows" | undefined;
        } | undefined;
        backgroundImage?: string | undefined;
        font?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | {
            headings?: {
                family: string;
                weight?: number | undefined;
                url?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
            body?: {
                family: string;
                weight?: number | undefined;
                url?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
        } | undefined;
        feedback?: {
            thumbsRating?: boolean | undefined;
            suggestEdit?: boolean | undefined;
            raiseIssue?: boolean | undefined;
        } | undefined;
        analytics?: {
            amplitude?: {
                apiKey: string;
            } | undefined;
            clearbit?: {
                publicApiKey: string;
            } | undefined;
            fathom?: {
                siteId: string;
            } | undefined;
            ga4?: {
                measurementId: string;
            } | undefined;
            gtm?: {
                tagId: string;
            } | undefined;
            heap?: {
                appId: string;
            } | undefined;
            hotjar?: {
                hjid: string;
                hjsv: string;
            } | undefined;
            koala?: {
                publicApiKey: string;
            } | undefined;
            logrocket?: {
                appId: string;
            } | undefined;
            mixpanel?: {
                projectToken: string;
            } | undefined;
            pirsch?: {
                id: string;
            } | undefined;
            posthog?: {
                apiKey: string;
                apiHost?: string | undefined;
            } | undefined;
            plausible?: {
                domain: string;
                server?: string | undefined;
            } | undefined;
            segment?: {
                key: string;
            } | undefined;
        } | undefined;
        integrations?: {
            intercom?: string | undefined;
            frontchat?: string | undefined;
            osano?: string | undefined;
        } | undefined;
        isWhiteLabeled?: boolean | undefined;
        search?: {
            prompt?: string | undefined;
            location?: "side" | "top" | undefined;
        } | undefined;
        redirects?: {
            source: string;
            destination: string;
            permanent?: boolean | undefined;
        }[] | undefined;
        seo?: {
            indexHiddenPages?: boolean | undefined;
        } | undefined;
        footerSocials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
            type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
            url: string;
        }[] | undefined;
    };
};
export declare function validateDocsConfig(value: unknown): z.SafeParseError<{
    name: string;
    theme: "almond";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    $schema?: string | undefined;
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect?: boolean | undefined;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../index.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    theme: "aspen";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    $schema?: string | undefined;
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect?: boolean | undefined;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../index.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    theme: "linden";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    $schema?: string | undefined;
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect?: boolean | undefined;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../index.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    theme: "maple";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    $schema?: string | undefined;
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect?: boolean | undefined;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../index.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    theme: "mint";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    $schema?: string | undefined;
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect?: boolean | undefined;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../index.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    theme: "palm";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    $schema?: string | undefined;
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect?: boolean | undefined;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../index.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    theme: "willow";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    $schema?: string | undefined;
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect?: boolean | undefined;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../index.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
}> | {
    warnings: z.ZodIssue[];
    success: true;
    data: {
        name: string;
        $schema: string;
        theme: "almond";
        colors: {
            primary: string;
            light?: string | undefined;
            dark?: string | undefined;
        };
        navigation: ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        }) & ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | undefined);
        description?: string | undefined;
        logo?: string | {
            light: string;
            dark: string;
            href?: string | undefined;
        } | undefined;
        favicon?: string | {
            light: string;
            dark: string;
        } | undefined;
        api?: {
            openapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            asyncapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            params?: {
                expanded?: "all" | "closed" | undefined;
            } | undefined;
            playground?: {
                display?: "simple" | "none" | "interactive" | undefined;
                proxy?: boolean | undefined;
            } | undefined;
            examples?: {
                defaults?: "all" | "required" | undefined;
                languages?: string[] | undefined;
            } | undefined;
            mdx?: {
                auth?: {
                    method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                    name?: string | undefined;
                } | undefined;
                server?: string | string[] | undefined;
            } | undefined;
        } | undefined;
        appearance?: {
            default?: "light" | "dark" | "system" | undefined;
            strict?: boolean | undefined;
        } | undefined;
        background?: {
            image?: string | {
                light: string;
                dark: string;
            } | undefined;
            decoration?: "gradient" | "grid" | "windows" | undefined;
            color?: {
                light?: string | undefined;
                dark?: string | undefined;
            } | undefined;
        } | undefined;
        navbar?: {
            links?: {
                href: string;
                label: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            }[] | undefined;
            primary?: {
                type: "button";
                href: string;
                label: string;
            } | {
                type: "github";
                href: string;
            } | undefined;
        } | undefined;
        footer?: {
            socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
            links?: {
                items: {
                    href: string;
                    label: string;
                }[];
                header?: string | undefined;
            }[] | undefined;
        } | undefined;
        search?: {
            prompt?: string | undefined;
        } | undefined;
        seo?: {
            metatags?: Record<string, string> | undefined;
            indexing?: "all" | "navigable" | undefined;
        } | undefined;
        fonts?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | {
            heading?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
            body?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
        } | undefined;
        icons?: {
            library: "fontawesome" | "lucide";
        } | undefined;
        styling?: {
            eyebrows?: "section" | "breadcrumbs" | undefined;
            codeblocks?: "dark" | "system" | undefined;
        } | undefined;
        redirects?: {
            source: string;
            destination: string;
            permanent?: boolean | undefined;
        }[] | undefined;
        integrations?: {
            amplitude?: {
                apiKey: string;
            } | undefined;
            clearbit?: {
                publicApiKey: string;
            } | undefined;
            fathom?: {
                siteId: string;
            } | undefined;
            frontchat?: {
                snippetId: string;
            } | undefined;
            ga4?: {
                measurementId: string;
            } | undefined;
            gtm?: {
                tagId: string;
            } | undefined;
            heap?: {
                appId: string;
            } | undefined;
            hotjar?: {
                hjid: string;
                hjsv: string;
            } | undefined;
            intercom?: {
                appId: string;
            } | undefined;
            koala?: {
                publicApiKey: string;
            } | undefined;
            logrocket?: {
                appId: string;
            } | undefined;
            mixpanel?: {
                projectToken: string;
            } | undefined;
            osano?: {
                scriptSource: string;
            } | undefined;
            pirsch?: {
                id: string;
            } | undefined;
            posthog?: {
                apiKey: string;
                apiHost?: string | undefined;
            } | undefined;
            plausible?: {
                domain: string;
                server?: string | undefined;
            } | undefined;
            segment?: {
                key: string;
            } | undefined;
            telemetry?: {
                enabled?: boolean | undefined;
            } | undefined;
            cookies?: {
                key?: string | undefined;
                value?: string | undefined;
            } | undefined;
        } | undefined;
        banner?: {
            content: string;
            dismissible?: boolean | undefined;
        } | undefined;
        errors?: {
            '404': {
                redirect: boolean;
            };
        } | undefined;
        contextual?: {
            options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
                href: (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                }) & (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                } | undefined);
                title: string;
                description: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            })[];
        } | undefined;
        thumbnails?: {
            appearance?: import("../index.js").ThumbnailAppearance | undefined;
            background?: string | undefined;
        } | undefined;
    } | {
        name: string;
        $schema: string;
        theme: "aspen";
        colors: {
            primary: string;
            light?: string | undefined;
            dark?: string | undefined;
        };
        navigation: ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        }) & ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | undefined);
        description?: string | undefined;
        logo?: string | {
            light: string;
            dark: string;
            href?: string | undefined;
        } | undefined;
        favicon?: string | {
            light: string;
            dark: string;
        } | undefined;
        api?: {
            openapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            asyncapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            params?: {
                expanded?: "all" | "closed" | undefined;
            } | undefined;
            playground?: {
                display?: "simple" | "none" | "interactive" | undefined;
                proxy?: boolean | undefined;
            } | undefined;
            examples?: {
                defaults?: "all" | "required" | undefined;
                languages?: string[] | undefined;
            } | undefined;
            mdx?: {
                auth?: {
                    method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                    name?: string | undefined;
                } | undefined;
                server?: string | string[] | undefined;
            } | undefined;
        } | undefined;
        appearance?: {
            default?: "light" | "dark" | "system" | undefined;
            strict?: boolean | undefined;
        } | undefined;
        background?: {
            image?: string | {
                light: string;
                dark: string;
            } | undefined;
            decoration?: "gradient" | "grid" | "windows" | undefined;
            color?: {
                light?: string | undefined;
                dark?: string | undefined;
            } | undefined;
        } | undefined;
        navbar?: {
            links?: {
                href: string;
                label: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            }[] | undefined;
            primary?: {
                type: "button";
                href: string;
                label: string;
            } | {
                type: "github";
                href: string;
            } | undefined;
        } | undefined;
        footer?: {
            socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
            links?: {
                items: {
                    href: string;
                    label: string;
                }[];
                header?: string | undefined;
            }[] | undefined;
        } | undefined;
        search?: {
            prompt?: string | undefined;
        } | undefined;
        seo?: {
            metatags?: Record<string, string> | undefined;
            indexing?: "all" | "navigable" | undefined;
        } | undefined;
        fonts?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | {
            heading?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
            body?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
        } | undefined;
        icons?: {
            library: "fontawesome" | "lucide";
        } | undefined;
        styling?: {
            eyebrows?: "section" | "breadcrumbs" | undefined;
            codeblocks?: "dark" | "system" | undefined;
        } | undefined;
        redirects?: {
            source: string;
            destination: string;
            permanent?: boolean | undefined;
        }[] | undefined;
        integrations?: {
            amplitude?: {
                apiKey: string;
            } | undefined;
            clearbit?: {
                publicApiKey: string;
            } | undefined;
            fathom?: {
                siteId: string;
            } | undefined;
            frontchat?: {
                snippetId: string;
            } | undefined;
            ga4?: {
                measurementId: string;
            } | undefined;
            gtm?: {
                tagId: string;
            } | undefined;
            heap?: {
                appId: string;
            } | undefined;
            hotjar?: {
                hjid: string;
                hjsv: string;
            } | undefined;
            intercom?: {
                appId: string;
            } | undefined;
            koala?: {
                publicApiKey: string;
            } | undefined;
            logrocket?: {
                appId: string;
            } | undefined;
            mixpanel?: {
                projectToken: string;
            } | undefined;
            osano?: {
                scriptSource: string;
            } | undefined;
            pirsch?: {
                id: string;
            } | undefined;
            posthog?: {
                apiKey: string;
                apiHost?: string | undefined;
            } | undefined;
            plausible?: {
                domain: string;
                server?: string | undefined;
            } | undefined;
            segment?: {
                key: string;
            } | undefined;
            telemetry?: {
                enabled?: boolean | undefined;
            } | undefined;
            cookies?: {
                key?: string | undefined;
                value?: string | undefined;
            } | undefined;
        } | undefined;
        banner?: {
            content: string;
            dismissible?: boolean | undefined;
        } | undefined;
        errors?: {
            '404': {
                redirect: boolean;
            };
        } | undefined;
        contextual?: {
            options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
                href: (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                }) & (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                } | undefined);
                title: string;
                description: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            })[];
        } | undefined;
        thumbnails?: {
            appearance?: import("../index.js").ThumbnailAppearance | undefined;
            background?: string | undefined;
        } | undefined;
    } | {
        name: string;
        $schema: string;
        theme: "linden";
        colors: {
            primary: string;
            light?: string | undefined;
            dark?: string | undefined;
        };
        navigation: ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        }) & ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | undefined);
        description?: string | undefined;
        logo?: string | {
            light: string;
            dark: string;
            href?: string | undefined;
        } | undefined;
        favicon?: string | {
            light: string;
            dark: string;
        } | undefined;
        api?: {
            openapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            asyncapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            params?: {
                expanded?: "all" | "closed" | undefined;
            } | undefined;
            playground?: {
                display?: "simple" | "none" | "interactive" | undefined;
                proxy?: boolean | undefined;
            } | undefined;
            examples?: {
                defaults?: "all" | "required" | undefined;
                languages?: string[] | undefined;
            } | undefined;
            mdx?: {
                auth?: {
                    method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                    name?: string | undefined;
                } | undefined;
                server?: string | string[] | undefined;
            } | undefined;
        } | undefined;
        appearance?: {
            default?: "light" | "dark" | "system" | undefined;
            strict?: boolean | undefined;
        } | undefined;
        background?: {
            image?: string | {
                light: string;
                dark: string;
            } | undefined;
            decoration?: "gradient" | "grid" | "windows" | undefined;
            color?: {
                light?: string | undefined;
                dark?: string | undefined;
            } | undefined;
        } | undefined;
        navbar?: {
            links?: {
                href: string;
                label: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            }[] | undefined;
            primary?: {
                type: "button";
                href: string;
                label: string;
            } | {
                type: "github";
                href: string;
            } | undefined;
        } | undefined;
        footer?: {
            socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
            links?: {
                items: {
                    href: string;
                    label: string;
                }[];
                header?: string | undefined;
            }[] | undefined;
        } | undefined;
        search?: {
            prompt?: string | undefined;
        } | undefined;
        seo?: {
            metatags?: Record<string, string> | undefined;
            indexing?: "all" | "navigable" | undefined;
        } | undefined;
        fonts?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | {
            heading?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
            body?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
        } | undefined;
        icons?: {
            library: "fontawesome" | "lucide";
        } | undefined;
        styling?: {
            eyebrows?: "section" | "breadcrumbs" | undefined;
            codeblocks?: "dark" | "system" | undefined;
        } | undefined;
        redirects?: {
            source: string;
            destination: string;
            permanent?: boolean | undefined;
        }[] | undefined;
        integrations?: {
            amplitude?: {
                apiKey: string;
            } | undefined;
            clearbit?: {
                publicApiKey: string;
            } | undefined;
            fathom?: {
                siteId: string;
            } | undefined;
            frontchat?: {
                snippetId: string;
            } | undefined;
            ga4?: {
                measurementId: string;
            } | undefined;
            gtm?: {
                tagId: string;
            } | undefined;
            heap?: {
                appId: string;
            } | undefined;
            hotjar?: {
                hjid: string;
                hjsv: string;
            } | undefined;
            intercom?: {
                appId: string;
            } | undefined;
            koala?: {
                publicApiKey: string;
            } | undefined;
            logrocket?: {
                appId: string;
            } | undefined;
            mixpanel?: {
                projectToken: string;
            } | undefined;
            osano?: {
                scriptSource: string;
            } | undefined;
            pirsch?: {
                id: string;
            } | undefined;
            posthog?: {
                apiKey: string;
                apiHost?: string | undefined;
            } | undefined;
            plausible?: {
                domain: string;
                server?: string | undefined;
            } | undefined;
            segment?: {
                key: string;
            } | undefined;
            telemetry?: {
                enabled?: boolean | undefined;
            } | undefined;
            cookies?: {
                key?: string | undefined;
                value?: string | undefined;
            } | undefined;
        } | undefined;
        banner?: {
            content: string;
            dismissible?: boolean | undefined;
        } | undefined;
        errors?: {
            '404': {
                redirect: boolean;
            };
        } | undefined;
        contextual?: {
            options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
                href: (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                }) & (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                } | undefined);
                title: string;
                description: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            })[];
        } | undefined;
        thumbnails?: {
            appearance?: import("../index.js").ThumbnailAppearance | undefined;
            background?: string | undefined;
        } | undefined;
    } | {
        name: string;
        $schema: string;
        theme: "maple";
        colors: {
            primary: string;
            light?: string | undefined;
            dark?: string | undefined;
        };
        navigation: ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        }) & ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | undefined);
        description?: string | undefined;
        logo?: string | {
            light: string;
            dark: string;
            href?: string | undefined;
        } | undefined;
        favicon?: string | {
            light: string;
            dark: string;
        } | undefined;
        api?: {
            openapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            asyncapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            params?: {
                expanded?: "all" | "closed" | undefined;
            } | undefined;
            playground?: {
                display?: "simple" | "none" | "interactive" | undefined;
                proxy?: boolean | undefined;
            } | undefined;
            examples?: {
                defaults?: "all" | "required" | undefined;
                languages?: string[] | undefined;
            } | undefined;
            mdx?: {
                auth?: {
                    method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                    name?: string | undefined;
                } | undefined;
                server?: string | string[] | undefined;
            } | undefined;
        } | undefined;
        appearance?: {
            default?: "light" | "dark" | "system" | undefined;
            strict?: boolean | undefined;
        } | undefined;
        background?: {
            image?: string | {
                light: string;
                dark: string;
            } | undefined;
            decoration?: "gradient" | "grid" | "windows" | undefined;
            color?: {
                light?: string | undefined;
                dark?: string | undefined;
            } | undefined;
        } | undefined;
        navbar?: {
            links?: {
                href: string;
                label: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            }[] | undefined;
            primary?: {
                type: "button";
                href: string;
                label: string;
            } | {
                type: "github";
                href: string;
            } | undefined;
        } | undefined;
        footer?: {
            socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
            links?: {
                items: {
                    href: string;
                    label: string;
                }[];
                header?: string | undefined;
            }[] | undefined;
        } | undefined;
        search?: {
            prompt?: string | undefined;
        } | undefined;
        seo?: {
            metatags?: Record<string, string> | undefined;
            indexing?: "all" | "navigable" | undefined;
        } | undefined;
        fonts?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | {
            heading?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
            body?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
        } | undefined;
        icons?: {
            library: "fontawesome" | "lucide";
        } | undefined;
        styling?: {
            eyebrows?: "section" | "breadcrumbs" | undefined;
            codeblocks?: "dark" | "system" | undefined;
        } | undefined;
        redirects?: {
            source: string;
            destination: string;
            permanent?: boolean | undefined;
        }[] | undefined;
        integrations?: {
            amplitude?: {
                apiKey: string;
            } | undefined;
            clearbit?: {
                publicApiKey: string;
            } | undefined;
            fathom?: {
                siteId: string;
            } | undefined;
            frontchat?: {
                snippetId: string;
            } | undefined;
            ga4?: {
                measurementId: string;
            } | undefined;
            gtm?: {
                tagId: string;
            } | undefined;
            heap?: {
                appId: string;
            } | undefined;
            hotjar?: {
                hjid: string;
                hjsv: string;
            } | undefined;
            intercom?: {
                appId: string;
            } | undefined;
            koala?: {
                publicApiKey: string;
            } | undefined;
            logrocket?: {
                appId: string;
            } | undefined;
            mixpanel?: {
                projectToken: string;
            } | undefined;
            osano?: {
                scriptSource: string;
            } | undefined;
            pirsch?: {
                id: string;
            } | undefined;
            posthog?: {
                apiKey: string;
                apiHost?: string | undefined;
            } | undefined;
            plausible?: {
                domain: string;
                server?: string | undefined;
            } | undefined;
            segment?: {
                key: string;
            } | undefined;
            telemetry?: {
                enabled?: boolean | undefined;
            } | undefined;
            cookies?: {
                key?: string | undefined;
                value?: string | undefined;
            } | undefined;
        } | undefined;
        banner?: {
            content: string;
            dismissible?: boolean | undefined;
        } | undefined;
        errors?: {
            '404': {
                redirect: boolean;
            };
        } | undefined;
        contextual?: {
            options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
                href: (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                }) & (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                } | undefined);
                title: string;
                description: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            })[];
        } | undefined;
        thumbnails?: {
            appearance?: import("../index.js").ThumbnailAppearance | undefined;
            background?: string | undefined;
        } | undefined;
    } | {
        name: string;
        $schema: string;
        theme: "mint";
        colors: {
            primary: string;
            light?: string | undefined;
            dark?: string | undefined;
        };
        navigation: ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        }) & ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | undefined);
        description?: string | undefined;
        logo?: string | {
            light: string;
            dark: string;
            href?: string | undefined;
        } | undefined;
        favicon?: string | {
            light: string;
            dark: string;
        } | undefined;
        api?: {
            openapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            asyncapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            params?: {
                expanded?: "all" | "closed" | undefined;
            } | undefined;
            playground?: {
                display?: "simple" | "none" | "interactive" | undefined;
                proxy?: boolean | undefined;
            } | undefined;
            examples?: {
                defaults?: "all" | "required" | undefined;
                languages?: string[] | undefined;
            } | undefined;
            mdx?: {
                auth?: {
                    method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                    name?: string | undefined;
                } | undefined;
                server?: string | string[] | undefined;
            } | undefined;
        } | undefined;
        appearance?: {
            default?: "light" | "dark" | "system" | undefined;
            strict?: boolean | undefined;
        } | undefined;
        background?: {
            image?: string | {
                light: string;
                dark: string;
            } | undefined;
            decoration?: "gradient" | "grid" | "windows" | undefined;
            color?: {
                light?: string | undefined;
                dark?: string | undefined;
            } | undefined;
        } | undefined;
        navbar?: {
            links?: {
                href: string;
                label: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            }[] | undefined;
            primary?: {
                type: "button";
                href: string;
                label: string;
            } | {
                type: "github";
                href: string;
            } | undefined;
        } | undefined;
        footer?: {
            socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
            links?: {
                items: {
                    href: string;
                    label: string;
                }[];
                header?: string | undefined;
            }[] | undefined;
        } | undefined;
        search?: {
            prompt?: string | undefined;
        } | undefined;
        seo?: {
            metatags?: Record<string, string> | undefined;
            indexing?: "all" | "navigable" | undefined;
        } | undefined;
        fonts?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | {
            heading?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
            body?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
        } | undefined;
        icons?: {
            library: "fontawesome" | "lucide";
        } | undefined;
        styling?: {
            eyebrows?: "section" | "breadcrumbs" | undefined;
            codeblocks?: "dark" | "system" | undefined;
        } | undefined;
        redirects?: {
            source: string;
            destination: string;
            permanent?: boolean | undefined;
        }[] | undefined;
        integrations?: {
            amplitude?: {
                apiKey: string;
            } | undefined;
            clearbit?: {
                publicApiKey: string;
            } | undefined;
            fathom?: {
                siteId: string;
            } | undefined;
            frontchat?: {
                snippetId: string;
            } | undefined;
            ga4?: {
                measurementId: string;
            } | undefined;
            gtm?: {
                tagId: string;
            } | undefined;
            heap?: {
                appId: string;
            } | undefined;
            hotjar?: {
                hjid: string;
                hjsv: string;
            } | undefined;
            intercom?: {
                appId: string;
            } | undefined;
            koala?: {
                publicApiKey: string;
            } | undefined;
            logrocket?: {
                appId: string;
            } | undefined;
            mixpanel?: {
                projectToken: string;
            } | undefined;
            osano?: {
                scriptSource: string;
            } | undefined;
            pirsch?: {
                id: string;
            } | undefined;
            posthog?: {
                apiKey: string;
                apiHost?: string | undefined;
            } | undefined;
            plausible?: {
                domain: string;
                server?: string | undefined;
            } | undefined;
            segment?: {
                key: string;
            } | undefined;
            telemetry?: {
                enabled?: boolean | undefined;
            } | undefined;
            cookies?: {
                key?: string | undefined;
                value?: string | undefined;
            } | undefined;
        } | undefined;
        banner?: {
            content: string;
            dismissible?: boolean | undefined;
        } | undefined;
        errors?: {
            '404': {
                redirect: boolean;
            };
        } | undefined;
        contextual?: {
            options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
                href: (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                }) & (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                } | undefined);
                title: string;
                description: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            })[];
        } | undefined;
        thumbnails?: {
            appearance?: import("../index.js").ThumbnailAppearance | undefined;
            background?: string | undefined;
        } | undefined;
    } | {
        name: string;
        $schema: string;
        theme: "palm";
        colors: {
            primary: string;
            light?: string | undefined;
            dark?: string | undefined;
        };
        navigation: ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        }) & ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | undefined);
        description?: string | undefined;
        logo?: string | {
            light: string;
            dark: string;
            href?: string | undefined;
        } | undefined;
        favicon?: string | {
            light: string;
            dark: string;
        } | undefined;
        api?: {
            openapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            asyncapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            params?: {
                expanded?: "all" | "closed" | undefined;
            } | undefined;
            playground?: {
                display?: "simple" | "none" | "interactive" | undefined;
                proxy?: boolean | undefined;
            } | undefined;
            examples?: {
                defaults?: "all" | "required" | undefined;
                languages?: string[] | undefined;
            } | undefined;
            mdx?: {
                auth?: {
                    method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                    name?: string | undefined;
                } | undefined;
                server?: string | string[] | undefined;
            } | undefined;
        } | undefined;
        appearance?: {
            default?: "light" | "dark" | "system" | undefined;
            strict?: boolean | undefined;
        } | undefined;
        background?: {
            image?: string | {
                light: string;
                dark: string;
            } | undefined;
            decoration?: "gradient" | "grid" | "windows" | undefined;
            color?: {
                light?: string | undefined;
                dark?: string | undefined;
            } | undefined;
        } | undefined;
        navbar?: {
            links?: {
                href: string;
                label: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            }[] | undefined;
            primary?: {
                type: "button";
                href: string;
                label: string;
            } | {
                type: "github";
                href: string;
            } | undefined;
        } | undefined;
        footer?: {
            socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
            links?: {
                items: {
                    href: string;
                    label: string;
                }[];
                header?: string | undefined;
            }[] | undefined;
        } | undefined;
        search?: {
            prompt?: string | undefined;
        } | undefined;
        seo?: {
            metatags?: Record<string, string> | undefined;
            indexing?: "all" | "navigable" | undefined;
        } | undefined;
        fonts?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | {
            heading?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
            body?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
        } | undefined;
        icons?: {
            library: "fontawesome" | "lucide";
        } | undefined;
        styling?: {
            eyebrows?: "section" | "breadcrumbs" | undefined;
            codeblocks?: "dark" | "system" | undefined;
        } | undefined;
        redirects?: {
            source: string;
            destination: string;
            permanent?: boolean | undefined;
        }[] | undefined;
        integrations?: {
            amplitude?: {
                apiKey: string;
            } | undefined;
            clearbit?: {
                publicApiKey: string;
            } | undefined;
            fathom?: {
                siteId: string;
            } | undefined;
            frontchat?: {
                snippetId: string;
            } | undefined;
            ga4?: {
                measurementId: string;
            } | undefined;
            gtm?: {
                tagId: string;
            } | undefined;
            heap?: {
                appId: string;
            } | undefined;
            hotjar?: {
                hjid: string;
                hjsv: string;
            } | undefined;
            intercom?: {
                appId: string;
            } | undefined;
            koala?: {
                publicApiKey: string;
            } | undefined;
            logrocket?: {
                appId: string;
            } | undefined;
            mixpanel?: {
                projectToken: string;
            } | undefined;
            osano?: {
                scriptSource: string;
            } | undefined;
            pirsch?: {
                id: string;
            } | undefined;
            posthog?: {
                apiKey: string;
                apiHost?: string | undefined;
            } | undefined;
            plausible?: {
                domain: string;
                server?: string | undefined;
            } | undefined;
            segment?: {
                key: string;
            } | undefined;
            telemetry?: {
                enabled?: boolean | undefined;
            } | undefined;
            cookies?: {
                key?: string | undefined;
                value?: string | undefined;
            } | undefined;
        } | undefined;
        banner?: {
            content: string;
            dismissible?: boolean | undefined;
        } | undefined;
        errors?: {
            '404': {
                redirect: boolean;
            };
        } | undefined;
        contextual?: {
            options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
                href: (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                }) & (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                } | undefined);
                title: string;
                description: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            })[];
        } | undefined;
        thumbnails?: {
            appearance?: import("../index.js").ThumbnailAppearance | undefined;
            background?: string | undefined;
        } | undefined;
    } | {
        name: string;
        $schema: string;
        theme: "willow";
        colors: {
            primary: string;
            light?: string | undefined;
            dark?: string | undefined;
        };
        navigation: ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        }) & ({
            languages: import("./schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            versions: import("./schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            tabs: import("./schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            dropdowns: import("./schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            anchors: import("./schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            groups: ({
                openapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                group: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                asyncapi: (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                }) & (string | string[] | {
                    source: string;
                    directory?: string | undefined;
                } | undefined);
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            } | {
                group: string;
                pages: any[];
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
                tag?: string | undefined;
                hidden?: boolean | undefined;
                root?: string | undefined;
            })[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | {
            pages: any[];
            global?: import("./schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
        } | undefined);
        description?: string | undefined;
        logo?: string | {
            light: string;
            dark: string;
            href?: string | undefined;
        } | undefined;
        favicon?: string | {
            light: string;
            dark: string;
        } | undefined;
        api?: {
            openapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            asyncapi?: string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined;
            params?: {
                expanded?: "all" | "closed" | undefined;
            } | undefined;
            playground?: {
                display?: "simple" | "none" | "interactive" | undefined;
                proxy?: boolean | undefined;
            } | undefined;
            examples?: {
                defaults?: "all" | "required" | undefined;
                languages?: string[] | undefined;
            } | undefined;
            mdx?: {
                auth?: {
                    method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                    name?: string | undefined;
                } | undefined;
                server?: string | string[] | undefined;
            } | undefined;
        } | undefined;
        appearance?: {
            default?: "light" | "dark" | "system" | undefined;
            strict?: boolean | undefined;
        } | undefined;
        background?: {
            image?: string | {
                light: string;
                dark: string;
            } | undefined;
            decoration?: "gradient" | "grid" | "windows" | undefined;
            color?: {
                light?: string | undefined;
                dark?: string | undefined;
            } | undefined;
        } | undefined;
        navbar?: {
            links?: {
                href: string;
                label: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            }[] | undefined;
            primary?: {
                type: "button";
                href: string;
                label: string;
            } | {
                type: "github";
                href: string;
            } | undefined;
        } | undefined;
        footer?: {
            socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
            links?: {
                items: {
                    href: string;
                    label: string;
                }[];
                header?: string | undefined;
            }[] | undefined;
        } | undefined;
        search?: {
            prompt?: string | undefined;
        } | undefined;
        seo?: {
            metatags?: Record<string, string> | undefined;
            indexing?: "all" | "navigable" | undefined;
        } | undefined;
        fonts?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | {
            heading?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
            body?: {
                family: string;
                weight?: number | undefined;
                source?: string | undefined;
                format?: "woff" | "woff2" | undefined;
            } | undefined;
        } | undefined;
        icons?: {
            library: "fontawesome" | "lucide";
        } | undefined;
        styling?: {
            eyebrows?: "section" | "breadcrumbs" | undefined;
            codeblocks?: "dark" | "system" | undefined;
        } | undefined;
        redirects?: {
            source: string;
            destination: string;
            permanent?: boolean | undefined;
        }[] | undefined;
        integrations?: {
            amplitude?: {
                apiKey: string;
            } | undefined;
            clearbit?: {
                publicApiKey: string;
            } | undefined;
            fathom?: {
                siteId: string;
            } | undefined;
            frontchat?: {
                snippetId: string;
            } | undefined;
            ga4?: {
                measurementId: string;
            } | undefined;
            gtm?: {
                tagId: string;
            } | undefined;
            heap?: {
                appId: string;
            } | undefined;
            hotjar?: {
                hjid: string;
                hjsv: string;
            } | undefined;
            intercom?: {
                appId: string;
            } | undefined;
            koala?: {
                publicApiKey: string;
            } | undefined;
            logrocket?: {
                appId: string;
            } | undefined;
            mixpanel?: {
                projectToken: string;
            } | undefined;
            osano?: {
                scriptSource: string;
            } | undefined;
            pirsch?: {
                id: string;
            } | undefined;
            posthog?: {
                apiKey: string;
                apiHost?: string | undefined;
            } | undefined;
            plausible?: {
                domain: string;
                server?: string | undefined;
            } | undefined;
            segment?: {
                key: string;
            } | undefined;
            telemetry?: {
                enabled?: boolean | undefined;
            } | undefined;
            cookies?: {
                key?: string | undefined;
                value?: string | undefined;
            } | undefined;
        } | undefined;
        banner?: {
            content: string;
            dismissible?: boolean | undefined;
        } | undefined;
        errors?: {
            '404': {
                redirect: boolean;
            };
        } | undefined;
        contextual?: {
            options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
                href: (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                }) & (string | {
                    base: string;
                    query?: {
                        value: string;
                        key: string;
                    }[] | undefined;
                } | undefined);
                title: string;
                description: string;
                icon?: string | {
                    name: string;
                    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                    library?: "fontawesome" | "lucide" | undefined;
                } | undefined;
            })[];
        } | undefined;
        thumbnails?: {
            appearance?: import("../index.js").ThumbnailAppearance | undefined;
            background?: string | undefined;
        } | undefined;
    };
};
