import { z } from 'zod';
import { appearanceSchema } from '../mint-config/schemas/v2/properties/appearance.js';
import { colorsSchema } from '../mint-config/schemas/v2/properties/colors.js';
import { faviconSchema } from '../mint-config/schemas/v2/properties/favicon.js';
import { fontsSchema } from '../mint-config/schemas/v2/properties/font.js';
import { logoSchema } from '../mint-config/schemas/v2/properties/logo.js';
import { nameSchema } from '../mint-config/schemas/v2/properties/name.js';
import { footerSchema } from './footer.js';
import { heroSchema } from './hero.js';
export const chatConfigSchema = z.object({
    name: nameSchema.optional(),
    favicon: faviconSchema.optional(),
    logo: logoSchema.optional(),
    hero: heroSchema.optional(),
    colors: colorsSchema.optional().transform((arg) => {
        if (arg === undefined) {
            return {
                primary: '#16A34A',
                light: '#4ADE80',
                dark: '#166534',
            };
        }
        else if (arg.dark === undefined && arg.light !== undefined) {
            arg.dark = arg.light;
        }
        else if (arg.light === undefined && arg.dark !== undefined) {
            arg.light = arg.dark;
        }
        else if (arg.dark === undefined && arg.light === undefined) {
            arg.dark = '#166534';
            arg.light = '#4ADE80';
        }
        return arg;
    }),
    fonts: fontsSchema.optional(),
    appearance: appearanceSchema.optional(),
    footer: footerSchema.optional(),
});
export { heroSchema };
