import { z } from 'zod';
import { normalizeRelativePath } from '../../../../transforms/normalizeRelativePath.js';
import { iconSchema } from './icon.js';
export const pageSchema = z
    .string()
    .nonempty()
    .transform(normalizeRelativePath)
    .describe('A page in the navigation. Referenced by the path to the page. Example: path/to/page');
export const decoratedPageSchema = z
    .object({
    href: z.string(),
    title: z.string(),
    sidebarTitle: z.string().optional(),
    description: z.string().optional(),
    api: z.string().optional(),
    openapi: z.string().optional(),
    asyncapi: z.string().optional(),
    contentType: z.string().optional(),
    authMethod: z.string().optional(),
    auth: z.string().optional(),
    version: z.string().optional(),
    mode: z.string().optional(),
    hideFooterPagination: z.boolean().optional(),
    authors: z.unknown().optional(),
    lastUpdatedDate: z.string().optional(),
    createdDate: z.string().optional(),
    'openapi-schema': z.string().optional(),
    icon: iconSchema.optional(),
    tag: z.string().optional(),
    url: z.string().optional(),
    hideApiMarker: z.boolean().optional(),
    noindex: z.boolean().optional(),
    isPublic: z.boolean().optional(),
    public: z.boolean().optional(),
    deprecated: z.boolean().optional(),
})
    .describe('page metadata tags in the navigation');
