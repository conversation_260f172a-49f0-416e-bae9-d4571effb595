export { AsyncAPIDocument as AsyncAPIDocumentV3 } from './asyncapi';
export { Binding as BindingV3 } from './binding';
export { Bindings as BindingsV3 } from './bindings';
export { ChannelParameter as ChannelParameterV3 } from './channel-parameter';
export { ChannelParameters as ChannelParametersV3 } from './channel-parameters';
export { Channel as ChannelV3 } from './channel';
export { Channels as ChannelsV3 } from './channels';
export { Components as ComponentsV3 } from './components';
export { Contact as ContactV3 } from './contact';
export { CorrelationId as CorrelationIdV3 } from './correlation-id';
export { Extension as ExtensionV3 } from './extension';
export { Extensions as ExtensionsV3 } from './extensions';
export { ExternalDocumentation as ExternalDocumentationV3 } from './external-docs';
export { Info as InfoV3 } from './info';
export { License as LicenseV3 } from './license';
export { MessageExample as MessageExampleV3 } from './message-example';
export { MessageExamples as MessageExamplesV3 } from './message-examples';
export { MessageTrait as MessageTraitV3 } from './message-trait';
export { MessageTraits as MessageTraitsV3 } from './message-traits';
export { Message as MessageV3 } from './message';
export { Messages as MessagesV3 } from './messages';
export { OAuthFlow as OAuthFlowV3 } from './oauth-flow';
export { OAuthFlows as OAuthFlowsV3 } from './oauth-flows';
export { OperationTrait as OperationTraitV3 } from './operation-trait';
export { OperationTraits as OperationTraitsV3 } from './operation-traits';
export { OperationReplies as OperationRepliesV3 } from './operation-replies';
export { OperationReplyAddress as OperationReplyAddressV3 } from './operation-reply-address';
export { OperationReplyAddresses as OperationReplyAddressesV3 } from './operation-reply-addresses';
export { OperationReply as OperationReplyV3 } from './operation-reply';
export { Operation as OperationV3 } from './operation';
export { Operations as OperationsV3 } from './operations';
export { Schema as SchemaV3 } from './schema';
export { Schemas as SchemasV3 } from './schemas';
export { SecurityScheme as SecuritySchemeV3 } from './security-scheme';
export { SecuritySchemes as SecuritySchemesV3 } from './security-schemes';
export { ServerVariable as ServerVariableV3 } from './server-variable';
export { ServerVariables as ServerVariablesV3 } from './server-variables';
export { Server as ServerV3 } from './server';
export { Servers as ServersV3 } from './servers';
export { Tag as TagV3 } from './tag';
export { Tags as TagsV3 } from './tags';
