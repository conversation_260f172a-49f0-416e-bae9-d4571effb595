import { z } from 'zod';
export declare const mintConfigSchema: z.ZodObject<{
    $schema: z.Zod<PERSON>efault<z.ZodOptional<z.ZodString>>;
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    colors: z.ZodObject<{
        primary: z.ZodString;
        light: z.ZodOptional<z.ZodString>;
        dark: z.Zod<PERSON>ptional<z.ZodString>;
    }, "strict", z.ZodType<PERSON>ny, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    }>;
    logo: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
        light: z.ZodString;
        dark: z.ZodString;
        href: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        light: string;
        dark: string;
        href?: string | undefined;
    }, {
        light: string;
        dark: string;
        href?: string | undefined;
    }>]>>;
    favicon: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
        light: z.ZodString;
        dark: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        light: string;
        dark: string;
    }, {
        light: string;
        dark: string;
    }>]>>;
    api: z.ZodOptional<z.ZodObject<{
        openapi: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
            source: z.ZodEffects<z.ZodString, string, string>;
            directory: z.ZodOptional<z.ZodString>;
        }, "strict", z.ZodTypeAny, {
            source: string;
            directory?: string | undefined;
        }, {
            source: string;
            directory?: string | undefined;
        }>]>>;
        asyncapi: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
            source: z.ZodEffects<z.ZodString, string, string>;
            directory: z.ZodOptional<z.ZodString>;
        }, "strict", z.ZodTypeAny, {
            source: string;
            directory?: string | undefined;
        }, {
            source: string;
            directory?: string | undefined;
        }>]>>;
        params: z.ZodOptional<z.ZodObject<{
            expanded: z.ZodOptional<z.ZodEnum<["all", "closed"]>>;
        }, "strip", z.ZodTypeAny, {
            expanded?: "all" | "closed" | undefined;
        }, {
            expanded?: "all" | "closed" | undefined;
        }>>;
        playground: z.ZodOptional<z.ZodObject<{
            display: z.ZodOptional<z.ZodEnum<["interactive", "simple", "none"]>>;
            proxy: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        }, {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        }>>;
        examples: z.ZodOptional<z.ZodObject<{
            defaults: z.ZodOptional<z.ZodEnum<["required", "all"]>>;
            languages: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        }, "strip", z.ZodTypeAny, {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        }, {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        }>>;
        mdx: z.ZodOptional<z.ZodObject<{
            auth: z.ZodOptional<z.ZodObject<{
                method: z.ZodOptional<z.ZodEnum<["bearer", "basic", "key", "cobo"]>>;
                name: z.ZodOptional<z.ZodString>;
            }, "strip", z.ZodTypeAny, {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            }, {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            }>>;
            server: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodArray<z.ZodString, "many">]>>;
        }, "strip", z.ZodTypeAny, {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        }, {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    }, {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    }>>;
    appearance: z.ZodOptional<z.ZodObject<{
        default: z.ZodOptional<z.ZodEnum<["system", "light", "dark"]>>;
        strict: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    }, {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    }>>;
    background: z.ZodOptional<z.ZodObject<{
        image: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
            light: z.ZodString;
            dark: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            light: string;
            dark: string;
        }, {
            light: string;
            dark: string;
        }>]>>;
        decoration: z.ZodOptional<z.ZodEnum<["gradient", "grid", "windows"]>>;
        color: z.ZodOptional<z.ZodObject<{
            light: z.ZodOptional<z.ZodString>;
            dark: z.ZodOptional<z.ZodString>;
        }, "strict", z.ZodTypeAny, {
            light?: string | undefined;
            dark?: string | undefined;
        }, {
            light?: string | undefined;
            dark?: string | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    }, {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    }>>;
    navbar: z.ZodOptional<z.ZodObject<{
        links: z.ZodOptional<z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
                style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: z.ZodEffects<z.ZodString, string, string>;
                library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            href: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }, {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }>, "many">>;
        primary: z.ZodOptional<z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
            type: z.ZodLiteral<"button">;
            label: z.ZodString;
            href: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            type: "button";
            href: string;
            label: string;
        }, {
            type: "button";
            href: string;
            label: string;
        }>, z.ZodObject<{
            type: z.ZodLiteral<"github">;
            href: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            type: "github";
            href: string;
        }, {
            type: "github";
            href: string;
        }>]>>;
    }, "strip", z.ZodTypeAny, {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    }, {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    }>>;
    navigation: z.ZodUnion<[z.ZodObject<{
        global: z.ZodOptional<z.ZodType<import("../properties/navigation/divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").GlobalNavigation>>;
        languages: z.ZodArray<z.ZodType<import("../properties/navigation/divisionNav.js").LanguageNavigation<"default">, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").LanguageNavigation<"default">>, "many">;
    }, "strip", z.ZodTypeAny, {
        languages: import("../properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        languages: import("../properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, z.ZodObject<{
        global: z.ZodOptional<z.ZodType<import("../properties/navigation/divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").GlobalNavigation>>;
        versions: z.ZodArray<z.ZodType<import("../properties/navigation/divisionNav.js").VersionNavigation<"default">, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").VersionNavigation<"default">>, "many">;
    }, "strip", z.ZodTypeAny, {
        versions: import("../properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        versions: import("../properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, z.ZodObject<{
        global: z.ZodOptional<z.ZodType<import("../properties/navigation/divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").GlobalNavigation>>;
        tabs: z.ZodArray<z.ZodType<import("../properties/navigation/divisionNav.js").TabNavigation<"default">, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").TabNavigation<"default">>, "many">;
    }, "strip", z.ZodTypeAny, {
        tabs: import("../properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        tabs: import("../properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, z.ZodObject<{
        global: z.ZodOptional<z.ZodType<import("../properties/navigation/divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").GlobalNavigation>>;
        dropdowns: z.ZodArray<z.ZodType<import("../properties/navigation/divisionNav.js").DropdownNavigation<"default">, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").DropdownNavigation<"default">>, "many">;
    }, "strip", z.ZodTypeAny, {
        dropdowns: import("../properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        dropdowns: import("../properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, z.ZodObject<{
        global: z.ZodOptional<z.ZodType<import("../properties/navigation/divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").GlobalNavigation>>;
        anchors: z.ZodArray<z.ZodType<import("../properties/navigation/divisionNav.js").AnchorNavigation<"default">, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").AnchorNavigation<"default">>, "many">;
    }, "strip", z.ZodTypeAny, {
        anchors: import("../properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        anchors: import("../properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, z.ZodObject<{
        global: z.ZodOptional<z.ZodType<import("../properties/navigation/divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").GlobalNavigation>>;
        groups: z.ZodArray<z.ZodUnion<[z.ZodObject<{
            icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
                style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: z.ZodEffects<z.ZodString, string, string>;
                library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            group: z.ZodString;
            tag: z.ZodOptional<z.ZodString>;
            hidden: z.ZodOptional<z.ZodBoolean>;
            root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
            openapi: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
                source: z.ZodEffects<z.ZodString, string, string>;
                directory: z.ZodOptional<z.ZodString>;
            }, "strict", z.ZodTypeAny, {
                source: string;
                directory?: string | undefined;
            }, {
                source: string;
                directory?: string | undefined;
            }>]>;
        }, "strip", z.ZodTypeAny, {
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }, {
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }>, z.ZodObject<{
            icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
                style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: z.ZodEffects<z.ZodString, string, string>;
                library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            group: z.ZodString;
            tag: z.ZodOptional<z.ZodString>;
            hidden: z.ZodOptional<z.ZodBoolean>;
            root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
            asyncapi: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
                source: z.ZodEffects<z.ZodString, string, string>;
                directory: z.ZodOptional<z.ZodString>;
            }, "strict", z.ZodTypeAny, {
                source: string;
                directory?: string | undefined;
            }, {
                source: string;
                directory?: string | undefined;
            }>]>;
        }, "strip", z.ZodTypeAny, {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }, {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }>, z.ZodObject<{
            icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
                style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: z.ZodEffects<z.ZodString, string, string>;
                library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            group: z.ZodString;
            tag: z.ZodOptional<z.ZodString>;
            hidden: z.ZodOptional<z.ZodBoolean>;
            root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
            pages: z.ZodLazy<z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">>;
        }, "strip", z.ZodTypeAny, {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }, {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }>]>, "many">;
    }, "strip", z.ZodTypeAny, {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, z.ZodObject<{
        global: z.ZodOptional<z.ZodType<import("../properties/navigation/divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("../properties/navigation/divisionNav.js").GlobalNavigation>>;
        pages: z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">;
    }, "strip", z.ZodTypeAny, {
        pages: any[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        pages: any[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>]>;
    footer: z.ZodOptional<z.ZodObject<{
        socials: z.ZodOptional<z.ZodRecord<z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>, z.ZodString>>;
        links: z.ZodOptional<z.ZodArray<z.ZodObject<{
            header: z.ZodOptional<z.ZodString>;
            items: z.ZodArray<z.ZodObject<{
                label: z.ZodString;
                href: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                href: string;
                label: string;
            }, {
                href: string;
                label: string;
            }>, "many">;
        }, "strip", z.ZodTypeAny, {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }, {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    }, {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    }>>;
    search: z.ZodOptional<z.ZodObject<{
        prompt: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        prompt?: string | undefined;
    }, {
        prompt?: string | undefined;
    }>>;
    seo: z.ZodOptional<z.ZodObject<{
        metatags: z.ZodOptional<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>>;
        indexing: z.ZodOptional<z.ZodEnum<["navigable", "all"]>>;
    }, "strip", z.ZodTypeAny, {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    }, {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    }>>;
    fonts: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodObject<{
        family: z.ZodString;
        weight: z.ZodOptional<z.ZodNumber>;
        source: z.ZodOptional<z.ZodString>;
        format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
    }, "strip", z.ZodTypeAny, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, z.ZodObject<{
        heading: z.ZodOptional<z.ZodEffects<z.ZodObject<{
            family: z.ZodString;
            weight: z.ZodOptional<z.ZodNumber>;
            source: z.ZodOptional<z.ZodString>;
            format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
        }, "strip", z.ZodTypeAny, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>>;
        body: z.ZodOptional<z.ZodEffects<z.ZodObject<{
            family: z.ZodString;
            weight: z.ZodOptional<z.ZodNumber>;
            source: z.ZodOptional<z.ZodString>;
            format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
        }, "strip", z.ZodTypeAny, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>>;
    }, "strict", z.ZodTypeAny, {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    }, {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    }>]>>;
    icons: z.ZodOptional<z.ZodObject<{
        library: z.ZodEnum<["fontawesome", "lucide"]>;
    }, "strip", z.ZodTypeAny, {
        library: "fontawesome" | "lucide";
    }, {
        library: "fontawesome" | "lucide";
    }>>;
    styling: z.ZodOptional<z.ZodObject<{
        eyebrows: z.ZodOptional<z.ZodEnum<["section", "breadcrumbs"]>>;
        codeblocks: z.ZodOptional<z.ZodEnum<["system", "dark"]>>;
    }, "strip", z.ZodTypeAny, {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    }, {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    }>>;
    redirects: z.ZodOptional<z.ZodEffects<z.ZodArray<z.ZodObject<{
        source: z.ZodString;
        destination: z.ZodString;
        permanent: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }>, "many">, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[], {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[]>>;
    integrations: z.ZodOptional<z.ZodObject<{
        amplitude: z.ZodOptional<z.ZodObject<{
            apiKey: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            apiKey: string;
        }, {
            apiKey: string;
        }>>;
        clearbit: z.ZodOptional<z.ZodObject<{
            publicApiKey: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            publicApiKey: string;
        }, {
            publicApiKey: string;
        }>>;
        fathom: z.ZodOptional<z.ZodObject<{
            siteId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            siteId: string;
        }, {
            siteId: string;
        }>>;
        frontchat: z.ZodOptional<z.ZodObject<{
            snippetId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            snippetId: string;
        }, {
            snippetId: string;
        }>>;
        ga4: z.ZodOptional<z.ZodObject<{
            measurementId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            measurementId: string;
        }, {
            measurementId: string;
        }>>;
        gtm: z.ZodOptional<z.ZodObject<{
            tagId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            tagId: string;
        }, {
            tagId: string;
        }>>;
        heap: z.ZodOptional<z.ZodObject<{
            appId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            appId: string;
        }, {
            appId: string;
        }>>;
        hotjar: z.ZodOptional<z.ZodObject<{
            hjid: z.ZodString;
            hjsv: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            hjid: string;
            hjsv: string;
        }, {
            hjid: string;
            hjsv: string;
        }>>;
        intercom: z.ZodOptional<z.ZodObject<{
            appId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            appId: string;
        }, {
            appId: string;
        }>>;
        koala: z.ZodOptional<z.ZodObject<{
            publicApiKey: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            publicApiKey: string;
        }, {
            publicApiKey: string;
        }>>;
        logrocket: z.ZodOptional<z.ZodObject<{
            appId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            appId: string;
        }, {
            appId: string;
        }>>;
        mixpanel: z.ZodOptional<z.ZodObject<{
            projectToken: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            projectToken: string;
        }, {
            projectToken: string;
        }>>;
        osano: z.ZodOptional<z.ZodObject<{
            scriptSource: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            scriptSource: string;
        }, {
            scriptSource: string;
        }>>;
        pirsch: z.ZodOptional<z.ZodObject<{
            id: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            id: string;
        }, {
            id: string;
        }>>;
        posthog: z.ZodOptional<z.ZodObject<{
            apiKey: z.ZodString;
            apiHost: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            apiKey: string;
            apiHost?: string | undefined;
        }, {
            apiKey: string;
            apiHost?: string | undefined;
        }>>;
        plausible: z.ZodOptional<z.ZodObject<{
            domain: z.ZodEffects<z.ZodString, string, string>;
            server: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        }, "strip", z.ZodTypeAny, {
            domain: string;
            server?: string | undefined;
        }, {
            domain: string;
            server?: string | undefined;
        }>>;
        segment: z.ZodOptional<z.ZodObject<{
            key: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            key: string;
        }, {
            key: string;
        }>>;
        telemetry: z.ZodOptional<z.ZodObject<{
            enabled: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            enabled?: boolean | undefined;
        }, {
            enabled?: boolean | undefined;
        }>>;
        cookies: z.ZodOptional<z.ZodObject<{
            key: z.ZodOptional<z.ZodString>;
            value: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            key?: string | undefined;
            value?: string | undefined;
        }, {
            key?: string | undefined;
            value?: string | undefined;
        }>>;
    }, "strict", z.ZodTypeAny, {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    }, {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    }>>;
    banner: z.ZodOptional<z.ZodObject<{
        content: z.ZodString;
        dismissible: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        content: string;
        dismissible?: boolean | undefined;
    }, {
        content: string;
        dismissible?: boolean | undefined;
    }>>;
    errors: z.ZodOptional<z.ZodObject<{
        '404': z.ZodObject<{
            redirect: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
        }, "strip", z.ZodTypeAny, {
            redirect: boolean;
        }, {
            redirect?: boolean | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        '404': {
            redirect: boolean;
        };
    }, {
        '404': {
            redirect?: boolean | undefined;
        };
    }>>;
    contextual: z.ZodOptional<z.ZodObject<{
        options: z.ZodEffects<z.ZodArray<z.ZodUnion<[z.ZodEnum<["copy", "view", "chatgpt", "claude", "perplexity"]>, z.ZodObject<{
            title: z.ZodString;
            description: z.ZodString;
            icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
                style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: z.ZodEffects<z.ZodString, string, string>;
                library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            href: z.ZodUnion<[z.ZodString, z.ZodObject<{
                base: z.ZodString;
                query: z.ZodOptional<z.ZodArray<z.ZodObject<{
                    key: z.ZodString;
                    value: z.ZodString;
                }, "strip", z.ZodTypeAny, {
                    value: string;
                    key: string;
                }, {
                    value: string;
                    key: string;
                }>, "many">>;
            }, "strip", z.ZodTypeAny, {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }, {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }>]>;
        }, "strip", z.ZodTypeAny, {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }, {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }>]>, "many">, ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[], ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[]>;
    }, "strip", z.ZodTypeAny, {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    }, {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    }>>;
    thumbnails: z.ZodOptional<z.ZodObject<{
        appearance: z.ZodOptional<z.ZodEnum<[import("../properties/thumbnails.js").ThumbnailAppearance.Light, import("../properties/thumbnails.js").ThumbnailAppearance.Dark]>>;
        background: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        appearance?: import("../properties/thumbnails.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    }, {
        appearance?: import("../properties/thumbnails.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    }>>;
    theme: z.ZodLiteral<"mint">;
}, "strip", z.ZodTypeAny, {
    name: string;
    $schema: string;
    theme: "mint";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("../properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("../properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("../properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("../properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("../properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("../properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("../properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("../properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("../properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("../properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect: boolean;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../properties/thumbnails.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
}, {
    name: string;
    theme: "mint";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("../properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("../properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("../properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("../properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("../properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("../properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("../properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("../properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("../properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("../properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    $schema?: string | undefined;
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        '404': {
            redirect?: boolean | undefined;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("../properties/thumbnails.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
}>;
