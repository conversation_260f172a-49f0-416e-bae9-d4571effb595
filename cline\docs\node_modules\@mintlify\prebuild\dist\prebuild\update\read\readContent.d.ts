import type { FileWithImports } from '@mintlify/common';
import { AsyncAPIFile } from '@mintlify/common';
import type { OpenApiFile, DecoratedNavigationPage } from '@mintlify/models';
import { Root } from 'mdast';
type ReadPageContentsArgs = {
    contentDirectoryPath: string;
    openApiFiles: OpenApiFile[];
    asyncApiFiles: AsyncAPIFile[];
    contentFilenames: string[];
    pagesAcc: Record<string, DecoratedNavigationPage>;
};
export declare const readPageContents: ({ contentDirectoryPath, openApiFiles, asyncApiFiles, contentFilenames, pagesAcc, }: ReadPageContentsArgs) => Promise<{
    mdxFilesWithNoImports: {
        targetPath: string;
        tree: Root;
    }[];
    pagesAcc: Record<string, DecoratedNavigationPage>;
    filesWithImports: FileWithImports[];
}>;
export declare const readSnippetsV2Contents: (contentDirectoryPath: string, snippetV2Filenames: string[]) => Promise<{
    filename: string;
    tree: Root;
}[]>;
export {};
