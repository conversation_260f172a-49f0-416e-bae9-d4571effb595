{"name": "@mintlify/validation", "version": "0.1.416", "description": "Validates mint.json files", "author": "Mintlify, Inc.", "bugs": {"url": "https://github.com/mintlify/docs/issues"}, "license": "Elastic-2.0", "keywords": ["mintlify", "mint", "validation"], "type": "module", "sideEffects": false, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"prepare": "npm run build", "build": "tsc --project tsconfig.build.json", "clean:build": "<PERSON><PERSON><PERSON> dist", "clean:all": "rimraf node_modules .eslintcache && yarn clean:build", "test": "vitest run", "type": "tsc --noEmit", "lint": "eslint . --cache", "format": "prettier . --write", "format:check": "prettier . --check"}, "dependencies": {"@mintlify/models": "0.0.207", "lcm": "^0.0.3", "lodash": "^4.17.21", "openapi-types": "^12.0.0", "zod": "^3.20.6", "zod-to-json-schema": "^3.20.3"}, "devDependencies": {"@mintlify/eslint-config-typescript": "1.0.13", "@mintlify/prettier-config": "1.0.4", "@mintlify/ts-config": "2.0.2", "@trivago/prettier-plugin-sort-imports": "^4.2.1", "@tsconfig/recommended": "1.x", "@types/lcm": "^0.0.0", "@typescript-eslint/eslint-plugin": "6.x", "@typescript-eslint/parser": "6.x", "eslint": "8.x", "openapi-types": "^12.0.0", "prettier": "^3.1.1", "rimraf": "^5.0.1", "ts-expect": "^1.3.0", "typescript": "^5.5.3", "vitest": "^2.0.4"}, "gitHead": "d5cf4d01c9f3ca7b9747165bb247f3aa958d124f"}