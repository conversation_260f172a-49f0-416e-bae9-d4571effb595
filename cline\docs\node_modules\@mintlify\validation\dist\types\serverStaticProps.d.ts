import type { StaticPropsOrgType, CustomerPageType, SnippetType, CssFileType, JsFileType, OpenApiMetadata, DecoratedNavigation } from '@mintlify/models';
import { DecoratedNavigationConfig } from '../mint-config/schemas/v2/properties/index.js';
import { StaticPropsDeploymentType } from './deployment/index.js';
export type ServerStaticProps = {
    deployment: StaticPropsDeploymentType;
    navigation: DecoratedNavigation;
    docsDecoratedNav?: DecoratedNavigationConfig;
} | {
    deployment: StaticPropsDeploymentType;
    navigation: DecoratedNavigation;
    docsDecoratedNav?: DecoratedNavigationConfig;
    page: CustomerPageType;
    snippets: SnippetType[];
    shouldIndex: boolean;
    cssFiles: CssFileType[];
    jsFiles: JsFileType[];
    org: StaticPropsOrgType;
    openApiUrls: {
        url: string;
        metadata: OpenApiMetadata;
    }[];
    asyncApiUrls?: {
        url: string;
        metadata: OpenApiMetadata;
    }[];
    buildId?: string;
};
export type StaticPageProps = {
    page: CustomerPageType;
    shouldIndex: boolean;
};
export type StaticMetadataProps = {
    deployment: StaticPropsDeploymentType;
    org: StaticPropsOrgType;
    buildId?: string;
};
export type StaticNavProps = {
    navigation: DecoratedNavigation;
    docsDecoratedNav?: DecoratedNavigationConfig;
};
export type StaticCssAndJsFilesProps = {
    cssFiles: CssFileType[];
    jsFiles: JsFileType[];
};
export type StaticSnippetsProps = {
    snippets: SnippetType[];
};
export type StaticOpenApiUrlsProps = {
    openApiUrls: {
        url: string;
        metadata: OpenApiMetadata;
    }[];
};
