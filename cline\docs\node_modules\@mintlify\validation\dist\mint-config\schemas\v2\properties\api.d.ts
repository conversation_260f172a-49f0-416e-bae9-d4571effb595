import { z } from 'zod';
export declare const apiSchema: z.ZodObject<{
    openapi: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
        source: z.ZodEffects<z.ZodString, string, string>;
        directory: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        source: string;
        directory?: string | undefined;
    }, {
        source: string;
        directory?: string | undefined;
    }>]>>;
    asyncapi: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
        source: z.ZodEffects<z.ZodString, string, string>;
        directory: z.ZodOptional<z.ZodString>;
    }, "strict", z.<PERSON>odTypeAny, {
        source: string;
        directory?: string | undefined;
    }, {
        source: string;
        directory?: string | undefined;
    }>]>>;
    params: z.ZodOptional<z.ZodObject<{
        expanded: z.ZodOptional<z.ZodEnum<["all", "closed"]>>;
    }, "strip", z.ZodTypeAny, {
        expanded?: "all" | "closed" | undefined;
    }, {
        expanded?: "all" | "closed" | undefined;
    }>>;
    playground: z.ZodOptional<z.ZodObject<{
        display: z.ZodOptional<z.ZodEnum<["interactive", "simple", "none"]>>;
        proxy: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        display?: "simple" | "none" | "interactive" | undefined;
        proxy?: boolean | undefined;
    }, {
        display?: "simple" | "none" | "interactive" | undefined;
        proxy?: boolean | undefined;
    }>>;
    examples: z.ZodOptional<z.ZodObject<{
        defaults: z.ZodOptional<z.ZodEnum<["required", "all"]>>;
        languages: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        defaults?: "all" | "required" | undefined;
        languages?: string[] | undefined;
    }, {
        defaults?: "all" | "required" | undefined;
        languages?: string[] | undefined;
    }>>;
    mdx: z.ZodOptional<z.ZodObject<{
        auth: z.ZodOptional<z.ZodObject<{
            method: z.ZodOptional<z.ZodEnum<["bearer", "basic", "key", "cobo"]>>;
            name: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
        }, {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
        }>>;
        server: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodArray<z.ZodString, "many">]>>;
    }, "strip", z.ZodTypeAny, {
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
        } | undefined;
        server?: string | string[] | undefined;
    }, {
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
        } | undefined;
        server?: string | string[] | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    openapi?: string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined;
    asyncapi?: string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined;
    params?: {
        expanded?: "all" | "closed" | undefined;
    } | undefined;
    playground?: {
        display?: "simple" | "none" | "interactive" | undefined;
        proxy?: boolean | undefined;
    } | undefined;
    examples?: {
        defaults?: "all" | "required" | undefined;
        languages?: string[] | undefined;
    } | undefined;
    mdx?: {
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
        } | undefined;
        server?: string | string[] | undefined;
    } | undefined;
}, {
    openapi?: string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined;
    asyncapi?: string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined;
    params?: {
        expanded?: "all" | "closed" | undefined;
    } | undefined;
    playground?: {
        display?: "simple" | "none" | "interactive" | undefined;
        proxy?: boolean | undefined;
    } | undefined;
    examples?: {
        defaults?: "all" | "required" | undefined;
        languages?: string[] | undefined;
    } | undefined;
    mdx?: {
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
        } | undefined;
        server?: string | string[] | undefined;
    } | undefined;
}>;
