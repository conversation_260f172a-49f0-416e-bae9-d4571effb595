import { OpenAPIV3_1 } from 'openapi-types';
import { BaseConverter } from './BaseConverter.js';
import { DataSchemaArray } from './types/endpoint.js';
export declare class SchemaConverter extends BaseConverter {
    readonly schema: OpenAPIV3_1.SchemaObject | undefined;
    readonly required?: boolean | undefined;
    readonly path: string[];
    readonly location?: ("request" | "response") | undefined;
    readonly contentType?: string | undefined;
    readonly safeParse: boolean;
    private constructor();
    /**
     * This function converts the `schema` property into a `DataSchemaArray`. Due to
     * the recursive nature of OpenAPI schemas, this conversion happens in two parts:
     *
     * 1. **Reduction**\*: The schema is transformed into its *reduced form*. In this form,
     * the schema and any subschemas are represented by a schema with one property, `oneOf`,
     * whose items are guaranteed NOT to have a `oneOf`, `anyOf`, or `allOf` property.
     *
     * 2. **Conversion**: In this step, we take a schema in its reduced form and convert it
     * into a new data type. This is fairly straightforward, as we just need to convert
     * each element of each `oneOf` schema to a `DataSchema`, and do this for all subschemas.
     *
     * \*We call this step a reduction rather than a conversion because the result is still
     * of the `OpenAPIV3_1.SchemaObject` type.
     *
     * @returns An array of `DataSchema` objects representing all valid schemas
     */
    private convert;
    /**
     * This function should be used to reduce strictly `oneOf` and `anyOf` compositions.
     *
     * @param schemaArray `schema.allOf` or `schema.oneOf`
     * @returns a schema array equivalent to the `schemaArray` argument, but in reduced form
     */
    private reduceOptionsCompositions;
    private reduceCompositionsRecursive;
    private generateTopLevelSchemaArray;
    /**
     * Given two arrays representing schema options, return an array representing schema options that satisfy one element in both arrays.
     *
     * It is helpful to think of each array as a union of all the schemas in the array. This function can then be thought of as taking
     * the intersection of the two union types.
     *
     * Used in the Reduction step.
     *
     * @param a first array of schema options
     * @param b second array of schema options
     * @returns array of schemas that satisfy both arrays
     */
    private multiplySchemaArrays;
    private combineReducedSchemas;
    private combineTopLevelSchemas;
    private convertSchemaRecursive;
    private convertProperties;
    static convert({ schema, required, path, location, contentType, safeParse, }: {
        schema: OpenAPIV3_1.SchemaObject | undefined;
        required?: boolean;
        path?: string[];
        location?: 'request' | 'response';
        contentType?: string;
        safeParse?: boolean;
    }): DataSchemaArray;
}
/**
 * Given an OpenAPI 3.1 schema, this function will attempt to determine the schema type
 * based on the properties present in the schema. This is useful for assigning types to
 * schemas that are missing a type.
 *
 * For example, if a schema has no type but has `schema.properties`, we can infer the
 * intended type is `object`.
 *
 * Used in the Conversion step.
 *
 * @param schema
 * @returns if exactly one type can be inferred, the string corresponding to that type; otherwise `undefined`
 */
export declare const inferType: (schema: Omit<OpenAPIV3_1.SchemaObject, "type">) => OpenAPIV3_1.ArraySchemaObjectType | OpenAPIV3_1.NonArraySchemaObjectType | undefined;
