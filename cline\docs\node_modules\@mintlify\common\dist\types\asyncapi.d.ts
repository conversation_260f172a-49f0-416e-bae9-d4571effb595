import { AsyncAPIDocumentInterface, Input, ChannelInterface, ServerInterface, ChannelParameterInterface, BindingsInterface, OperationInterface, SchemaInterface, MessageInterface, OperationReplyInterface, OperationTraitsInterface, MessageTraitsInterface, ExtensionInterface, SecuritySchemeInterface, BindingInterface, AsyncAPIDocumentV3 } from '@asyncapi/parser';
export type { AsyncAPIDocumentInterface, ServerInterface, ChannelParameterInterface, BindingsInterface, OperationInterface, SchemaInterface, MessageInterface, OperationReplyInterface, OperationTraitsInterface, MessageTraitsInterface, ExtensionInterface, SecuritySchemeInterface, BindingInterface, AsyncAPIDocumentV3, };
export type AsyncAPIParserInput = Input;
export type AsyncAPIFile = {
    filename: string;
    spec: AsyncAPIDocumentInterface;
    originalFileLocation: string;
};
export type SavedAsyncAPIFile = {
    filename: string;
    spec: AsyncAPIDocumentV3;
    originalFileLocation: string;
};
export interface AsyncAPIChannel extends ChannelInterface {
    tags(): {
        all(): Array<{
            name(): string;
        }>;
    };
    title(): string | undefined;
    description(): string | undefined;
    summary(): string | undefined;
    id(): string;
}
export type ParsedAsyncApiPageMetadata = {
    filename?: string;
    channelId: string;
    title?: string;
    description?: string;
} | undefined;
export type AnyRecord = Record<string, any>;
export type AsyncApiParameter = {
    id: string;
    jsonSchema: AnyRecord | undefined;
    type: string | undefined;
    description: string | undefined;
    propertiesCount: number | undefined;
    required: boolean | undefined;
    deprecated: boolean | undefined;
};
export type AsyncApiBinding = {
    protocol: string;
    version: string;
    value: AnyRecord;
    schemaProperties: SchemaProperty[];
};
export type AsyncApiExtension = {
    id: string;
    value: AnyRecord;
};
export type AsyncApiOperation = {
    id: string | undefined;
    title: string | undefined;
    type: string | undefined;
    description: string | undefined;
    messages: AsyncApiMessage[];
    bindings: AsyncApiBinding[];
    extensions: AsyncApiExtension[];
};
export type AsyncApiMessage = {
    id: string;
    contentType: string | undefined;
    payload: SchemaProperty[] | undefined;
    jsonPayloadSchema: AnyRecord | undefined;
    headers: SchemaProperty[] | undefined;
    jsonHeadersSchema: AnyRecord | undefined;
    title: string | undefined;
    description: string | undefined;
    example: string | undefined;
    bindings: AsyncApiBinding[] | undefined;
    extensions: AsyncApiExtension[] | undefined;
};
export type AsyncApiSecurityScheme = {
    id: string;
    name: string | undefined;
    type: string;
    description: string | undefined;
    in: string | undefined;
    scheme: string | undefined;
    bearerFormat: string | undefined;
    openIdConnectUrl: string | undefined;
    extensions: AsyncApiExtension[];
};
export type AsyncApiVariable = {
    id: string;
    description: string | undefined;
    defaultValue: string | undefined;
    allowedValues: string[] | undefined;
};
export type Server = {
    id: string;
    protocol: string;
    host: string;
    bindings: AsyncApiBinding[];
    variables: AsyncApiVariable[];
};
export type ChannelData = {
    id: string;
    title: string;
    description: string | undefined;
    servers: Server[];
    address: string | null | undefined;
    parameters: AsyncApiParameter[];
    bindings: AsyncApiBinding[];
    extensions: AsyncApiExtension[];
    securitySchemes: AsyncApiSecurityScheme[];
    operations: AsyncApiOperation[];
    sendOperations: AsyncApiOperation[];
    receiveOperations: AsyncApiOperation[];
    sendMessages: AsyncApiMessage[];
    receiveMessages: AsyncApiMessage[];
};
export type SchemaProperty = {
    name: string;
    type: string | undefined;
    description?: string | undefined;
    properties?: SchemaProperty[];
    required?: boolean;
    deprecated?: boolean;
};
