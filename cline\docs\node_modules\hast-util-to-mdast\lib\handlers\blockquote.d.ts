/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Blockquote} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Blockquote}
 *   mdast node.
 */
export function blockquote(state: State, node: Readonly<Element>): Blockquote;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Blockquote } from 'mdast';
//# sourceMappingURL=blockquote.d.ts.map