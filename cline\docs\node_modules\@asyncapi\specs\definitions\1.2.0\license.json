{"type": "object", "required": ["name"], "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the license type. It's encouraged to use an OSI compatible license."}, "url": {"type": "string", "description": "The URL pointing to the license.", "format": "uri"}}, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.2.0/vendorExtension.json"}}, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.2.0/license.json"}