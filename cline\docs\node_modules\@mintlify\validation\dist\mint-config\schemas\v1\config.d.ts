import { z } from 'zod';
export declare const mintConfigSchema: z.ZodObject<{
    $schema: z.ZodDefault<z.ZodOptional<z.ZodString>>;
    mintlify: z.ZodOptional<z.ZodString>;
    name: z.ZodString;
    logo: z.Zod<PERSON>ptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
        light: z.ZodString;
        dark: z.ZodString;
        href: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        light: string;
        dark: string;
        href?: string | undefined;
    }, {
        light: string;
        dark: string;
        href?: string | undefined;
    }>]>>;
    theme: z.ZodOptional<z.ZodEnum<["venus", "quill", "prism"]>>;
    layout: z.ZodOptional<z.ZodEnum<["topnav", "sidenav", "solidSidenav"]>>;
    favicon: z.ZodString;
    openapi: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.Z<PERSON>y<z.ZodEffects<z.ZodString, string, string>, "many">]>>;
    topbar: z.ZodOptional<z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["gradient", "default"]>>;
    }, "strip", z.ZodTypeAny, {
        style?: "gradient" | "default" | undefined;
    }, {
        style?: "gradient" | "default" | undefined;
    }>>;
    sidebar: z.ZodOptional<z.ZodObject<{
        items: z.ZodOptional<z.ZodEnum<["container", "card", "border", "undecorated"]>>;
    }, "strip", z.ZodTypeAny, {
        items?: "container" | "card" | "border" | "undecorated" | undefined;
    }, {
        items?: "container" | "card" | "border" | "undecorated" | undefined;
    }>>;
    rounded: z.ZodOptional<z.ZodEnum<["default", "sharp"]>>;
    api: z.ZodOptional<z.ZodObject<{
        baseUrl: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodArray<z.ZodString, "many">]>>;
        auth: z.ZodOptional<z.ZodObject<{
            method: z.ZodOptional<z.ZodEnum<["bearer", "basic", "key", "cobo"]>>;
            name: z.ZodOptional<z.ZodString>;
            inputPrefix: z.ZodOptional<z.ZodString>;
        }, "strict", z.ZodTypeAny, {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
            inputPrefix?: string | undefined;
        }, {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
            inputPrefix?: string | undefined;
        }>>;
        playground: z.ZodOptional<z.ZodObject<{
            mode: z.ZodDefault<z.ZodOptional<z.ZodEnum<["show", "simple", "hide"]>>>;
            disableProxy: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            mode: "show" | "simple" | "hide";
            disableProxy?: boolean | undefined;
        }, {
            mode?: "show" | "simple" | "hide" | undefined;
            disableProxy?: boolean | undefined;
        }>>;
        request: z.ZodOptional<z.ZodObject<{
            example: z.ZodOptional<z.ZodObject<{
                showOptionalParams: z.ZodOptional<z.ZodBoolean>;
                languages: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodEnum<["bash", "python", "javascript", "php", "go", "java"]>, z.ZodString]>, "many">>;
            }, "strip", z.ZodTypeAny, {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            }, {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            example?: {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            } | undefined;
        }, {
            example?: {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            } | undefined;
        }>>;
        maintainOrder: z.ZodOptional<z.ZodBoolean>;
        paramFields: z.ZodOptional<z.ZodObject<{
            expanded: z.ZodOptional<z.ZodEnum<["all", "topLevel", "topLevelOneOfs", "none"]>>;
        }, "strip", z.ZodTypeAny, {
            expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
        }, {
            expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
        }>>;
    }, "strict", z.ZodTypeAny, {
        baseUrl?: string | string[] | undefined;
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
            inputPrefix?: string | undefined;
        } | undefined;
        playground?: {
            mode: "show" | "simple" | "hide";
            disableProxy?: boolean | undefined;
        } | undefined;
        request?: {
            example?: {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            } | undefined;
        } | undefined;
        maintainOrder?: boolean | undefined;
        paramFields?: {
            expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
        } | undefined;
    }, {
        baseUrl?: string | string[] | undefined;
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
            inputPrefix?: string | undefined;
        } | undefined;
        playground?: {
            mode?: "show" | "simple" | "hide" | undefined;
            disableProxy?: boolean | undefined;
        } | undefined;
        request?: {
            example?: {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            } | undefined;
        } | undefined;
        maintainOrder?: boolean | undefined;
        paramFields?: {
            expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
        } | undefined;
    }>>;
    modeToggle: z.ZodOptional<z.ZodObject<{
        default: z.ZodOptional<z.ZodEnum<["light", "dark"]>>;
        isHidden: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        default?: "light" | "dark" | undefined;
        isHidden?: boolean | undefined;
    }, {
        default?: "light" | "dark" | undefined;
        isHidden?: boolean | undefined;
    }>>;
    versions: z.ZodOptional<z.ZodEffects<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodObject<{
        name: z.ZodString;
        url: z.ZodOptional<z.ZodString>;
        default: z.ZodOptional<z.ZodLiteral<true>>;
        locale: z.ZodOptional<z.ZodEnum<["en", "cn", "zh", "zh-Hans", "zh-Hant", "es", "fr", "fr-CA", "ja", "jp", "pt", "pt-BR", "de", "ko", "it", "ru", "id", "ar", "tr", "hi"]>>;
    }, "strict", z.ZodTypeAny, {
        name: string;
        url?: string | undefined;
        default?: true | undefined;
        locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
    }, {
        name: string;
        url?: string | undefined;
        default?: true | undefined;
        locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
    }>]>, "many">, (string | {
        name: string;
        url?: string | undefined;
        default?: true | undefined;
        locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
    })[], (string | {
        name: string;
        url?: string | undefined;
        default?: true | undefined;
        locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
    })[]>>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
    codeBlock: z.ZodOptional<z.ZodObject<{
        mode: z.ZodOptional<z.ZodEnum<["auto", "dark"]>>;
    }, "strip", z.ZodTypeAny, {
        mode?: "dark" | "auto" | undefined;
    }, {
        mode?: "dark" | "auto" | undefined;
    }>>;
    colors: z.ZodObject<{
        primary: z.ZodString;
        light: z.ZodOptional<z.ZodString>;
        dark: z.ZodOptional<z.ZodString>;
        background: z.ZodOptional<z.ZodObject<{
            light: z.ZodOptional<z.ZodString>;
            dark: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            light?: string | undefined;
            dark?: string | undefined;
        }, {
            light?: string | undefined;
            dark?: string | undefined;
        }>>;
        anchors: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
            from: z.ZodString;
            via: z.ZodOptional<z.ZodString>;
            to: z.ZodString;
        }, "strict", z.ZodTypeAny, {
            from: string;
            to: string;
            via?: string | undefined;
        }, {
            from: string;
            to: string;
            via?: string | undefined;
        }>]>>;
        ultraLight: z.ZodOptional<z.ZodAny>;
        ultraDark: z.ZodOptional<z.ZodAny>;
    }, "strict", z.ZodTypeAny, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
        background?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
        anchors?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        ultraLight?: any;
        ultraDark?: any;
    }, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
        background?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
        anchors?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        ultraLight?: any;
        ultraDark?: any;
    }>;
    eyebrow: z.ZodOptional<z.ZodObject<{
        display: z.ZodOptional<z.ZodEnum<["section", "breadcrumbs"]>>;
    }, "strip", z.ZodTypeAny, {
        display?: "section" | "breadcrumbs" | undefined;
    }, {
        display?: "section" | "breadcrumbs" | undefined;
    }>>;
    topbarCtaButton: z.ZodOptional<z.ZodUnion<[z.ZodObject<{
        type: z.ZodOptional<z.ZodLiteral<"link">>;
        name: z.ZodString;
        url: z.ZodString;
        style: z.ZodOptional<z.ZodEnum<["pill", "roundedRectangle"]>>;
        arrow: z.ZodOptional<z.ZodBoolean>;
    }, "strict", z.ZodTypeAny, {
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    }, {
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    }>, z.ZodObject<{
        type: z.ZodLiteral<"github">;
        url: z.ZodString;
    }, "strict", z.ZodTypeAny, {
        type: "github";
        url: string;
    }, {
        type: "github";
        url: string;
    }>]>>;
    topbarLinks: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodObject<{
        type: z.ZodOptional<z.ZodLiteral<"link">>;
        name: z.ZodString;
        url: z.ZodString;
        style: z.ZodOptional<z.ZodEnum<["pill", "roundedRectangle"]>>;
        arrow: z.ZodOptional<z.ZodBoolean>;
    }, "strict", z.ZodTypeAny, {
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    }, {
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    }>, z.ZodObject<{
        type: z.ZodLiteral<"github">;
        url: z.ZodString;
    }, "strict", z.ZodTypeAny, {
        type: "github";
        url: string;
    }, {
        type: "github";
        url: string;
    }>]>, "many">>;
    navigation: z.ZodArray<z.ZodType<import("@mintlify/models").NavigationGroup, z.ZodTypeDef, import("@mintlify/models").NavigationGroup>, "many">;
    primaryTab: z.ZodOptional<z.ZodObject<{
        name: z.ZodString;
        isDefaultHidden: z.ZodOptional<z.ZodBoolean>;
    }, "strict", z.ZodTypeAny, {
        name: string;
        isDefaultHidden?: boolean | undefined;
    }, {
        name: string;
        isDefaultHidden?: boolean | undefined;
    }>>;
    topAnchor: z.ZodOptional<z.ZodObject<{
        name: z.ZodString;
        icon: z.ZodOptional<z.ZodString>;
        iconType: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
    }, "strict", z.ZodTypeAny, {
        name: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    }, {
        name: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    }>>;
    anchors: z.ZodOptional<z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        url: z.ZodString;
        icon: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        iconType: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        color: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
            from: z.ZodString;
            via: z.ZodOptional<z.ZodString>;
            to: z.ZodString;
        }, "strict", z.ZodTypeAny, {
            from: string;
            to: string;
            via?: string | undefined;
        }, {
            from: string;
            to: string;
            via?: string | undefined;
        }>]>>;
        isDefaultHidden: z.ZodOptional<z.ZodBoolean>;
        version: z.ZodOptional<z.ZodString>;
        openapi: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        url: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        color?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        isDefaultHidden?: boolean | undefined;
        version?: string | undefined;
        openapi?: string | undefined;
    }, {
        name: string;
        url: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        color?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        isDefaultHidden?: boolean | undefined;
        version?: string | undefined;
        openapi?: string | undefined;
    }>, "many">>;
    tabs: z.ZodOptional<z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        url: z.ZodString;
        version: z.ZodOptional<z.ZodString>;
        isDefaultHidden: z.ZodOptional<z.ZodBoolean>;
        openapi: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        url: string;
        version?: string | undefined;
        isDefaultHidden?: boolean | undefined;
        openapi?: string | undefined;
    }, {
        name: string;
        url: string;
        version?: string | undefined;
        isDefaultHidden?: boolean | undefined;
        openapi?: string | undefined;
    }>, "many">>;
    footer: z.ZodOptional<z.ZodObject<{
        socials: z.ZodOptional<z.ZodUnion<[z.ZodRecord<z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>, z.ZodString>, z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>;
            url: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
            url: string;
        }, {
            type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
            url: string;
        }>, "many">]>>;
        links: z.ZodOptional<z.ZodArray<z.ZodObject<{
            title: z.ZodOptional<z.ZodString>;
            links: z.ZodArray<z.ZodObject<{
                label: z.ZodString;
                url: z.ZodUnion<[z.ZodString, z.ZodString]>;
            }, "strip", z.ZodTypeAny, {
                url: string;
                label: string;
            }, {
                url: string;
                label: string;
            }>, "many">;
        }, "strip", z.ZodTypeAny, {
            links: {
                url: string;
                label: string;
            }[];
            title?: string | undefined;
        }, {
            links: {
                url: string;
                label: string;
            }[];
            title?: string | undefined;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
            type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
            url: string;
        }[] | undefined;
        links?: {
            links: {
                url: string;
                label: string;
            }[];
            title?: string | undefined;
        }[] | undefined;
    }, {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
            type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
            url: string;
        }[] | undefined;
        links?: {
            links: {
                url: string;
                label: string;
            }[];
            title?: string | undefined;
        }[] | undefined;
    }>>;
    background: z.ZodOptional<z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["gradient", "grid", "windows"]>>;
    }, "strip", z.ZodTypeAny, {
        style?: "gradient" | "grid" | "windows" | undefined;
    }, {
        style?: "gradient" | "grid" | "windows" | undefined;
    }>>;
    backgroundImage: z.ZodOptional<z.ZodString>;
    font: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodObject<{
        family: z.ZodString;
        weight: z.ZodOptional<z.ZodNumber>;
        url: z.ZodOptional<z.ZodString>;
        format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
    }, "strip", z.ZodTypeAny, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, z.ZodObject<{
        headings: z.ZodOptional<z.ZodEffects<z.ZodObject<{
            family: z.ZodString;
            weight: z.ZodOptional<z.ZodNumber>;
            url: z.ZodOptional<z.ZodString>;
            format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
        }, "strip", z.ZodTypeAny, {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>, {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>>;
        body: z.ZodOptional<z.ZodEffects<z.ZodObject<{
            family: z.ZodString;
            weight: z.ZodOptional<z.ZodNumber>;
            url: z.ZodOptional<z.ZodString>;
            format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
        }, "strip", z.ZodTypeAny, {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>, {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>>;
    }, "strict", z.ZodTypeAny, {
        headings?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    }, {
        headings?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    }>]>>;
    feedback: z.ZodOptional<z.ZodObject<{
        thumbsRating: z.ZodOptional<z.ZodBoolean>;
        suggestEdit: z.ZodOptional<z.ZodBoolean>;
        raiseIssue: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        thumbsRating?: boolean | undefined;
        suggestEdit?: boolean | undefined;
        raiseIssue?: boolean | undefined;
    }, {
        thumbsRating?: boolean | undefined;
        suggestEdit?: boolean | undefined;
        raiseIssue?: boolean | undefined;
    }>>;
    analytics: z.ZodOptional<z.ZodObject<{
        amplitude: z.ZodOptional<z.ZodObject<{
            apiKey: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            apiKey: string;
        }, {
            apiKey: string;
        }>>;
        clearbit: z.ZodOptional<z.ZodObject<{
            publicApiKey: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            publicApiKey: string;
        }, {
            publicApiKey: string;
        }>>;
        fathom: z.ZodOptional<z.ZodObject<{
            siteId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            siteId: string;
        }, {
            siteId: string;
        }>>;
        ga4: z.ZodOptional<z.ZodObject<{
            measurementId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            measurementId: string;
        }, {
            measurementId: string;
        }>>;
        gtm: z.ZodOptional<z.ZodObject<{
            tagId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            tagId: string;
        }, {
            tagId: string;
        }>>;
        heap: z.ZodOptional<z.ZodObject<{
            appId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            appId: string;
        }, {
            appId: string;
        }>>;
        hotjar: z.ZodOptional<z.ZodObject<{
            hjid: z.ZodString;
            hjsv: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            hjid: string;
            hjsv: string;
        }, {
            hjid: string;
            hjsv: string;
        }>>;
        koala: z.ZodOptional<z.ZodObject<{
            publicApiKey: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            publicApiKey: string;
        }, {
            publicApiKey: string;
        }>>;
        logrocket: z.ZodOptional<z.ZodObject<{
            appId: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            appId: string;
        }, {
            appId: string;
        }>>;
        mixpanel: z.ZodOptional<z.ZodObject<{
            projectToken: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            projectToken: string;
        }, {
            projectToken: string;
        }>>;
        pirsch: z.ZodOptional<z.ZodObject<{
            id: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            id: string;
        }, {
            id: string;
        }>>;
        posthog: z.ZodOptional<z.ZodObject<{
            apiKey: z.ZodString;
            apiHost: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            apiKey: string;
            apiHost?: string | undefined;
        }, {
            apiKey: string;
            apiHost?: string | undefined;
        }>>;
        plausible: z.ZodOptional<z.ZodObject<{
            domain: z.ZodEffects<z.ZodString, string, string>;
            server: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        }, "strip", z.ZodTypeAny, {
            domain: string;
            server?: string | undefined;
        }, {
            domain: string;
            server?: string | undefined;
        }>>;
        segment: z.ZodOptional<z.ZodObject<{
            key: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            key: string;
        }, {
            key: string;
        }>>;
    }, "strict", z.ZodTypeAny, {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
    }, {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
    }>>;
    integrations: z.ZodOptional<z.ZodObject<{
        intercom: z.ZodOptional<z.ZodString>;
        frontchat: z.ZodOptional<z.ZodString>;
        osano: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        intercom?: string | undefined;
        frontchat?: string | undefined;
        osano?: string | undefined;
    }, {
        intercom?: string | undefined;
        frontchat?: string | undefined;
        osano?: string | undefined;
    }>>;
    isWhiteLabeled: z.ZodOptional<z.ZodBoolean>;
    search: z.ZodOptional<z.ZodObject<{
        prompt: z.ZodOptional<z.ZodString>;
        location: z.ZodOptional<z.ZodEnum<["side", "top"]>>;
    }, "strip", z.ZodTypeAny, {
        prompt?: string | undefined;
        location?: "side" | "top" | undefined;
    }, {
        prompt?: string | undefined;
        location?: "side" | "top" | undefined;
    }>>;
    redirects: z.ZodOptional<z.ZodEffects<z.ZodArray<z.ZodObject<{
        source: z.ZodString;
        destination: z.ZodString;
        permanent: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }>, "many">, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[], {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[]>>;
    seo: z.ZodOptional<z.ZodObject<{
        indexHiddenPages: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        indexHiddenPages?: boolean | undefined;
    }, {
        indexHiddenPages?: boolean | undefined;
    }>>;
    footerSocials: z.ZodOptional<z.ZodUnion<[z.ZodRecord<z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>, z.ZodString>, z.ZodArray<z.ZodObject<{
        type: z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>;
        url: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }, {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }>, "many">]>>;
}, "strip", z.ZodTypeAny, {
    name: string;
    $schema: string;
    favicon: string;
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
        background?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
        anchors?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        ultraLight?: any;
        ultraDark?: any;
    };
    navigation: import("@mintlify/models").NavigationGroup[];
    mintlify?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    theme?: "venus" | "quill" | "prism" | undefined;
    layout?: "topnav" | "sidenav" | "solidSidenav" | undefined;
    openapi?: string | string[] | undefined;
    topbar?: {
        style?: "gradient" | "default" | undefined;
    } | undefined;
    sidebar?: {
        items?: "container" | "card" | "border" | "undecorated" | undefined;
    } | undefined;
    rounded?: "default" | "sharp" | undefined;
    api?: {
        baseUrl?: string | string[] | undefined;
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
            inputPrefix?: string | undefined;
        } | undefined;
        playground?: {
            mode: "show" | "simple" | "hide";
            disableProxy?: boolean | undefined;
        } | undefined;
        request?: {
            example?: {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            } | undefined;
        } | undefined;
        maintainOrder?: boolean | undefined;
        paramFields?: {
            expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
        } | undefined;
    } | undefined;
    modeToggle?: {
        default?: "light" | "dark" | undefined;
        isHidden?: boolean | undefined;
    } | undefined;
    versions?: (string | {
        name: string;
        url?: string | undefined;
        default?: true | undefined;
        locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
    })[] | undefined;
    metadata?: Record<string, string> | undefined;
    codeBlock?: {
        mode?: "dark" | "auto" | undefined;
    } | undefined;
    eyebrow?: {
        display?: "section" | "breadcrumbs" | undefined;
    } | undefined;
    topbarCtaButton?: {
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    } | {
        type: "github";
        url: string;
    } | undefined;
    topbarLinks?: ({
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    } | {
        type: "github";
        url: string;
    })[] | undefined;
    primaryTab?: {
        name: string;
        isDefaultHidden?: boolean | undefined;
    } | undefined;
    topAnchor?: {
        name: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    } | undefined;
    anchors?: {
        name: string;
        url: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        color?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        isDefaultHidden?: boolean | undefined;
        version?: string | undefined;
        openapi?: string | undefined;
    }[] | undefined;
    tabs?: {
        name: string;
        url: string;
        version?: string | undefined;
        isDefaultHidden?: boolean | undefined;
        openapi?: string | undefined;
    }[] | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
            type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
            url: string;
        }[] | undefined;
        links?: {
            links: {
                url: string;
                label: string;
            }[];
            title?: string | undefined;
        }[] | undefined;
    } | undefined;
    background?: {
        style?: "gradient" | "grid" | "windows" | undefined;
    } | undefined;
    backgroundImage?: string | undefined;
    font?: {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        headings?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    feedback?: {
        thumbsRating?: boolean | undefined;
        suggestEdit?: boolean | undefined;
        raiseIssue?: boolean | undefined;
    } | undefined;
    analytics?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
    } | undefined;
    integrations?: {
        intercom?: string | undefined;
        frontchat?: string | undefined;
        osano?: string | undefined;
    } | undefined;
    isWhiteLabeled?: boolean | undefined;
    search?: {
        prompt?: string | undefined;
        location?: "side" | "top" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    seo?: {
        indexHiddenPages?: boolean | undefined;
    } | undefined;
    footerSocials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }[] | undefined;
}, {
    name: string;
    favicon: string;
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
        background?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
        anchors?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        ultraLight?: any;
        ultraDark?: any;
    };
    navigation: import("@mintlify/models").NavigationGroup[];
    $schema?: string | undefined;
    mintlify?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    theme?: "venus" | "quill" | "prism" | undefined;
    layout?: "topnav" | "sidenav" | "solidSidenav" | undefined;
    openapi?: string | string[] | undefined;
    topbar?: {
        style?: "gradient" | "default" | undefined;
    } | undefined;
    sidebar?: {
        items?: "container" | "card" | "border" | "undecorated" | undefined;
    } | undefined;
    rounded?: "default" | "sharp" | undefined;
    api?: {
        baseUrl?: string | string[] | undefined;
        auth?: {
            method?: "key" | "bearer" | "basic" | "cobo" | undefined;
            name?: string | undefined;
            inputPrefix?: string | undefined;
        } | undefined;
        playground?: {
            mode?: "show" | "simple" | "hide" | undefined;
            disableProxy?: boolean | undefined;
        } | undefined;
        request?: {
            example?: {
                showOptionalParams?: boolean | undefined;
                languages?: string[] | undefined;
            } | undefined;
        } | undefined;
        maintainOrder?: boolean | undefined;
        paramFields?: {
            expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
        } | undefined;
    } | undefined;
    modeToggle?: {
        default?: "light" | "dark" | undefined;
        isHidden?: boolean | undefined;
    } | undefined;
    versions?: (string | {
        name: string;
        url?: string | undefined;
        default?: true | undefined;
        locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
    })[] | undefined;
    metadata?: Record<string, string> | undefined;
    codeBlock?: {
        mode?: "dark" | "auto" | undefined;
    } | undefined;
    eyebrow?: {
        display?: "section" | "breadcrumbs" | undefined;
    } | undefined;
    topbarCtaButton?: {
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    } | {
        type: "github";
        url: string;
    } | undefined;
    topbarLinks?: ({
        name: string;
        url: string;
        type?: "link" | undefined;
        style?: "pill" | "roundedRectangle" | undefined;
        arrow?: boolean | undefined;
    } | {
        type: "github";
        url: string;
    })[] | undefined;
    primaryTab?: {
        name: string;
        isDefaultHidden?: boolean | undefined;
    } | undefined;
    topAnchor?: {
        name: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    } | undefined;
    anchors?: {
        name: string;
        url: string;
        icon?: string | undefined;
        iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        color?: string | {
            from: string;
            to: string;
            via?: string | undefined;
        } | undefined;
        isDefaultHidden?: boolean | undefined;
        version?: string | undefined;
        openapi?: string | undefined;
    }[] | undefined;
    tabs?: {
        name: string;
        url: string;
        version?: string | undefined;
        isDefaultHidden?: boolean | undefined;
        openapi?: string | undefined;
    }[] | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
            type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
            url: string;
        }[] | undefined;
        links?: {
            links: {
                url: string;
                label: string;
            }[];
            title?: string | undefined;
        }[] | undefined;
    } | undefined;
    background?: {
        style?: "gradient" | "grid" | "windows" | undefined;
    } | undefined;
    backgroundImage?: string | undefined;
    font?: {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        headings?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            url?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    feedback?: {
        thumbsRating?: boolean | undefined;
        suggestEdit?: boolean | undefined;
        raiseIssue?: boolean | undefined;
    } | undefined;
    analytics?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
    } | undefined;
    integrations?: {
        intercom?: string | undefined;
        frontchat?: string | undefined;
        osano?: string | undefined;
    } | undefined;
    isWhiteLabeled?: boolean | undefined;
    search?: {
        prompt?: string | undefined;
        location?: "side" | "top" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    seo?: {
        indexHiddenPages?: boolean | undefined;
    } | undefined;
    footerSocials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }[] | undefined;
}>;
