asyncapi: 3.0.0
info:
  title: Account Service
  version: 1.0.0
  description: This service is in charge of processing user accounts
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0

servers:
  production:
    host: mqtt://api.example.com
    protocol: mqtt
    description: Production server
  development:
    host: mqtt://dev.example.com
    protocol: mqtt
    description: Development server

channels:
  userSignedUp:
    address: user/signedup
    messages:
      userSignedUpMessage:
        $ref: '#/components/messages/UserSignedUp'
  userDeleted:
    address: user/deleted
    messages:
      userDeletedMessage:
        $ref: '#/components/messages/UserDeleted'

operations:
  sendUserSignedUp:
    action: send
    channel:
      $ref: '#/channels/userSignedUp'
    messages:
      - $ref: '#/channels/userSignedUp/messages/userSignedUpMessage'
  receiveUserSignedUp:
    action: receive
    channel:
      $ref: '#/channels/userSignedUp'
    messages:
      - $ref: '#/channels/userSignedUp/messages/userSignedUpMessage'
  sendUserDeleted:
    action: send
    channel:
      $ref: '#/channels/userDeleted'
    messages:
      - $ref: '#/channels/userDeleted/messages/userDeletedMessage'
  receiveUserDeleted:
    action: receive
    channel:
      $ref: '#/channels/userDeleted'
    messages:
      - $ref: '#/channels/userDeleted/messages/userDeletedMessage'

components:
  messages:
    UserSignedUp:
      name: userSignedUp
      title: User signed up event
      summary: Inform about a new user registration
      contentType: application/json
      payload:
        $ref: '#/components/schemas/UserSignedUpPayload'
    UserDeleted:
      name: userDeleted
      title: User deleted event
      summary: Inform about a user deletion
      contentType: application/json
      payload:
        $ref: '#/components/schemas/UserDeletedPayload'

  schemas:
    UserSignedUpPayload:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: User unique identifier
        fullName:
          type: string
          description: User full name
        email:
          type: string
          format: email
          description: User email address
        createdAt:
          type: string
          format: date-time
          description: Datetime when the user was created
      required:
        - id
        - email

    UserDeletedPayload:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: User unique identifier
        deletedAt:
          type: string
          format: date-time
          description: Datetime when the user was deleted
      required:
        - id
