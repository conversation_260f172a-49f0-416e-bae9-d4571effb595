import { z } from 'zod';
import { isAbsoluteUrl } from '../../../../isAbsoluteUrl.js';
import { normalizeRelativePath } from '../../../../transforms/normalizeRelativePath.js';
export const openapiStringSchema = z
    .string()
    .trim()
    .nonempty()
    .transform((value, ctx) => {
    if (isAbsoluteUrl(value)) {
        try {
            const url = new URL(value);
            if (url.protocol !== 'https:' && url.protocol !== 'http:') {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: "Must use protocol https or http. Urls with 'http://' are only supported with cli option --local-schema",
                });
                return z.NEVER;
            }
        }
        catch (_a) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Invalid URL',
            });
            return z.NEVER;
        }
        return value;
    }
    else {
        return normalizeRelativePath(value);
    }
});
export const openapiObjSchema = z
    .object({
    source: openapiStringSchema,
    directory: z.string().trim().optional(),
})
    .strict();
export const openApiSchema = z
    .union([openapiStringSchema, z.array(openapiStringSchema), openapiObjSchema])
    .describe('A string or an array of strings of absolute or relative urls pointing to the OpenAPI file(s)');
