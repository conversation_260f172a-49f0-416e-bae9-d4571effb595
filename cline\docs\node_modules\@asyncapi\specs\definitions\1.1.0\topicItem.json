{"type": "object", "additionalProperties": false, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.1.0/vendorExtension.json"}}, "minProperties": 1, "properties": {"$ref": {"type": "string"}, "parameters": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/1.1.0/parameter.json"}}, "publish": {"$ref": "http://asyncapi.com/definitions/1.1.0/operation.json"}, "subscribe": {"$ref": "http://asyncapi.com/definitions/1.1.0/operation.json"}, "deprecated": {"type": "boolean", "default": false}}, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.1.0/topicItem.json"}