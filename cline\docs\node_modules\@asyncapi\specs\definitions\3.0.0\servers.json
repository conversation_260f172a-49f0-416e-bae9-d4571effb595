{"description": "An object representing multiple servers.", "type": "object", "additionalProperties": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/server.json"}]}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/servers.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/servers.json"}