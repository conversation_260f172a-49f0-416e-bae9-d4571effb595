{"version": 3, "file": "scalarInference.test.js", "sourceRoot": "", "sources": ["../../test/scalarInference.test.ts"], "names": [], "mappings": ";;AAAA,6BAA4B;AAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;AAC1B,4DAAwJ;AAExJ,qCAAoC;AAEpC,KAAK,CAAC,qBAAqB,EAAE,GAAG,EAAE;IAE9B,SAAS,mBAAmB,CAAC,MAAyB;QAClD,OAAO,qCAAG,CAAkB,MAAM,CAAC,CAAA;IACvC,CAAC;IAED,SAAS,QAAQ,CAAC,KAAK;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;IACnC,CAAC;IAED,IAAI,KAAK,GAAG,IAAI,CAAC;IAGjB,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;QAE/B,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU;YAChC,KAAK,CAAC,IAAI,EAAE;gBACR,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;oBAC3B,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;iBACtE;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAAA,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,4BAAU,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAA;QAErF,IAAI,CAAC,MAAM,EAAE,4BAAU,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAA;QAChE,KAAK,CAAC,uBAAuB,EAAE;YAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAChC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,4BAAU,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAA;QAC3F,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,SAAS,EAAE,4BAAU,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAA;QAE5D,IAAI,CAAC,OAAO,EAAE,4BAAU,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAA;QAEzE,IAAI,CAAC,gBAAgB,EAAE,4BAAU,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAA;QAEpE,IAAI,CAAC,WAAW,EAAE,4BAAU,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;QAE7D,IAAI,CAAC,QAAQ,EAAE,4BAAU,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;IAC9G,CAAC,CAAC,CAAA;IAEF,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE;QACrB,IAAI,CAAC,wBAAwB,EAAE;YAC3B,MAAM,IAAI,GAAsB,QAAQ,CAAC;;;;;;;EAOnD,CAAC,CAAA;YAES,MAAM,QAAQ,GAAG,CAAC,4BAAU,CAAC,IAAI,EAAE,4BAAU,CAAC,IAAI,EAAE,4BAAU,CAAC,GAAG,EAAE,4BAAU,CAAC,KAAK,EAAE,4BAAU,CAAC,KAAK,EAAE,4BAAU,CAAC,KAAK,EAAE,4BAAU,CAAC,MAAM,CAAC,CAAA;YAE5I,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;QAC3E,CAAC,CAAC,CAAA;IACN,CAAC,CAAC,CAAA;IAEF,KAAK,CAAC,cAAc,EAAE,GAAG,EAAE;QACvB,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAEhE,IAAI,CAAC,oBAAoB,EAAE;YACvB,KAAK,MAAM,OAAO,IAAI,UAAU,EAAE;gBAC9B,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,EAAE,4BAAU,CAAC,MAAM,CAAC,CAAC;aAC1F;QACL,CAAC,CAAC,CAAA;IACN,CAAC,CAAC,CAAA;AACN,CAAC,CAAC,CAAA;AAEF,KAAK,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAC3B,IAAI,CAAC,SAAS,EAAE;QACZ,MAAM,CAAC,WAAW,CAAC,kCAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QAC5C,MAAM,CAAC,WAAW,CAAC,kCAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAChD,MAAM,CAAC,WAAW,CAAC,kCAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;IACjD,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,aAAa,EAAE;QAChB,MAAM,CAAC,WAAW,CAAC,kCAAgB,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;IACpD,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,OAAO,EAAE;QACV,MAAM,CAAC,WAAW,CAAC,kCAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;IAClD,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,WAAW,EAAE;QACd,IAAI,KAAK,CAAC;QACV,IAAI;YACA,kCAAgB,CAAC,KAAK,CAAC,CAAA;SAC1B;QACD,OAAO,CAAC,EAAE;YACN,KAAK,GAAG,CAAC,CAAC;SACb;QAED,MAAM,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;AACN,CAAC,CAAC,CAAA;AAEF,KAAK,CAAC,qBAAqB,EAAE,GAAG,EAAE;IAC9B,IAAI,CAAC,SAAS,EAAE;QACZ,MAAM,CAAC,WAAW,CAAC,qCAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QAC/C,MAAM,CAAC,WAAW,CAAC,qCAAmB,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QACnD,MAAM,CAAC,WAAW,CAAC,qCAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;IACpD,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,eAAe,EAAE;QAClB,MAAM,CAAC,KAAK,CAAC,qCAAmB,CAAC,cAAc,CAAC,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,CAAA;QACzE,MAAM,CAAC,KAAK,CAAC,qCAAmB,CAAC,qBAAqB,CAAC,EAAE,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAA;IAC3F,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,aAAa,EAAE;QAChB,MAAM,CAAC,WAAW,CAAC,qCAAmB,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,OAAO,EAAE;QACV,MAAM,CAAC,WAAW,CAAC,qCAAmB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;IACrD,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,WAAW,EAAE;QACd,IAAI,KAAK,CAAC;QACV,IAAI;YACA,qCAAmB,CAAC,KAAK,CAAC,CAAA;SAC7B;QACD,OAAO,CAAC,EAAE;YACN,KAAK,GAAG,CAAC,CAAC;SACb;QAED,MAAM,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;AACN,CAAC,CAAC,CAAA;AAEF,KAAK,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAC3B,IAAI,CAAC,MAAM,EAAE;QACT,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;YAC1C,MAAM,CAAC,WAAW,CAAC,kCAAgB,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC5D;IACL,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,OAAO,EAAE;QACV,KAAK,MAAM,KAAK,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;YAC7C,MAAM,CAAC,WAAW,CAAC,kCAAgB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SAC7D;IACL,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,WAAW,EAAE;QACd,IAAI,KAAK,CAAC;QACV,IAAI;YACA,kCAAgB,CAAC,MAAM,CAAC,CAAA;SAC3B;QACD,OAAO,CAAC,EAAE;YACN,KAAK,GAAG,CAAC,CAAC;SACb;QAED,MAAM,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;AACN,CAAC,CAAC,CAAA;AAEF,KAAK,CAAC,gBAAgB,EAAE,GAAG,EAAE;IACzB,IAAI,CAAC,OAAO,EAAE;QACV,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;QACvD,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAA;QAC7C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChD,MAAM,CAAC,WAAW,CAAC,gCAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;SACrE;IACL,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,KAAK,EAAE;QACR,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,gCAAc,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,KAAK,GAAG,CAAC,CAAA;SAC1D;IACL,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,UAAU,EAAE;QACb,MAAM,CAAC,WAAW,CAAC,gCAAc,CAAC,MAAM,CAAC,EACrC,QAAQ,CAAC,CAAA;QACb,MAAM,CAAC,WAAW,CAAC,gCAAc,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAA;QACtD,MAAM,CAAC,WAAW,CAAC,gCAAc,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAA;IACxD,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,WAAW,EAAE;QACd,IAAI,KAAK,CAAC;QACV,IAAI;YACA,gCAAc,CAAC,MAAM,CAAC,CAAA;SACzB;QACD,OAAO,CAAC,EAAE;YACN,KAAK,GAAG,CAAC,CAAC;SACb;QAED,MAAM,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;AACN,CAAC,CAAC,CAAA"}