import { z } from 'zod';
import { hrefSchema } from './reusable/href.js';
import { iconSchema } from './reusable/icon.js';
const linkSchema = z.object({
    label: z.string(),
    icon: iconSchema.optional(),
    href: hrefSchema,
});
const buttonSchema = z.object({
    type: z.literal('button'),
    label: z.string(),
    href: hrefSchema,
});
const githubSchema = z.object({
    type: z.literal('github'),
    href: hrefSchema,
});
export const primarySchema = z.discriminatedUnion('type', [buttonSchema, githubSchema]);
export const navbarSchema = z
    .object({
    links: z.array(linkSchema).optional().describe('The links in the navbar'),
    primary: primarySchema.optional().describe('The primary CTA in the navbar'),
})
    .describe('Navbar content and settings');
