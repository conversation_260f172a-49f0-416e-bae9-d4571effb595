import { z } from 'zod';
import { hrefSchema } from './reusable/href.js';
export const logoSchema = z
    .union([
    z
        .string()
        .min(3, 'Must be a path to your logo file including the file extension')
        .describe('The logo (for both light and dark mode)'),
    z.object({
        light: z
            .string()
            .describe('Path pointing to the light logo file to use in dark mode, including the file extension. Example: `/logo.png`'),
        dark: z
            .string()
            .describe('Path pointing to the dark logo file to use in light mode, including the file extension. Example: `/logo-dark.png`'),
        href: hrefSchema
            .optional()
            .describe('The URL to redirect to when clicking the logo. If not provided, the logo will link to the homepage. Example: `https://example.com`'),
    }),
])
    .describe('The logo configuration. Can be a single image path for both light and dark mode, or separate paths for each mode with an optional click target URL');
