// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface SlashServiceDefinition {
  // Slash command service methods
}

export interface SlashCommand {
  name: string;
  description: string;
  usage: string;
  parameters?: SlashCommandParameter[];
}

export interface SlashCommandParameter {
  name: string;
  type: string;
  required: boolean;
  description?: string;
}

export interface SlashCommandRequest {
  command: string;
  args: string[];
}

export interface SlashCommandResponse {
  success: boolean;
  result?: string;
  error?: string;
}

export interface SlashCommandListResponse {
  commands: SlashCommand[];
}
