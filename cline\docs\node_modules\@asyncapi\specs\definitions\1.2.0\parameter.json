{"additionalProperties": false, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.2.0/vendorExtension.json"}}, "properties": {"description": {"type": "string", "description": "A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed."}, "name": {"type": "string", "description": "The name of the parameter."}, "schema": {"$ref": "http://asyncapi.com/definitions/1.2.0/schema.json"}, "$ref": {"type": "string"}}, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.2.0/parameter.json"}