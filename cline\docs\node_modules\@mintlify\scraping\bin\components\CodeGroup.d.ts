import type { Element } from 'hast';
import type { <PERSON><PERSON><PERSON><PERSON>, HastNodeIndex, HastNodeParent } from '../types/hast.js';
export declare function gitBookScrapeCodeGroup(node: HastNode, _: HastNodeIndex, parent: HastNodeParent): Element | undefined;
export declare function readmeScrapeCodeGroup(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
export declare function docusaurusScrapeCodeGroup(node: HastNode, _: HastNodeIndex, parent: HastNodeParent): Element | undefined;
