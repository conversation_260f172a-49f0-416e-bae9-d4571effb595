{"title": "Events Object", "type": "object", "additionalProperties": false, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.2.0/vendorExtension.json"}}, "minProperties": 1, "anyOf": [{"required": ["receive"]}, {"required": ["send"]}], "properties": {"receive": {"title": "Events Receive Object", "type": "array", "uniqueItems": true, "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/1.2.0/message.json"}}, "send": {"title": "Events Send Object", "type": "array", "uniqueItems": true, "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/1.2.0/message.json"}}}, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.2.0/events.json"}