import { z } from 'zod';
export declare enum ThumbnailAppearance {
    Light = "light",
    Dark = "dark"
}
export declare const thumbnailsSchema: z.ZodObject<{
    appearance: z.ZodOptional<z.ZodEnum<[ThumbnailAppearance.Light, ThumbnailAppearance.Dark]>>;
    background: z.<PERSON>od<PERSON><z.ZodString>;
}, "strip", z.<PERSON>, {
    appearance?: ThumbnailAppearance | undefined;
    background?: string | undefined;
}, {
    appearance?: ThumbnailAppearance | undefined;
    background?: string | undefined;
}>;
export type ThumbnailsConfig = z.infer<typeof thumbnailsSchema>;
