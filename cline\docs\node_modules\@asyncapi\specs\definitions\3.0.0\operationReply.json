{"type": "object", "description": "Describes the reply part that MAY be applied to an Operation Object. If an operation implements the request/reply pattern, the reply object represents the response message.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"address": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/operationReplyAddress.json"}]}, "channel": {"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, "messages": {"type": "array", "description": "A list of $ref pointers pointing to the supported Message Objects that can be processed by this operation as reply. It MUST contain a subset of the messages defined in the channel referenced in this operation reply. Every message processed by this operation MUST be valid against one, and only one, of the message objects referenced in this list. Please note the messages property value MUST be a list of Reference Objects and, therefore, MUST NOT contain Message Objects. However, it is RECOMMENDED that parsers (or other software) dereference this property for a better development experience.", "items": {"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/operationReply.json"}