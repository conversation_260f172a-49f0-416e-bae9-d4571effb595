import { iconTypes } from '@mintlify/models';
import { z } from 'zod';
import { normalizeRelativePath } from '../../transforms/normalizeRelativePath.js';
// export to allow type testing against @mintlify/models
export const baseNavigationGroupSchema = z
    .object({
    // We allow top-level groups to be an empty string if the user wants to hide the title.
    // Future work should refactor this so nested groups are non-empty strings.
    group: z.string().describe('The label for this group in the navigation sidebar'),
    icon: z.string().optional(),
    iconType: z.enum(iconTypes).optional(),
    version: z.string().optional(),
})
    .strict();
const navigationGroupSchema = baseNavigationGroupSchema.extend({
    pages: z.lazy(() => z
        .array(z.union([navigationGroupSchema, z.string().nonempty().transform(normalizeRelativePath)]))
        .nonempty()),
});
export const navigationSchema = z.array(navigationGroupSchema).min(1);
