import { walk } from 'estree-walker';
import { remove } from 'unist-util-remove';
import { isMdxJsEsm } from '../../utils.js';
const REACT_HOOKS = [
    'useState',
    'useEffect',
    'useRef',
    'useCallback',
    'useMemo',
    'useReducer',
    'useContext',
    'useLayoutEffect',
    'useImperativeHandle',
    'useDebugValue',
    'useDeferredValue',
    'useTransition',
    'useId',
    'useSyncExternalStore',
    'useInsertionEffect',
    'useOptimistic',
    'useActionState',
];
export const remarkMdxRemoveJs = () => (tree) => {
    remove(tree, ['mdxTextExpression', 'mdxFlowExpression']);
    remove(tree, (node) => {
        var _a;
        if (!isMdxJsEsm(node))
            return false;
        if (((_a = node.data) === null || _a === void 0 ? void 0 : _a.estree) && !isFunction(node.data.estree))
            return false;
        const value = node.value;
        const containsReact = REACT_HOOKS.some((hook) => value.includes(hook) || value.includes('React.' + hook.toLowerCase()));
        return containsReact;
    });
};
function isFunction(estree) {
    if (!estree.body.length)
        return false;
    let hasFunctionDeclaration = false;
    walk(estree, {
        enter(node) {
            if (node.type === 'FunctionDeclaration' ||
                node.type === 'ArrowFunctionExpression' ||
                node.type === 'FunctionExpression') {
                hasFunctionDeclaration = true;
                return this.skip();
            }
            if (node.type === 'ExportDefaultDeclaration' &&
                (node.declaration.type === 'FunctionDeclaration' ||
                    node.declaration.type === 'FunctionExpression' ||
                    node.declaration.type === 'ArrowFunctionExpression')) {
                hasFunctionDeclaration = true;
                return this.skip();
            }
            if (node.type === 'VariableDeclaration' &&
                node.declarations.some((decl) => decl.init &&
                    (decl.init.type === 'ArrowFunctionExpression' ||
                        decl.init.type === 'FunctionExpression'))) {
                hasFunctionDeclaration = true;
                return this.skip();
            }
        },
    });
    return hasFunctionDeclaration;
}
