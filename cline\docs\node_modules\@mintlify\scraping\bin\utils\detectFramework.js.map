{"version": 3, "file": "detectFramework.js", "sourceRoot": "", "sources": ["../../src/utils/detectFramework.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAGzD,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAE/B,MAAM,CAAC,MAAM,SAAS,GAAc;IAClC,MAAM,EAAE,SAAS;IACjB,OAAO,EAAE,SAAS;CACnB,CAAC;AAEF,MAAM,UAAU,eAAe,CAAC,QAAc;IAC5C,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,IAAI;QACvC,IACE,IAAI,CAAC,OAAO,KAAK,MAAM;YACvB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC1C,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,yBAAyB,EAClD,CAAC;YACD,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC;YAC7B,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wCAAwC;QACxC,yCAAyC;QACzC,oCAAoC;QACpC,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ;YAAE,OAAO,QAAQ,CAAC;QAEzF,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC7B,KAAK,eAAe;gBAClB,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC5B,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC;gBAC9B,OAAO,IAAI,CAAC;YAEd,kCAAkC;YAClC,mCAAmC;YACnC,mCAAmC;YACnC,iBAAiB;YAEjB,KAAK,WAAW;gBACd,IACE,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,QAAQ;oBAC3C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAC9C,CAAC;oBACD,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC;oBAChC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;oBACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBACxB,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC;oBACxB,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC/B,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC;oBACxB,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC/B,GAAG,CACD,gFAAgF,EAChF,OAAO,CACR,CAAC;wBACF,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC;wBAC7B,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC;oBAChC,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;QACrB,GAAG,CAAC,8CAA8C,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IACzE,CAAC;SAAM,CAAC;QACN,GAAG,CAAC,4EAA4E,CAAC,CAAC;QAClF,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC;QAC9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC"}