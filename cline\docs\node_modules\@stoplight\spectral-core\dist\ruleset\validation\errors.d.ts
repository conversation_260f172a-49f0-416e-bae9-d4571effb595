import type { ErrorObject } from 'ajv';
import type { IDiagnostic, JsonPath } from '@stoplight/types';
export declare type RulesetValidationErrorCode = 'generic-validation-error' | 'invalid-ruleset-definition' | 'invalid-parser-options-definition' | 'invalid-alias-definition' | 'invalid-extend-definition' | 'invalid-rule-definition' | 'invalid-override-definition' | 'invalid-function-options' | 'invalid-given-definition' | 'invalid-severity' | 'invalid-format' | 'undefined-function' | 'undefined-alias';
interface IRulesetValidationSingleError extends Pick<IDiagnostic, 'message' | 'path'> {
    readonly code: RulesetValidationErrorCode;
}
export declare class RulesetValidationError extends Error implements IRulesetValidationSingleError {
    readonly code: RulesetValidationErrorCode;
    readonly message: string;
    readonly path: JsonPath;
    constructor(code: RulesetValidationErrorCode, message: string, path: JsonPath);
}
export declare function convertAjvErrors(errors: ErrorObject[]): RulesetValidationError[];
export {};
