import { slugifyWithCounter } from '@sindresorhus/slugify';
import { slugify } from '../../../slugify.js';
import { createMdxJsxAttribute, getTableOfContentsTitle } from '../../lib/remark-utils.js';
import { HEADING_LEVELS } from './remarkHeadingIds.js';
const HEADING_NAMES = ['h1', 'h2', 'h3', 'h4'];
export const remarkExtractTableOfContents = (mdxExtracts) => {
    // slugifyWithCounter adds a counter (eg. slug, slug-2, slug-3) to the end of the slug if the header
    // already exists. No counter is added for the first occurence.
    const slugifyFn = slugifyWithCounter();
    return (tree) => {
        var _a, _b, _c, _d;
        const contents = [];
        let hasTopLayer = false;
        for (let nodeIndex = 0; nodeIndex < tree.children.length; nodeIndex++) {
            const node = tree.children[nodeIndex];
            if (!node)
                continue;
            const isValidHeading = node.type === 'heading' && HEADING_LEVELS.includes(node.depth);
            const isValidMdxHeading = node.type === 'mdxJsxFlowElement' && HEADING_NAMES.includes((_a = node.name) !== null && _a !== void 0 ? _a : '');
            const isValidUpdate = node.type === 'mdxJsxFlowElement' &&
                node.name === 'Update' &&
                node.attributes.some((attr) => 'name' in attr && attr.name === 'label');
            if (!isValidHeading && !isValidMdxHeading && !isValidUpdate) {
                continue;
            }
            let level;
            if ('name' in node && node.name === 'Update') {
                level = 1;
                // @ts-expect-error we're assigning to depth despite the node not containing depth in the type
                node.depth = 1;
            }
            else if ('depth' in node) {
                level = node.depth;
            }
            else if ('name' in node && ((_b = node.name) === null || _b === void 0 ? void 0 : _b[1])) {
                const num = Number(node.name[1]);
                level = !isNaN(num) ? num : undefined;
            }
            const title = getTableOfContentsTitle(node);
            const slug = slugify(title, slugifyFn);
            let mdxJsxAttributes;
            if ('name' in node && node.name === 'Update') {
                mdxJsxAttributes = [...node.attributes, createMdxJsxAttribute('id', slug)];
            }
            else if (level !== undefined) {
                mdxJsxAttributes = [
                    createMdxJsxAttribute('level', level),
                    createMdxJsxAttribute('id', slug),
                    createMdxJsxAttribute('isAtRootLevel', true),
                ];
            }
            // @ts-expect-error we're assigning over 'attributes' if it doesn't exist
            node.attributes = mdxJsxAttributes;
            node.type = 'mdxJsxFlowElement';
            // @ts-expect-error we're assigning over 'name' if it doesn't exist
            node.name = node.name === 'Update' ? 'Update' : 'Heading';
            // @ts-expect-error we've already written to 'depth' and so this should be safe
            const depth = node.depth;
            if (level !== undefined && Number(level) <= 2) {
                hasTopLayer = true;
                contents.push({ title, slug, depth, children: [] });
            }
            else {
                // Account if there is no first layer
                let arrToPushInto = contents;
                if (hasTopLayer) {
                    arrToPushInto = (_d = (_c = contents.at(-1)) === null || _c === void 0 ? void 0 : _c.children) !== null && _d !== void 0 ? _d : [];
                }
                arrToPushInto.push({ title, slug, depth, children: [] });
            }
        }
        if (mdxExtracts) {
            mdxExtracts.tableOfContents = contents;
        }
    };
};
