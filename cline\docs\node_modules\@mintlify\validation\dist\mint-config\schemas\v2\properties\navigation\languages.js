import { z } from 'zod';
import { locales } from '../localization.js';
import { asyncApiSchema } from '../reusable/asyncapi.js';
import { hiddenSchema } from '../reusable/hidden.js';
import { hrefSchema } from '../reusable/href.js';
import { openApiSchema } from '../reusable/openapi.js';
import { anchorsSchema, decoratedAnchorsSchema } from './anchors.js';
import { decoratedDropdownsSchema, dropdownsSchema } from './dropdown.js';
import { globalSchema } from './global.js';
import { decoratedGroupsSchema, groupsSchema } from './groups.js';
import { decoratedPagesSchema, pagesSchema } from './pages.js';
import { decoratedTabsSchema, tabsSchema } from './tabs.js';
import { decoratedVersionsSchema, versionsSchema } from './version.js';
export const baseLanguageSchema = z.object({
    language: z.enum(locales).describe('The name of the language in the ISO 639-1 format'),
    default: z.boolean().optional().describe('Whether this language is the default language'),
    hidden: hiddenSchema.optional(),
});
export const nonRecursiveLanguageSchema = baseLanguageSchema.extend({ href: hrefSchema });
export const languageSchema = z
    .union([
    baseLanguageSchema.extend({
        href: hrefSchema,
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseLanguageSchema.extend({
        versions: z.lazy(() => versionsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseLanguageSchema.extend({
        tabs: z.lazy(() => tabsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseLanguageSchema.extend({
        dropdowns: z.lazy(() => dropdownsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseLanguageSchema.extend({
        anchors: z.lazy(() => anchorsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseLanguageSchema.extend({
        groups: z.lazy(() => groupsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseLanguageSchema.extend({
        pages: z.lazy(() => pagesSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseLanguageSchema.extend({
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
])
    .describe('Organizing by languages');
export const decoratedLanguageSchema = baseLanguageSchema
    .and(z.union([
    z.object({ href: hrefSchema }),
    z.lazy(() => z.object({ versions: decoratedVersionsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ tabs: decoratedTabsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ dropdowns: decoratedDropdownsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ anchors: decoratedAnchorsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ groups: decoratedGroupsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ pages: decoratedPagesSchema, global: globalSchema.optional() })),
]))
    .describe('Organizing by languages');
export const languagesSchema = z
    .array(languageSchema)
    .min(1, 'At least one language must be specified')
    .describe('Organizing by languages');
export const decoratedLanguagesSchema = z
    .array(decoratedLanguageSchema)
    .min(1, 'At least one language must be specified')
    .describe('Organizing by languages');
