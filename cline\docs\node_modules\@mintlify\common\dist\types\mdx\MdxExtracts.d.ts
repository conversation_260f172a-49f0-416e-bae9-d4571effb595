import { Endpoint } from '@mintlify/validation';
import { ChangelogFilter } from './ChangelogFilter.js';
import { MdxCodeExampleData } from './MdxCodeExampleData.js';
import { PanelType } from './PanelType.js';
import { TableOfContentsSectionType } from './TableOfContentsSectionType.js';
export type MdxExtracts = {
    changelogFilters?: ChangelogFilter[];
    tableOfContents?: TableOfContentsSectionType[];
    endpoint?: Endpoint;
    codeExamples?: {
        request?: MdxCodeExampleData;
        response?: MdxCodeExampleData;
    };
    panel?: PanelType;
};
