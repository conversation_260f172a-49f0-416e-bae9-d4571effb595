export { toMdast } from "./lib/index.js";
export type Handle = import("./lib/state.js").Handle;
export type NodeHandle = import("./lib/state.js").NodeHandle;
export type Options = import("./lib/state.js").Options;
export type State = import("./lib/state.js").State;
export { handlers as defaultHandlers, nodeHandlers as defaultNodeHandlers } from "./lib/handlers/index.js";
//# sourceMappingURL=index.d.ts.map