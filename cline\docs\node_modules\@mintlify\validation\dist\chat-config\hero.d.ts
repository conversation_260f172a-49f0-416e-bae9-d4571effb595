import { z } from 'zod';
export declare const heroSchema: z.ZodObject<{
    prompt: z.ZodOptional<z.ZodString>;
    examples: z.ZodOptional<z.ZodU<PERSON>n<[z.<PERSON>od<PERSON>rray<z.ZodString, "many">, z.<PERSON><z.ZodObject<{
        icon: z.ZodOptional<z.ZodString>;
        title: z.ZodString;
        prompt: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        prompt: string;
        title: string;
        icon?: string | undefined;
    }, {
        prompt: string;
        title: string;
        icon?: string | undefined;
    }>, "many">]>>;
}, "strip", z.<PERSON><PERSON>Type<PERSON>ny, {
    prompt?: string | undefined;
    examples?: string[] | {
        prompt: string;
        title: string;
        icon?: string | undefined;
    }[] | undefined;
}, {
    prompt?: string | undefined;
    examples?: string[] | {
        prompt: string;
        title: string;
        icon?: string | undefined;
    }[] | undefined;
}>;
export type HeroSchema = z.infer<typeof heroSchema>;
