{"type": "object", "description": "An object representing a Server Variable for server URL template substitution.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.1.0/specificationExtension.json"}}, "properties": {"enum": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "default": {"type": "string"}, "description": {"type": "string"}, "examples": {"type": "array", "items": {"type": "string"}}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.1.0/serverVariable.json"}