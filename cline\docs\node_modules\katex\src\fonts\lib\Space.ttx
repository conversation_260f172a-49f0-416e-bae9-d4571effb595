<?xml version="1.0" encoding="UTF-8"?>
<ttFont sfntVersion="OTTO" ttLibVersion="3.28">

  <GlyphOrder>
    <!-- The 'id' attribute is only for humans; it is ignored when parsed. -->
    <GlyphID id="0" name=".notdef"/>
    <GlyphID id="1" name="space"/>
    <GlyphID id="2" name="uni00A0"/>
  </GlyphOrder>

  <head>
    <!-- Most of this table will be recalculated by the compiler -->
    <tableVersion value="1.0"/>
    <fontRevision value="1.0"/>
    <checkSumAdjustment value="0xa98c8795"/>
    <magicNumber value="0x5f0f3cf5"/>
    <flags value="00000000 00000011"/>
    <unitsPerEm value="1000"/>
    <created value="Sun Jan 24 23:04:46 2010"/>
    <modified value="Sat May 14 12:26:22 2011"/>
    <xMin value="50"/>
    <yMin value="0"/>
    <xMax value="200"/>
    <yMax value="533"/>
    <macStyle value="00000000 00000000"/>
    <lowestRecPPEM value="8"/>
    <fontDirectionHint value="2"/>
    <indexToLocFormat value="0"/>
    <glyphDataFormat value="0"/>
  </head>

  <hhea>
    <tableVersion value="0x00010000"/>
    <ascent value="800"/>
    <descent value="-200"/>
    <lineGap value="90"/>
    <advanceWidthMax value="250"/>
    <minLeftSideBearing value="50"/>
    <minRightSideBearing value="50"/>
    <xMaxExtent value="200"/>
    <caretSlopeRise value="1"/>
    <caretSlopeRun value="0"/>
    <caretOffset value="0"/>
    <reserved0 value="0"/>
    <reserved1 value="0"/>
    <reserved2 value="0"/>
    <reserved3 value="0"/>
    <metricDataFormat value="0"/>
    <numberOfHMetrics value="1"/>
  </hhea>

  <maxp>
    <tableVersion value="0x5000"/>
    <numGlyphs value="3"/>
  </maxp>

  <OS_2>
    <!-- The fields 'usFirstCharIndex' and 'usLastCharIndex'
         will be recalculated by the compiler -->
    <version value="2"/>
    <xAvgCharWidth value="225"/>
    <usWeightClass value="400"/>
    <usWidthClass value="5"/>
    <fsType value="00000000 00000000"/>
    <ySubscriptXSize value="650"/>
    <ySubscriptYSize value="700"/>
    <ySubscriptXOffset value="0"/>
    <ySubscriptYOffset value="140"/>
    <ySuperscriptXSize value="650"/>
    <ySuperscriptYSize value="700"/>
    <ySuperscriptXOffset value="0"/>
    <ySuperscriptYOffset value="480"/>
    <yStrikeoutSize value="49"/>
    <yStrikeoutPosition value="258"/>
    <sFamilyClass value="0"/>
    <panose>
      <bFamilyType value="0"/>
      <bSerifStyle value="0"/>
      <bWeight value="0"/>
      <bProportion value="0"/>
      <bContrast value="0"/>
      <bStrokeVariation value="0"/>
      <bArmStyle value="0"/>
      <bLetterForm value="0"/>
      <bMidline value="0"/>
      <bXHeight value="0"/>
    </panose>
    <ulUnicodeRange1 value="10000000 00000000 00000000 11101111"/>
    <ulUnicodeRange2 value="00010000 00000000 11101100 11101101"/>
    <ulUnicodeRange3 value="00000000 00000000 00000000 00000000"/>
    <ulUnicodeRange4 value="00000000 00000000 00000000 00000000"/>
    <achVendID value="PfEd"/>
    <fsSelection value="00000000 01000000"/>
    <usFirstCharIndex value="32"/>
    <usLastCharIndex value="160"/>
    <sTypoAscender value="800"/>
    <sTypoDescender value="-200"/>
    <sTypoLineGap value="90"/>
    <usWinAscent value="533"/>
    <usWinDescent value="0"/>
    <ulCodePageRange1 value="00100000 00000000 00000000 10001111"/>
    <ulCodePageRange2 value="01011110 00000011 00000000 00000000"/>
    <sxHeight value="0"/>
    <sCapHeight value="0"/>
    <usDefaultChar value="32"/>
    <usBreakChar value="32"/>
    <usMaxContext value="1"/>
  </OS_2>

  <name>
    <namerecord nameID="0" platformID="3" platEncID="1" langID="0x409">
      Copyright (c) 2009-2010 Design Science, Inc.
Copyright (c) 2014-2018 Khan Academy
    </namerecord>
    <namerecord nameID="1" platformID="3" platEncID="1" langID="0x409">
      *NAME*
    </namerecord>
    <namerecord nameID="2" platformID="3" platEncID="1" langID="0x409">
      *WEIGHT_S*
    </namerecord>
    <namerecord nameID="3" platformID="3" platEncID="1" langID="0x409">
      FontForge 2.0 : *NAME*-*WEIGHT*
    </namerecord>
    <namerecord nameID="4" platformID="3" platEncID="1" langID="0x409">
      *NAME*-*WEIGHT*
    </namerecord>
    <namerecord nameID="5" platformID="3" platEncID="1" langID="0x409">
      Version 1.1
    </namerecord>
    <namerecord nameID="6" platformID="3" platEncID="1" langID="0x409">
      *NAME*-*WEIGHT*
    </namerecord>
    <namerecord nameID="13" platformID="3" platEncID="1" langID="0x409">
      Copyright (c) 2009-2010, Design Science, Inc. (&lt;www.mathjax.org&gt;)
Copyright (c) 2014-2018 Khan Academy (&lt;www.khanacademy.org&gt;),
with Reserved Font Name *NAME*.

This Font Software is licensed under the SIL Open Font License, Version 1.1.
This license available with a FAQ at:
http://scripts.sil.org/OFL
    </namerecord>
    <namerecord nameID="14" platformID="3" platEncID="1" langID="0x409">
      http://scripts.sil.org/OFL
    </namerecord>
  </name>

  <cmap>
    <tableVersion version="0"/>
    <cmap_format_4 platformID="0" platEncID="3" language="0">
      <map code="0x20" name="space"/><!-- SPACE -->
      <map code="0xa0" name="uni00A0"/><!-- NO-BREAK SPACE -->
    </cmap_format_4>
    <cmap_format_4 platformID="3" platEncID="1" language="0">
      <map code="0x20" name="space"/><!-- SPACE -->
      <map code="0xa0" name="uni00A0"/><!-- NO-BREAK SPACE -->
    </cmap_format_4>
  </cmap>

  <post>
    <formatType value="3.0"/>
    <italicAngle value="0.0"/>
    <underlinePosition value="-125"/>
    <underlineThickness value="50"/>
    <isFixedPitch value="0"/>
    <minMemType42 value="0"/>
    <maxMemType42 value="0"/>
    <minMemType1 value="0"/>
    <maxMemType1 value="0"/>
  </post>

  <CFF>
    <major value="1"/>
    <minor value="0"/>
    <CFFFont name="*NAME*-*WEIGHT*">
      <version value="001.001"/>
      <Notice value="Copyright (c) 2009-2010 Design Science, Inc., Copyright (c) 2014-2018 Khan Academy"/>
      <FullName value="*NAME*-*WEIGHT*"/>
      <FamilyName value="*NAME*"/>
      <Weight value="*NORMAL*"/>
      <isFixedPitch value="0"/>
      <ItalicAngle value="0"/>
      <UnderlinePosition value="-150"/>
      <UnderlineThickness value="50"/>
      <PaintType value="0"/>
      <CharstringType value="2"/>
      <FontMatrix value="0.001 0 0 0.001 0 0"/>
      <FontBBox value="50 0 200 533"/>
      <StrokeWidth value="0"/>
      <!-- charset is dumped separately as the 'GlyphOrder' element -->
      <Encoding name="StandardEncoding"/>
      <Private>
        <BlueScale value="0.03963"/>
        <BlueShift value="0"/>
        <BlueFuzz value="1"/>
        <StdHW value="50"/>
        <StdVW value="50"/>
        <ForceBold value="0"/>
        <LanguageGroup value="0"/>
        <ExpansionFactor value="0.06"/>
        <initialRandomSeed value="0"/>
        <defaultWidthX value="250"/>
        <nominalWidthX value="193"/>
      </Private>
      <CharStrings>
        <CharString name=".notdef">
          0 50 433 50 hstem
          50 50 50 50 vstem
          50 hmoveto
          150 533 -150 hlineto
          50 -483 rmoveto
          433 50 -433 vlineto
          endchar
        </CharString>
        <CharString name="space">
          endchar
        </CharString>
        <CharString name="uni00A0">
          endchar
        </CharString>
      </CharStrings>
    </CFFFont>

    <GlobalSubrs>
      <!-- The 'index' attribute is only for humans; it is ignored when parsed. -->
    </GlobalSubrs>
  </CFF>

  <hmtx>
    <mtx name=".notdef" width="250" lsb="50"/>
    <mtx name="space" width="250" lsb="0"/>
    <mtx name="uni00A0" width="250" lsb="0"/>
  </hmtx>

</ttFont>
