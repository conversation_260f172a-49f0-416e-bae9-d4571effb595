{"version": 3, "file": "rulesets.js", "sourceRoot": "", "sources": ["../../../src/ruleset/mergers/rulesets.ts"], "names": [], "mappings": ";;;AAWA,SAAS,YAAY,CACnB,SAAiF;IAEjF,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC7D,CAAC;AAED,SAAS,aAAa,CAAC,UAAoC;IACzD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AACnF,CAAC;AAED,SAAgB,aAAa,CAAC,IAAsB,EAAE,KAAuB,EAAE,UAAmB;IAChG,MAAM,OAAO,GAAqB;QAChC,GAAG,IAAI;QACP,GAAG,KAAK;KACT,CAAC;IAEF,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,OAAO,EAAE;QAChD,MAAM,eAAe,GAAG,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACtD,OAAsF,CAAC,OAAO,GAAG;YAChG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAC9E,GAAG,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CACpD;YACD,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC1E,CAAC;KACH;IAED,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,KAAK,EAAE;QAC1C,OAAsF,CAAC,OAAO,GAAG;YAChG,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,KAAK,CAAC,OAAO;SACjB,CAAC;KACH;IAED,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC;QAAE,OAAO,OAA4B,CAAC;IAEnF,IAAI,UAAU,EAAE;QACb,OAA4F,CAAC,KAAK,GAAG;YACpG,GAAG,IAAI,CAAC,KAAK;YACb,GAAG,KAAK,CAAC,KAAK;SACf,CAAC;KACH;SAAM;QACL,MAAM,CAAC,GAAG,OAAsF,CAAC;QAEjG,IAAI,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,EAAE;YACrB,CAAC,CAAC,OAAO,GAAG,IAAyB,CAAC;SACvC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;YACnC,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAyB,CAAC,CAAC;SACvD;aAAM;YACL,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,OAA4B,EAAE,IAAyB,CAAC,CAAC;SACzE;KACF;IAED,OAAO,OAA4B,CAAC;AACtC,CAAC;AA3CD,sCA2CC"}