{"additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/specificationExtension.json"}}, "properties": {"description": {"type": "string", "description": "A brief description of the parameter. This could contain examples of use. GitHub Flavored Markdown is allowed."}, "name": {"type": "string", "description": "The name of the parameter."}, "schema": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/schema.json"}, "$ref": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/ReferenceObject.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc1/parameter.json"}