const HALF_INDENT = '  ';
const INDENT = '    ';
export const formatIssue = (issue) => formatIssueWithIndent(issue);
export const formatIssueWithIndent = (issue, indent = 0) => {
    const pathComponents = issue.path.map((value) => typeof value === 'string' ? `.${value}` : `[${value}]`);
    const lines = issue.code === 'invalid_union'
        ? issue.unionErrors.flatMap((unionError, i) => {
            const unionErrorLines = unionError.issues.map((unionIssue) => formatIssueWithIndent(unionIssue, indent + 1));
            unionErrorLines.unshift(`${INDENT.repeat(indent)}${HALF_INDENT}${i + 1}:`);
            return unionErrorLines;
        })
        : [];
    lines.unshift(`${INDENT.repeat(indent)}\#${pathComponents.join('')}: ${issue.message}`);
    return lines.join('\n');
};
