{"version": 3, "file": "Frame.js", "sourceRoot": "", "sources": ["../../src/components/Frame.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,MAAM,UAAU,kBAAkB,CAChC,IAAc,EACd,CAAgB,EAChB,EAAkB;IAElB,IACE,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAChB,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,CACzE,CAAC;QACJ,CAAC,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC;QAEzD,OAAO,SAAS,CAAC;IAEnB,MAAM,OAAO,GAAY;QACvB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAA0B;KAC3F,CAAC;IAEF,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IACnC,IAAI,OAAO;QAAE,OAAO,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAElD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,IAAc,EACd,CAAgB,EAChB,EAAkB;IAElB,IACE,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAChB,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,CACzE,CAAC;QACJ,CAAC,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC;QAEzD,OAAO,SAAS,CAAC;IAEnB,MAAM,OAAO,GAAY;QACvB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAA0B;KAC3F,CAAC;IAEF,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IACnC,IAAI,OAAO;QAAE,OAAO,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAElD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,IAAc,EACd,CAAgB,EAChB,EAAkB;IAElB,IACE,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAChB,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,CACzE,CAAC;QACJ,CAAC,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC;QAEzD,OAAO,SAAS,CAAC;IAEnB,MAAM,OAAO,GAAY;QACvB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAA0B;KAC3F,CAAC;IAEF,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IACnC,IAAI,OAAO;QAAE,OAAO,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAElD,OAAO,OAAO,CAAC;AACjB,CAAC"}