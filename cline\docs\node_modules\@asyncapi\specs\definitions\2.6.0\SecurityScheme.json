{"description": "Defines a security scheme that can be used by the operations.", "oneOf": [{"$ref": "http://asyncapi.com/definitions/2.6.0/userPassword.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/apiKey.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/X509.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/symmetricEncryption.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/asymmetricEncryption.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/HTTPSecurityScheme.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/oauth2Flows.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/openIdConnect.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/SaslSecurityScheme.json"}], "example": {"$ref": "http://asyncapi.com/examples/2.6.0/SecurityScheme.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/SecurityScheme.json"}