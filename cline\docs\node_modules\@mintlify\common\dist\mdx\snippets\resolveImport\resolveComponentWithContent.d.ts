import type { Root } from 'mdast';
/**
 *
 * @param tree The tree to inject into
 * @param componentName The component name we are injecting into
 * @param snippet The snippet to inject in place of the component
 * @param exportMap The export map of the snippet
 */
export declare const resolveComponentWithContent: (tree: Root, componentName: string, snippet: Root, exportMap: Record<string, string>) => Promise<Root>;
