import { z } from 'zod';
export declare const gradientSchema: z.ZodObject<{
    from: z.ZodString;
    via: z.ZodOptional<z.ZodString>;
    to: z.ZodString;
}, "strip", z.ZodTypeAny, {
    from: string;
    to: string;
    via?: string | undefined;
}, {
    from: string;
    to: string;
    via?: string | undefined;
}>;
export declare const anchorColorSchema: z.<PERSON><[z.ZodString, z.ZodObject<{
    from: z.ZodString;
    via: z.ZodOptional<z.ZodString>;
    to: z.ZodString;
}, "strict", z.ZodTypeAny, {
    from: string;
    to: string;
    via?: string | undefined;
}, {
    from: string;
    to: string;
    via?: string | undefined;
}>]>;
