import { Analytics, MintConfig, InkeepType, FeatureFlags, LlmSupportedLocaleType } from '@mintlify/models';
import { DocsConfig } from '../../mint-config/schemas/v2/index.js';
import { AssistantType } from './assistant.js';
import { Auth, PreviewDeploymentAuth, UserAuth } from './auth.js';
import { DeploymentEntitlements } from './deploymentEntitlements.js';
import { DeploymentFeedbackType } from './deploymentFeedback.js';
import { GitlabSource, GithubSource } from './gitSource.js';
import { GitSource } from './gitSource.js';
import { MCPType } from './mcp.js';
import { SourceCheckType } from './sourceCheck.js';
import { StripeDeploymentDataType } from './stripe.js';
import { TrieveType } from './trieve.js';
export type DeploymentType = {
    name: string;
    subdomain: string;
    orgId: string;
    createdAt?: string;
    gitSource: GitSource;
    sourceChecks?: SourceCheckType[];
    analytics?: Analytics;
    basePath: string;
    customDomains?: string[];
    mintConfig?: MintConfig;
    docsConfig: DocsConfig;
    inkeep?: InkeepType;
    featureFlags?: FeatureFlags;
    isWhiteLabeled?: boolean;
    trieve?: TrieveType;
    disableAiChat?: boolean;
    auth?: Auth;
    previewDeploymentAuth?: PreviewDeploymentAuth;
    userAuth?: UserAuth;
    stripe?: StripeDeploymentDataType;
    preview?: {
        mainDeploymentId?: string;
    };
    allowedAutomatedLanguages?: LlmSupportedLocaleType[];
    enabledContinuousTranslations?: LlmSupportedLocaleType[];
    entitlements?: DeploymentEntitlements;
    feedback?: DeploymentFeedbackType;
    mcp?: MCPType;
    assistant?: AssistantType;
};
export type StaticPropsDeploymentAuthType = Pick<Auth, 'partial'>;
export type StaticPropsDeploymentType = Pick<DeploymentType, 'basePath' | 'disableAiChat' | 'entitlements' | 'featureFlags' | 'feedback' | 'inkeep' | 'isWhiteLabeled' | 'mintConfig' | 'subdomain' | 'trieve' | 'userAuth' | 'docsConfig' | 'assistant'> & {
    auth?: StaticPropsDeploymentAuthType;
    gitSource: GitSource;
};
export type GithubDeploymentType = DeploymentType & {
    gitSource: GithubSource;
};
export type GitlabDeploymentType = DeploymentType & {
    gitSource: GitlabSource;
};
export * from './auth.js';
export * from './deploymentEntitlements.js';
export * from './deploymentFeedback.js';
export * from './gitSource.js';
export * from './sourceCheck.js';
export * from './stripe.js';
export * from './trieve.js';
export * from './assistant.js';
