import { z } from 'zod';
import { nonRecursiveAnchorSchema } from './anchors.js';
import { nonRecursiveDropdownSchema } from './dropdown.js';
import { nonRecursiveLanguageSchema } from './languages.js';
import { nonRecursiveTabSchema } from './tabs.js';
import { nonRecursiveVersionSchema } from './version.js';
export const globalSchema = z
    .lazy(() => z
    .object({
    languages: z.array(nonRecursiveLanguageSchema).optional(),
    versions: z.array(nonRecursiveVersionSchema).optional(),
    tabs: z.array(nonRecursiveTabSchema).optional(),
    dropdowns: z.array(nonRecursiveDropdownSchema).optional(),
    anchors: z.array(nonRecursiveAnchorSchema).optional(),
})
    .strict())
    .describe('Add external links that will appear on all sections and pages irregardless of navigation nesting');
