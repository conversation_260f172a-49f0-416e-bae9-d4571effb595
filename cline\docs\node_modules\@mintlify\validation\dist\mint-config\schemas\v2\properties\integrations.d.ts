import { z } from 'zod';
export declare const integrationsSchema: z.ZodObject<{
    amplitude: z.ZodOptional<z.ZodObject<{
        apiKey: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        apiKey: string;
    }, {
        apiKey: string;
    }>>;
    clearbit: z.ZodOptional<z.ZodObject<{
        publicApiKey: z.ZodString;
    }, "strip", z.Zod<PERSON>ype<PERSON>ny, {
        publicApiKey: string;
    }, {
        publicApiKey: string;
    }>>;
    fathom: z.ZodOptional<z.ZodObject<{
        siteId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        siteId: string;
    }, {
        siteId: string;
    }>>;
    frontchat: z.ZodOptional<z.ZodObject<{
        snippetId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        snippetId: string;
    }, {
        snippetId: string;
    }>>;
    ga4: z.ZodOptional<z.ZodObject<{
        measurementId: z.ZodString;
    }, "strip", z.Z<PERSON>ype<PERSON>, {
        measurementId: string;
    }, {
        measurementId: string;
    }>>;
    gtm: z.ZodOptional<z.ZodObject<{
        tagId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        tagId: string;
    }, {
        tagId: string;
    }>>;
    heap: z.ZodOptional<z.ZodObject<{
        appId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        appId: string;
    }, {
        appId: string;
    }>>;
    hotjar: z.ZodOptional<z.ZodObject<{
        hjid: z.ZodString;
        hjsv: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        hjid: string;
        hjsv: string;
    }, {
        hjid: string;
        hjsv: string;
    }>>;
    intercom: z.ZodOptional<z.ZodObject<{
        appId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        appId: string;
    }, {
        appId: string;
    }>>;
    koala: z.ZodOptional<z.ZodObject<{
        publicApiKey: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        publicApiKey: string;
    }, {
        publicApiKey: string;
    }>>;
    logrocket: z.ZodOptional<z.ZodObject<{
        appId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        appId: string;
    }, {
        appId: string;
    }>>;
    mixpanel: z.ZodOptional<z.ZodObject<{
        projectToken: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        projectToken: string;
    }, {
        projectToken: string;
    }>>;
    osano: z.ZodOptional<z.ZodObject<{
        scriptSource: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        scriptSource: string;
    }, {
        scriptSource: string;
    }>>;
    pirsch: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
    }, {
        id: string;
    }>>;
    posthog: z.ZodOptional<z.ZodObject<{
        apiKey: z.ZodString;
        apiHost: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        apiKey: string;
        apiHost?: string | undefined;
    }, {
        apiKey: string;
        apiHost?: string | undefined;
    }>>;
    plausible: z.ZodOptional<z.ZodObject<{
        domain: z.ZodEffects<z.ZodString, string, string>;
        server: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    }, "strip", z.ZodTypeAny, {
        domain: string;
        server?: string | undefined;
    }, {
        domain: string;
        server?: string | undefined;
    }>>;
    segment: z.ZodOptional<z.ZodObject<{
        key: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        key: string;
    }, {
        key: string;
    }>>;
    telemetry: z.ZodOptional<z.ZodObject<{
        enabled: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        enabled?: boolean | undefined;
    }, {
        enabled?: boolean | undefined;
    }>>;
    cookies: z.ZodOptional<z.ZodObject<{
        key: z.ZodOptional<z.ZodString>;
        value: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        key?: string | undefined;
        value?: string | undefined;
    }, {
        key?: string | undefined;
        value?: string | undefined;
    }>>;
}, "strict", z.ZodTypeAny, {
    amplitude?: {
        apiKey: string;
    } | undefined;
    clearbit?: {
        publicApiKey: string;
    } | undefined;
    fathom?: {
        siteId: string;
    } | undefined;
    frontchat?: {
        snippetId: string;
    } | undefined;
    ga4?: {
        measurementId: string;
    } | undefined;
    gtm?: {
        tagId: string;
    } | undefined;
    heap?: {
        appId: string;
    } | undefined;
    hotjar?: {
        hjid: string;
        hjsv: string;
    } | undefined;
    intercom?: {
        appId: string;
    } | undefined;
    koala?: {
        publicApiKey: string;
    } | undefined;
    logrocket?: {
        appId: string;
    } | undefined;
    mixpanel?: {
        projectToken: string;
    } | undefined;
    osano?: {
        scriptSource: string;
    } | undefined;
    pirsch?: {
        id: string;
    } | undefined;
    posthog?: {
        apiKey: string;
        apiHost?: string | undefined;
    } | undefined;
    plausible?: {
        domain: string;
        server?: string | undefined;
    } | undefined;
    segment?: {
        key: string;
    } | undefined;
    telemetry?: {
        enabled?: boolean | undefined;
    } | undefined;
    cookies?: {
        key?: string | undefined;
        value?: string | undefined;
    } | undefined;
}, {
    amplitude?: {
        apiKey: string;
    } | undefined;
    clearbit?: {
        publicApiKey: string;
    } | undefined;
    fathom?: {
        siteId: string;
    } | undefined;
    frontchat?: {
        snippetId: string;
    } | undefined;
    ga4?: {
        measurementId: string;
    } | undefined;
    gtm?: {
        tagId: string;
    } | undefined;
    heap?: {
        appId: string;
    } | undefined;
    hotjar?: {
        hjid: string;
        hjsv: string;
    } | undefined;
    intercom?: {
        appId: string;
    } | undefined;
    koala?: {
        publicApiKey: string;
    } | undefined;
    logrocket?: {
        appId: string;
    } | undefined;
    mixpanel?: {
        projectToken: string;
    } | undefined;
    osano?: {
        scriptSource: string;
    } | undefined;
    pirsch?: {
        id: string;
    } | undefined;
    posthog?: {
        apiKey: string;
        apiHost?: string | undefined;
    } | undefined;
    plausible?: {
        domain: string;
        server?: string | undefined;
    } | undefined;
    segment?: {
        key: string;
    } | undefined;
    telemetry?: {
        enabled?: boolean | undefined;
    } | undefined;
    cookies?: {
        key?: string | undefined;
        value?: string | undefined;
    } | undefined;
}>;
