import { SpecificationExtensionsModel } from './mixins';
import { Channel } from './channel';
import { Message } from './message';
import { Schema } from './schema';
import { SecurityScheme } from './security-scheme';
import { Server } from './server';
import { ChannelParameter } from './channel-parameter';
import { CorrelationId } from './correlation-id';
import { OperationTrait } from './operation-trait';
import { MessageTrait } from './message-trait';
import { ServerVariable } from './server-variable';
import type { v2 } from '../spec-types';
export declare class Components extends SpecificationExtensionsModel<v2.ComponentsObject> {
    hasChannels(): boolean;
    channels(): Record<string, Channel>;
    channel(name: string): Channel | null;
    hasMessages(): boolean;
    messages(): Record<string, Message>;
    message(name: string): Message | null;
    hasSchemas(): boolean;
    schemas(): Record<string, Schema>;
    schema(name: string): Schema | null;
    hasSecuritySchemes(): boolean;
    securitySchemes(): Record<string, SecurityScheme>;
    securityScheme(name: string): SecurityScheme | null;
    hasServers(): boolean;
    servers(): Record<string, Server>;
    server(name: string): Server | null;
    hasParameters(): boolean;
    parameters(): Record<string, ChannelParameter>;
    parameter(name: string): ChannelParameter | null;
    hasCorrelationIds(): boolean;
    correlationIds(): Record<string, CorrelationId>;
    correlationId(name: string): CorrelationId | null;
    hasOperationTraits(): boolean;
    operationTraits(): Record<string, OperationTrait<unknown>>;
    operationTrait(name: string): OperationTrait<unknown> | null;
    hasMessageTraits(): boolean;
    messageTraits(): Record<string, MessageTrait<v2.MessageTraitObject>>;
    messageTrait(name: string): MessageTrait<v2.MessageTraitObject>;
    hasServerVariables(): boolean;
    serverVariables(): Record<string, ServerVariable>;
    serverVariable(name: string): ServerVariable | null;
}
