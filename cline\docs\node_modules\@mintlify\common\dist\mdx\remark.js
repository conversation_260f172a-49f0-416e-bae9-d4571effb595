import { remark } from 'remark';
import remarkFrontmatter from 'remark-frontmatter';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import remarkMdx from 'remark-mdx';
import remarkStringify from 'remark-stringify';
import { getJsxEsmTree } from './snippets/getJsxEsmTree.js';
import { isJsxFile } from './snippets/isJsxFile.js';
export const coreRemarkMdxPlugins = [
    remarkMdx,
    remarkGfm,
    [remarkFrontmatter, ['yaml', 'toml']],
    remarkMath,
];
export const coreRemark = remark().use(coreRemarkMdxPlugins).freeze();
export const getAST = (str, filePath) => {
    if (isJsxFile(filePath)) {
        return getJsxEsmTree(str);
    }
    return coreRemark().parse(str);
};
export const stringifyTree = (tree) => coreRemark().use(remarkStringify).stringify(tree);
