export type FooterLink = {
    label: string;
    url: string;
};
export type FooterLinksColumn = {
    title?: string;
    links: FooterLink[];
};
export type FooterSocial = {
    type: FooterSocialKeyType;
    url: string;
};
export declare const footerSocialKeys: readonly ["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"];
export type FooterSocialKeyType = (typeof footerSocialKeys)[number];
export type FooterSocials = FooterSocial[] | {
    [key in FooterSocialKeyType]?: string;
};
export type Footer = {
    socials?: FooterSocials;
    links?: FooterLinksColumn[];
};
