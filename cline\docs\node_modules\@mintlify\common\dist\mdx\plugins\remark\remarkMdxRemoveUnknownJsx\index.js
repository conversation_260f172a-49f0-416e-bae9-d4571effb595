import { map } from 'unist-util-map';
import { findExportedNodes, isMdxJsxFlowElementHast } from '../../../lib/index.js';
import { createCommentNode } from './createCommentNode.js';
export const remarkMdxRemoveUnknownJsx = (allowedComponents) => (tree) => {
    const exportedComponentNames = findExportedNodes(tree, 'ArrowFunctionExpression');
    return map(tree, (node) => {
        if (isMdxJsxFlowElementHast(node)) {
            if (node.name &&
                Array.isArray(allowedComponents) &&
                !allowedComponents.includes(node.name) &&
                !exportedComponentNames.includes(node.name)) {
                return createCommentNode(node.name);
            }
        }
        return node;
    });
};
