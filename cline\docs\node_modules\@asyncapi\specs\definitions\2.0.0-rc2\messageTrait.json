{"type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/specificationExtension.json"}}, "properties": {"schemaFormat": {"type": "string"}, "contentType": {"type": "string"}, "headers": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/schema.json"}]}, "correlationId": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/correlationId.json"}]}, "tags": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/tag.json"}, "uniqueItems": true}, "summary": {"type": "string", "description": "A brief summary of the message."}, "name": {"type": "string", "description": "Name of the message."}, "title": {"type": "string", "description": "A human-friendly title for the message."}, "description": {"type": "string", "description": "A longer description of the message. CommonMark is allowed."}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/externalDocs.json"}, "deprecated": {"type": "boolean", "default": false}, "examples": {"type": "array", "items": {"type": "object"}}, "bindings": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/bindingsObject.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc2/messageTrait.json"}