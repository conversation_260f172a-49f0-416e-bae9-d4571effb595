{"type": "object", "description": "An object to hold a set of reusable objects for different aspects of the AsyncAPI Specification.", "additionalProperties": false, "properties": {"schemas": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/schemas.json"}, "messages": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/messages.json"}, "securitySchemes": {"type": "object", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/SecurityScheme.json"}]}}}, "parameters": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/parameters.json"}, "correlationIds": {"type": "object", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/correlationId.json"}]}}}, "traits": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/traits.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc1/components.json"}