import type { Navigation, DecoratedNavigationPage } from '@mintlify/models';
import { NavigationConfig } from '@mintlify/validation';
export declare const updateGeneratedNav: (pages: Record<string, DecoratedNavigationPage>, configNav: Navigation) => Promise<void>;
export declare const updateGeneratedDocsNav: (pages: Record<string, DecoratedNavigationPage>, docsConfigNav: NavigationConfig) => Promise<void>;
