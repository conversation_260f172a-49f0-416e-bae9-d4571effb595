"use strict";
/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Permissions = exports.PermissionsCommandSchema = void 0;
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.5.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck Some types may be circular.
const zod_1 = __importDefault(require("zod"));
exports.PermissionsCommandSchema = zod_1.default.lazy(() => Permissions.SetPermissionSchema);
var Permissions;
(function (Permissions) {
    Permissions.PermissionDescriptorSchema = zod_1.default.lazy(() => zod_1.default.object({
        name: zod_1.default.string(),
    }));
})(Permissions || (exports.Permissions = Permissions = {}));
(function (Permissions) {
    Permissions.PermissionStateSchema = zod_1.default.lazy(() => zod_1.default.enum(['granted', 'denied', 'prompt']));
})(Permissions || (exports.Permissions = Permissions = {}));
(function (Permissions) {
    Permissions.SetPermissionSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('permissions.setPermission'),
        params: Permissions.SetPermissionParametersSchema,
    }));
})(Permissions || (exports.Permissions = Permissions = {}));
(function (Permissions) {
    Permissions.SetPermissionParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        descriptor: Permissions.PermissionDescriptorSchema,
        state: Permissions.PermissionStateSchema,
        origin: zod_1.default.string(),
        userContext: zod_1.default.string().optional(),
    }));
})(Permissions || (exports.Permissions = Permissions = {}));
//# sourceMappingURL=webdriver-bidi-permissions.js.map