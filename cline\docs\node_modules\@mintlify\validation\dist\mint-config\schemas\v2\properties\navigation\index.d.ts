import { z } from 'zod';
import { AnchorConfig, DecoratedAnchorConfig } from './anchors.js';
import { DecoratedDropdownConfig, DropdownConfig } from './dropdown.js';
import { DecoratedGroupConfig, GroupConfig } from './groups.js';
import { DecoratedLanguageConfig, LanguageConfig } from './languages.js';
import { DecoratedTabConfig, TabConfig } from './tabs.js';
import { DecoratedVersionConfig, VersionConfig } from './version.js';
export declare const navigationSchema: z.ZodUnion<[z.ZodObject<{
    global: z.ZodOptional<z.ZodType<import("./divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("./divisionNav.js").GlobalNavigation>>;
    languages: z.ZodArray<z.ZodType<import("./divisionNav.js").LanguageNavigation<"default">, z.ZodTypeDef, import("./divisionNav.js").LanguageNavigation<"default">>, "many">;
}, "strip", z.ZodTypeAny, {
    languages: import("./divisionNav.js").LanguageNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}, {
    languages: import("./divisionNav.js").LanguageNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}>, z.ZodObject<{
    global: z.ZodOptional<z.ZodType<import("./divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("./divisionNav.js").GlobalNavigation>>;
    versions: z.ZodArray<z.ZodType<import("./divisionNav.js").VersionNavigation<"default">, z.ZodTypeDef, import("./divisionNav.js").VersionNavigation<"default">>, "many">;
}, "strip", z.ZodTypeAny, {
    versions: import("./divisionNav.js").VersionNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}, {
    versions: import("./divisionNav.js").VersionNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}>, z.ZodObject<{
    global: z.ZodOptional<z.ZodType<import("./divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("./divisionNav.js").GlobalNavigation>>;
    tabs: z.ZodArray<z.ZodType<import("./divisionNav.js").TabNavigation<"default">, z.ZodTypeDef, import("./divisionNav.js").TabNavigation<"default">>, "many">;
}, "strip", z.ZodTypeAny, {
    tabs: import("./divisionNav.js").TabNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}, {
    tabs: import("./divisionNav.js").TabNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}>, z.ZodObject<{
    global: z.ZodOptional<z.ZodType<import("./divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("./divisionNav.js").GlobalNavigation>>;
    dropdowns: z.ZodArray<z.ZodType<import("./divisionNav.js").DropdownNavigation<"default">, z.ZodTypeDef, import("./divisionNav.js").DropdownNavigation<"default">>, "many">;
}, "strip", z.ZodTypeAny, {
    dropdowns: import("./divisionNav.js").DropdownNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}, {
    dropdowns: import("./divisionNav.js").DropdownNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}>, z.ZodObject<{
    global: z.ZodOptional<z.ZodType<import("./divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("./divisionNav.js").GlobalNavigation>>;
    anchors: z.ZodArray<z.ZodType<import("./divisionNav.js").AnchorNavigation<"default">, z.ZodTypeDef, import("./divisionNav.js").AnchorNavigation<"default">>, "many">;
}, "strip", z.ZodTypeAny, {
    anchors: import("./divisionNav.js").AnchorNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}, {
    anchors: import("./divisionNav.js").AnchorNavigation<"default">[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}>, z.ZodObject<{
    global: z.ZodOptional<z.ZodType<import("./divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("./divisionNav.js").GlobalNavigation>>;
    groups: z.ZodArray<z.ZodUnion<[z.ZodObject<{
        icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
            style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
            name: z.ZodEffects<z.ZodString, string, string>;
            library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }>]>>;
        group: z.ZodString;
        tag: z.ZodOptional<z.ZodString>;
        hidden: z.ZodOptional<z.ZodBoolean>;
        root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        openapi: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
            source: z.ZodEffects<z.ZodString, string, string>;
            directory: z.ZodOptional<z.ZodString>;
        }, "strict", z.ZodTypeAny, {
            source: string;
            directory?: string | undefined;
        }, {
            source: string;
            directory?: string | undefined;
        }>]>;
    }, "strip", z.ZodTypeAny, {
        openapi: (string | string[] | {
            source: string;
            directory?: string | undefined;
        }) & (string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined);
        group: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    }, {
        openapi: (string | string[] | {
            source: string;
            directory?: string | undefined;
        }) & (string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined);
        group: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    }>, z.ZodObject<{
        icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
            style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
            name: z.ZodEffects<z.ZodString, string, string>;
            library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }>]>>;
        group: z.ZodString;
        tag: z.ZodOptional<z.ZodString>;
        hidden: z.ZodOptional<z.ZodBoolean>;
        root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        asyncapi: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
            source: z.ZodEffects<z.ZodString, string, string>;
            directory: z.ZodOptional<z.ZodString>;
        }, "strict", z.ZodTypeAny, {
            source: string;
            directory?: string | undefined;
        }, {
            source: string;
            directory?: string | undefined;
        }>]>;
    }, "strip", z.ZodTypeAny, {
        group: string;
        asyncapi: (string | string[] | {
            source: string;
            directory?: string | undefined;
        }) & (string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined);
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    }, {
        group: string;
        asyncapi: (string | string[] | {
            source: string;
            directory?: string | undefined;
        }) & (string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined);
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    }>, z.ZodObject<{
        icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
            style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
            name: z.ZodEffects<z.ZodString, string, string>;
            library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }>]>>;
        group: z.ZodString;
        tag: z.ZodOptional<z.ZodString>;
        hidden: z.ZodOptional<z.ZodBoolean>;
        root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        pages: z.ZodLazy<z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">>;
    }, "strip", z.ZodTypeAny, {
        group: string;
        pages: any[];
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    }, {
        group: string;
        pages: any[];
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    }>]>, "many">;
}, "strip", z.ZodTypeAny, {
    groups: ({
        openapi: (string | string[] | {
            source: string;
            directory?: string | undefined;
        }) & (string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined);
        group: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    } | {
        group: string;
        asyncapi: (string | string[] | {
            source: string;
            directory?: string | undefined;
        }) & (string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined);
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    } | {
        group: string;
        pages: any[];
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    })[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}, {
    groups: ({
        openapi: (string | string[] | {
            source: string;
            directory?: string | undefined;
        }) & (string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined);
        group: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    } | {
        group: string;
        asyncapi: (string | string[] | {
            source: string;
            directory?: string | undefined;
        }) & (string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined);
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    } | {
        group: string;
        pages: any[];
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        hidden?: boolean | undefined;
        root?: string | undefined;
    })[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}>, z.ZodObject<{
    global: z.ZodOptional<z.ZodType<import("./divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("./divisionNav.js").GlobalNavigation>>;
    pages: z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">;
}, "strip", z.ZodTypeAny, {
    pages: any[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}, {
    pages: any[];
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}>]>;
export declare const decoratedNavigationSchema: z.ZodIntersection<z.ZodUnion<[z.ZodObject<{
    languages: z.ZodArray<z.ZodType<import("./divisionNav.js").LanguageNavigation<"decorated">, z.ZodTypeDef, import("./divisionNav.js").LanguageNavigation<"decorated">>, "many">;
}, "strip", z.ZodTypeAny, {
    languages: import("./divisionNav.js").LanguageNavigation<"decorated">[];
}, {
    languages: import("./divisionNav.js").LanguageNavigation<"decorated">[];
}>, z.ZodObject<{
    versions: z.ZodArray<z.ZodType<import("./divisionNav.js").VersionNavigation<"decorated">, z.ZodTypeDef, import("./divisionNav.js").VersionNavigation<"decorated">>, "many">;
}, "strip", z.ZodTypeAny, {
    versions: import("./divisionNav.js").VersionNavigation<"decorated">[];
}, {
    versions: import("./divisionNav.js").VersionNavigation<"decorated">[];
}>, z.ZodObject<{
    tabs: z.ZodArray<z.ZodType<import("./divisionNav.js").TabNavigation<"decorated">, z.ZodTypeDef, import("./divisionNav.js").TabNavigation<"decorated">>, "many">;
}, "strip", z.ZodTypeAny, {
    tabs: import("./divisionNav.js").TabNavigation<"decorated">[];
}, {
    tabs: import("./divisionNav.js").TabNavigation<"decorated">[];
}>, z.ZodObject<{
    dropdowns: z.ZodArray<z.ZodType<import("./divisionNav.js").DropdownNavigation<"decorated">, z.ZodTypeDef, import("./divisionNav.js").DropdownNavigation<"decorated">>, "many">;
}, "strip", z.ZodTypeAny, {
    dropdowns: import("./divisionNav.js").DropdownNavigation<"decorated">[];
}, {
    dropdowns: import("./divisionNav.js").DropdownNavigation<"decorated">[];
}>, z.ZodObject<{
    anchors: z.ZodArray<z.ZodType<import("./divisionNav.js").AnchorNavigation<"decorated">, z.ZodTypeDef, import("./divisionNav.js").AnchorNavigation<"decorated">>, "many">;
}, "strip", z.ZodTypeAny, {
    anchors: import("./divisionNav.js").AnchorNavigation<"decorated">[];
}, {
    anchors: import("./divisionNav.js").AnchorNavigation<"decorated">[];
}>, z.ZodObject<{
    groups: z.ZodArray<z.ZodObject<{
        group: z.ZodString;
        icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
            style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
            name: z.ZodEffects<z.ZodString, string, string>;
            library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }>]>>;
        hidden: z.ZodOptional<z.ZodBoolean>;
        root: z.ZodOptional<z.ZodObject<{
            href: z.ZodString;
            title: z.ZodString;
            sidebarTitle: z.ZodOptional<z.ZodString>;
            description: z.ZodOptional<z.ZodString>;
            api: z.ZodOptional<z.ZodString>;
            openapi: z.ZodOptional<z.ZodString>;
            asyncapi: z.ZodOptional<z.ZodString>;
            contentType: z.ZodOptional<z.ZodString>;
            authMethod: z.ZodOptional<z.ZodString>;
            auth: z.ZodOptional<z.ZodString>;
            version: z.ZodOptional<z.ZodString>;
            mode: z.ZodOptional<z.ZodString>;
            hideFooterPagination: z.ZodOptional<z.ZodBoolean>;
            authors: z.ZodOptional<z.ZodUnknown>;
            lastUpdatedDate: z.ZodOptional<z.ZodString>;
            createdDate: z.ZodOptional<z.ZodString>;
            'openapi-schema': z.ZodOptional<z.ZodString>;
            icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
                style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: z.ZodEffects<z.ZodString, string, string>;
                library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            tag: z.ZodOptional<z.ZodString>;
            url: z.ZodOptional<z.ZodString>;
            hideApiMarker: z.ZodOptional<z.ZodBoolean>;
            noindex: z.ZodOptional<z.ZodBoolean>;
            isPublic: z.ZodOptional<z.ZodBoolean>;
            public: z.ZodOptional<z.ZodBoolean>;
            deprecated: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            href: string;
            title: string;
            sidebarTitle?: string | undefined;
            description?: string | undefined;
            api?: string | undefined;
            openapi?: string | undefined;
            asyncapi?: string | undefined;
            contentType?: string | undefined;
            authMethod?: string | undefined;
            auth?: string | undefined;
            version?: string | undefined;
            mode?: string | undefined;
            hideFooterPagination?: boolean | undefined;
            authors?: unknown;
            lastUpdatedDate?: string | undefined;
            createdDate?: string | undefined;
            'openapi-schema'?: string | undefined;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            url?: string | undefined;
            hideApiMarker?: boolean | undefined;
            noindex?: boolean | undefined;
            isPublic?: boolean | undefined;
            public?: boolean | undefined;
            deprecated?: boolean | undefined;
        }, {
            href: string;
            title: string;
            sidebarTitle?: string | undefined;
            description?: string | undefined;
            api?: string | undefined;
            openapi?: string | undefined;
            asyncapi?: string | undefined;
            contentType?: string | undefined;
            authMethod?: string | undefined;
            auth?: string | undefined;
            version?: string | undefined;
            mode?: string | undefined;
            hideFooterPagination?: boolean | undefined;
            authors?: unknown;
            lastUpdatedDate?: string | undefined;
            createdDate?: string | undefined;
            'openapi-schema'?: string | undefined;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            url?: string | undefined;
            hideApiMarker?: boolean | undefined;
            noindex?: boolean | undefined;
            isPublic?: boolean | undefined;
            public?: boolean | undefined;
            deprecated?: boolean | undefined;
        }>>;
        pages: z.ZodLazy<z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">>;
        tag: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        group: string;
        pages: any[];
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        hidden?: boolean | undefined;
        root?: {
            href: string;
            title: string;
            sidebarTitle?: string | undefined;
            description?: string | undefined;
            api?: string | undefined;
            openapi?: string | undefined;
            asyncapi?: string | undefined;
            contentType?: string | undefined;
            authMethod?: string | undefined;
            auth?: string | undefined;
            version?: string | undefined;
            mode?: string | undefined;
            hideFooterPagination?: boolean | undefined;
            authors?: unknown;
            lastUpdatedDate?: string | undefined;
            createdDate?: string | undefined;
            'openapi-schema'?: string | undefined;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            url?: string | undefined;
            hideApiMarker?: boolean | undefined;
            noindex?: boolean | undefined;
            isPublic?: boolean | undefined;
            public?: boolean | undefined;
            deprecated?: boolean | undefined;
        } | undefined;
        tag?: string | undefined;
    }, {
        group: string;
        pages: any[];
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        hidden?: boolean | undefined;
        root?: {
            href: string;
            title: string;
            sidebarTitle?: string | undefined;
            description?: string | undefined;
            api?: string | undefined;
            openapi?: string | undefined;
            asyncapi?: string | undefined;
            contentType?: string | undefined;
            authMethod?: string | undefined;
            auth?: string | undefined;
            version?: string | undefined;
            mode?: string | undefined;
            hideFooterPagination?: boolean | undefined;
            authors?: unknown;
            lastUpdatedDate?: string | undefined;
            createdDate?: string | undefined;
            'openapi-schema'?: string | undefined;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            url?: string | undefined;
            hideApiMarker?: boolean | undefined;
            noindex?: boolean | undefined;
            isPublic?: boolean | undefined;
            public?: boolean | undefined;
            deprecated?: boolean | undefined;
        } | undefined;
        tag?: string | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    groups: {
        group: string;
        pages: any[];
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        hidden?: boolean | undefined;
        root?: {
            href: string;
            title: string;
            sidebarTitle?: string | undefined;
            description?: string | undefined;
            api?: string | undefined;
            openapi?: string | undefined;
            asyncapi?: string | undefined;
            contentType?: string | undefined;
            authMethod?: string | undefined;
            auth?: string | undefined;
            version?: string | undefined;
            mode?: string | undefined;
            hideFooterPagination?: boolean | undefined;
            authors?: unknown;
            lastUpdatedDate?: string | undefined;
            createdDate?: string | undefined;
            'openapi-schema'?: string | undefined;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            url?: string | undefined;
            hideApiMarker?: boolean | undefined;
            noindex?: boolean | undefined;
            isPublic?: boolean | undefined;
            public?: boolean | undefined;
            deprecated?: boolean | undefined;
        } | undefined;
        tag?: string | undefined;
    }[];
}, {
    groups: {
        group: string;
        pages: any[];
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        hidden?: boolean | undefined;
        root?: {
            href: string;
            title: string;
            sidebarTitle?: string | undefined;
            description?: string | undefined;
            api?: string | undefined;
            openapi?: string | undefined;
            asyncapi?: string | undefined;
            contentType?: string | undefined;
            authMethod?: string | undefined;
            auth?: string | undefined;
            version?: string | undefined;
            mode?: string | undefined;
            hideFooterPagination?: boolean | undefined;
            authors?: unknown;
            lastUpdatedDate?: string | undefined;
            createdDate?: string | undefined;
            'openapi-schema'?: string | undefined;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            url?: string | undefined;
            hideApiMarker?: boolean | undefined;
            noindex?: boolean | undefined;
            isPublic?: boolean | undefined;
            public?: boolean | undefined;
            deprecated?: boolean | undefined;
        } | undefined;
        tag?: string | undefined;
    }[];
}>, z.ZodObject<{
    pages: z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">;
}, "strip", z.ZodTypeAny, {
    pages: any[];
}, {
    pages: any[];
}>]>, z.ZodObject<{
    global: z.ZodOptional<z.ZodType<import("./divisionNav.js").GlobalNavigation, z.ZodTypeDef, import("./divisionNav.js").GlobalNavigation>>;
}, "strip", z.ZodTypeAny, {
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}, {
    global?: import("./divisionNav.js").GlobalNavigation | undefined;
}>>;
export type NavigationConfig = z.infer<typeof navigationSchema>;
export type DecoratedNavigationConfig = z.infer<typeof decoratedNavigationSchema>;
export type DivisionNavigationType = AnchorConfig | TabConfig | DropdownConfig | VersionConfig | LanguageConfig | GroupConfig;
export type DecoratedDivisionNavigationType = DecoratedAnchorConfig | DecoratedTabConfig | DecoratedDropdownConfig | DecoratedVersionConfig | DecoratedLanguageConfig | DecoratedGroupConfig;
