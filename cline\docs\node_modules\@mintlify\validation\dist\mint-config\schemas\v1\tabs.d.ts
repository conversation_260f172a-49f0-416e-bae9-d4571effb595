import { z } from 'zod';
export declare const tabsSchema: z<PERSON><z.ZodObject<{
    name: z.ZodString;
    url: z.ZodString;
    version: z.ZodOptional<z.ZodString>;
    isDefaultHidden: z.ZodOptional<z.ZodBoolean>;
    openapi: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
}, "strip", z.ZodTypeAny, {
    name: string;
    url: string;
    version?: string | undefined;
    isDefaultHidden?: boolean | undefined;
    openapi?: string | undefined;
}, {
    name: string;
    url: string;
    version?: string | undefined;
    isDefaultHidden?: boolean | undefined;
    openapi?: string | undefined;
}>, "many">;
export declare const primaryTabSchema: z.ZodObject<{
    name: z.ZodString;
    isDefaultHidden: z.ZodOptional<z.ZodBoolean>;
}, "strict", z.<PERSON>odTypeAny, {
    name: string;
    isDefaultHidden?: boolean | undefined;
}, {
    name: string;
    isDefaultHidden?: boolean | undefined;
}>;
