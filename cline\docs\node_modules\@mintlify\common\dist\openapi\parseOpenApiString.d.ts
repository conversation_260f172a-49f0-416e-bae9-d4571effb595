import { OpenAPIV3 } from 'openapi-types';
/**
 *
 * @param str either the openapi or api string defined in the frontmatter
 * @returns method: API method (GET, POST, PUT, DELETE, PATCH)
 *          endpoint: API endpoint
 *          filename: filename of the openapi file (if applicable)
 *          returns undefined for any of the values (fails silently)
 */
export declare const potentiallyParseOpenApiString: (str: string) => {
    method: OpenAPIV3.HttpMethods;
    endpoint: string;
    filename?: string;
} | undefined;
/**
 *
 * @param str either the openapi or api string defined in the frontmatter
 * @returns method: API method (GET, POST, PUT, DELETE, PATCH)
 *          endpoint: API endpoint
 *          filename: filename of the openapi file (if applicable)
 */
export declare const parseOpenApiString: (str: string) => {
    method: OpenAPIV3.HttpMethods;
    endpoint: string;
    filename?: string;
};
