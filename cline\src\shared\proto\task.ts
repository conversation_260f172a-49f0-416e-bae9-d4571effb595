// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface TaskServiceDefinition {
  // Task service methods
}

export interface NewTaskRequest {
  task: string;
  images?: string[];
}

export interface AskResponseRequest {
  response: string;
}

export interface GetTaskHistoryRequest {
  offset?: number;
  limit?: number;
}

export interface TaskFavoriteRequest {
  taskId: string;
  isFavorite: boolean;
}

export interface Task {
  id: string;
  name: string;
  description?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  isFavorite?: boolean;
}

export interface TaskHistory {
  tasks: Task[];
  total: number;
  hasMore: boolean;
}
