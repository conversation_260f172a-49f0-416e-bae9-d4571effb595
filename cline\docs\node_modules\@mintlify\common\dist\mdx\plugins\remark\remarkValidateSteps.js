import { visit } from 'unist-util-visit';
export const remarkValidateSteps = () => (tree) => {
    visit(tree, 'mdxJsxFlowElement', (node, _idx, parent) => {
        if (node.name === 'Step') {
            const parentElement = parent;
            if (!parentElement || parentElement.name !== 'Steps') {
                console.warn('Please ensure that all <Step> components are direct children of <Steps>.');
            }
        }
    });
};
