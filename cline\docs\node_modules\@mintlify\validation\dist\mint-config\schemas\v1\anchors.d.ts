import { z } from 'zod';
export declare const anchorSchema: z.ZodObject<{
    name: z.ZodString;
    url: z.ZodString;
    icon: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    iconType: z.<PERSON>odOptional<z.<PERSON>od<PERSON>num<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
    color: z.ZodOptional<z.<PERSON><PERSON><PERSON>nion<[z.ZodString, z.ZodObject<{
        from: z.ZodString;
        via: z.ZodOptional<z.ZodString>;
        to: z.ZodString;
    }, "strict", z.<PERSON>od<PERSON>ype<PERSON>ny, {
        from: string;
        to: string;
        via?: string | undefined;
    }, {
        from: string;
        to: string;
        via?: string | undefined;
    }>]>>;
    isDefaultHidden: z.ZodOptional<z.ZodBoolean>;
    version: z.<PERSON>odOptional<z.ZodString>;
    openapi: z.<PERSON>ptional<z.ZodEffects<z.ZodString, string, string>>;
}, "strip", z.Z<PERSON>ype<PERSON>ny, {
    name: string;
    url: string;
    icon?: string | undefined;
    iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    color?: string | {
        from: string;
        to: string;
        via?: string | undefined;
    } | undefined;
    isDefaultHidden?: boolean | undefined;
    version?: string | undefined;
    openapi?: string | undefined;
}, {
    name: string;
    url: string;
    icon?: string | undefined;
    iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    color?: string | {
        from: string;
        to: string;
        via?: string | undefined;
    } | undefined;
    isDefaultHidden?: boolean | undefined;
    version?: string | undefined;
    openapi?: string | undefined;
}>;
export declare const topAnchorSchema: z.ZodObject<{
    name: z.ZodString;
    icon: z.ZodOptional<z.ZodString>;
    iconType: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
}, "strict", z.ZodTypeAny, {
    name: string;
    icon?: string | undefined;
    iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
}, {
    name: string;
    icon?: string | undefined;
    iconType?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
}>;
