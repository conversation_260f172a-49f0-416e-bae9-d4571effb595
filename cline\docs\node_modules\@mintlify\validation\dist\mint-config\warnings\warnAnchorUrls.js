import { z } from 'zod';
export const warnAnchorUrls = ({ anchors, navigation }) => {
    var _a;
    return ((_a = anchors === null || anchors === void 0 ? void 0 : anchors.filter((anchor) => !anchor.url.startsWith('mailto:') &&
        !anchor.url.startsWith('http://') &&
        !anchor.url.startsWith('https://') &&
        !navigation.some((nav) => pageStartsWith(nav, anchor.url))).map((anchor, i) => ({
        code: z.ZodIssueCode.custom,
        message: `No pages in the navigation match anchor ${anchor.url}`,
        path: ['anchors', i, 'url'],
    }))) !== null && _a !== void 0 ? _a : []);
};
function pageStartsWith(navEntry, prefix) {
    if (typeof navEntry === 'string') {
        return navEntry.startsWith(prefix);
    }
    else {
        return navEntry.pages.some((entry) => pageStartsWith(entry, prefix));
    }
}
