type BetterAjvErrorsOptions = {
    indent?: number;
    json?: string;
};
type AjvError = {
    keyword: string;
    dataPath: string;
    schemaPath: string;
    params: any;
    message: string;
    schema: any;
    parentSchema: any;
    data: any;
};
export declare function betterAjvErrors(schema: any, data: any, errors: AjvError[], options?: BetterAjvErrorsOptions): any;
export {};
//# sourceMappingURL=index.d.ts.map