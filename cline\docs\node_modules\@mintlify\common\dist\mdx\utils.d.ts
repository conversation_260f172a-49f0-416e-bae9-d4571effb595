import type { ImportDeclaration, ExportAllDeclaration, ExportDefaultDeclaration, ExportNamedDeclaration, Program } from 'estree-jsx';
import { MdxFlowExpression, MdxjsEsm, MdxJsxAttribute, MdxJsxExpressionAttribute, MdxTextExpression } from 'mdast-util-mdx';
import type { MdxJsxFlowElement } from 'mdast-util-mdx-jsx';
import type { Node } from 'unist';
import type { MdxNodeBodyChildType } from '../types/mdx/index.js';
export declare const isMdxJsEsm: (node: Node) => node is MdxjsEsm;
export declare function estreeIsProgram<T extends MdxFlowExpression | MdxTextExpression | MdxjsEsm>(node: T): node is T & {
    data: {
        estree: Program;
    };
};
export declare const isImportDeclaration: (bodyChild: MdxNodeBodyChildType) => bodyChild is ImportDeclaration;
export declare const isExport: (type: string) => boolean;
export declare const isExportNode: (bodyChild: MdxNodeBodyChildType) => bodyChild is ExportAllDeclaration | ExportDefaultDeclaration | ExportNamedDeclaration;
export declare const isMdxJsxFlowElement: (node: Node) => node is MdxJsxFlowElement;
export declare const createUniqueVariableName: (variableName: string, id: number) => string;
export declare const getObjectExpressionStringProperty: (key: string, attribute: MdxJsxAttribute | MdxJsxExpressionAttribute | undefined) => string | undefined;
export declare const getArrayExpressionStringProperties: (attribute: MdxJsxAttribute | MdxJsxExpressionAttribute | undefined) => string[];
