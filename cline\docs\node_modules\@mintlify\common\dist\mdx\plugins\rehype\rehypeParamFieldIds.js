import slugify from '@sindresorhus/slugify';
import { visit } from 'unist-util-visit';
export const rehypeParamFieldIds = () => {
    return (tree) => {
        const paramCounts = new Map();
        visit(tree, 'mdxJsxFlowElement', (element) => {
            var _a;
            if (element.name === 'ParamField' ||
                element.name === 'Param' ||
                element.name === 'ResponseField') {
                const nameAttr = element.attributes.find((attr) => 'name' in attr &&
                    (attr.name === 'query' ||
                        attr.name === 'path' ||
                        attr.name === 'body' ||
                        attr.name === 'header' ||
                        attr.name === 'name'));
                if (nameAttr && typeof nameAttr.value === 'string' && nameAttr.value) {
                    const currentCount = (_a = paramCounts.get(nameAttr.value)) !== null && _a !== void 0 ? _a : 0;
                    paramCounts.set(nameAttr.value, currentCount + 1);
                    const suffix = currentCount > 0 ? `_${currentCount}` : '';
                    const id = slugify(`param-${nameAttr.value}${suffix}`, {
                        decamelize: true,
                        separator: '-',
                    });
                    element.attributes.push({
                        type: 'mdxJsxAttribute',
                        name: 'id',
                        value: id,
                    });
                }
            }
        });
        return tree;
    };
};
