import { OpenAPIV3_1 } from 'openapi-types';
import { BaseConverter } from './BaseConverter.js';
import type { ContentSchema, DataSchemaArray, Endpoint, ExampleSchema, HttpMethod, IncrementalDataSchemaArray, ParameterSections, ResponseSchema } from './types/endpoint.js';
import { EndpointLocation } from './types/endpoint.js';
export declare abstract class BaseOpenApiToEndpointConverter<D extends DataSchemaArray | IncrementalDataSchemaArray> extends BaseConverter {
    readonly document: OpenAPIV3_1.Document;
    readonly path: string;
    readonly method: HttpMethod;
    readonly safeParse: boolean;
    protected pathObject: OpenAPIV3_1.PathItemObject;
    protected operationObject: OpenAPIV3_1.OperationObject;
    protected location: EndpointLocation;
    protected constructor(document: OpenAPIV3_1.Document, path: string, method: HttpMethod, safeParse?: boolean);
    protected convert(): Endpoint<D>;
    abstract convertParameters(): ParameterSections<D>;
    abstract convertBody(): Record<string, ContentSchema<D>>;
    abstract convertResponses(): ResponseSchema<D>;
    protected convertExamples(examples: Record<string, OpenAPIV3_1.ExampleObject> | undefined, example: unknown | undefined, schemaArray: DataSchemaArray): Record<string, ExampleSchema>;
    private convertCodeSamples;
    private getXmcp;
}
export declare class OpenApiToEndpointConverter extends BaseOpenApiToEndpointConverter<DataSchemaArray> {
    convertBody(): Record<string, ContentSchema<DataSchemaArray>>;
    convertResponses(): ResponseSchema<DataSchemaArray>;
    convertContent(debugPath: string[], content: Record<string, OpenAPIV3_1.MediaTypeObject> | undefined, location: 'request' | 'response', required?: boolean, description?: string): Record<string, ContentSchema<DataSchemaArray>>;
    convertParameters(): ParameterSections<DataSchemaArray>;
    static convert(spec: OpenAPIV3_1.Document, path: string, method: HttpMethod, safeParse?: boolean): Endpoint<DataSchemaArray>;
}
export declare class OpenApiToIncrementalEndpointConverter extends BaseOpenApiToEndpointConverter<IncrementalDataSchemaArray> {
    readonly rawDocument: OpenAPIV3_1.Document;
    private constructor();
    convertParameters(): ParameterSections<IncrementalDataSchemaArray>;
    convertBody(): Record<string, ContentSchema<IncrementalDataSchemaArray>>;
    convertResponses(): ResponseSchema<IncrementalDataSchemaArray>;
    convertContent(debugPath: string[], rawContent: Record<string, OpenAPIV3_1.MediaTypeObject> | undefined, dereferencedContent: Record<string, OpenAPIV3_1.MediaTypeObject> | undefined, location: 'request' | 'response', required?: boolean, description?: string): Record<string, ContentSchema<IncrementalDataSchemaArray>>;
    static convert(rawDocument: OpenAPIV3_1.Document, resolvedDocument: OpenAPIV3_1.Document, path: string, method: HttpMethod, safeParse?: boolean): Endpoint<IncrementalDataSchemaArray>;
}
