{"version": 3, "file": "yamlSyntax.test.js", "sourceRoot": "", "sources": ["../../test/yamlSyntax.test.ts"], "names": [], "mappings": ";;AAAA,mCAAoC;AAEpC,KAAK,CAAC,aAAa,EAAE,GAAG,EAAE;IAEtB,KAAK,CAAC,0BAA0B,EAAE,GAAG,EAAE;QAEnC,IAAI,CAAC,UAAU,EAAE;YACb,UAAU,CACN,YAAY;gBACZ,WAAW,EACX;gBACI;oBACI,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,CAAC;oBACT,OAAO,EAAE,8CAA8C;oBACvD,SAAS,EAAE,IAAI;iBAClB;aACJ,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE;YACb,UAAU,CACN,WAAW;gBACX,aAAa;gBACb,eAAe;gBACf,iBAAiB,EACjB;gBACI;oBACI,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,CAAC;oBACT,OAAO,EAAE,8CAA8C;oBACvD,SAAS,EAAE,IAAI;iBAClB;aACJ,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AAEH,SAAS,UAAU,CAAC,KAAY,EAAC,cAAgC;IAC7D,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;AAC3C,CAAC"}