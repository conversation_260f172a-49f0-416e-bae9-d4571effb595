import type { Program } from 'estree-jsx';
import { MdxjsEsm } from 'mdast-util-mdx';
import { Node } from 'unist';
export type NodeWithExport = MdxjsEsm & {
    data: {
        estree: Program;
    };
};
/**
 * An export looks like this in AST form:
 * {
 *   type: 'mdxjsEsm',
 *   data: {
 *     estree: {
 *       type: 'Program',
 *       sourceType: 'module'
 *       body: [
 *         type: 'ExportNamedDeclaration',
 *         ...
 *       ]
 *     }
 *   }
 * }
 * @param content mdx ast node
 * @returns whether the node includes an export or not
 */
export declare const nodeIncludesExport: (node: Node) => node is NodeWithExport;
