import type { RootContent as <PERSON><PERSON><PERSON><PERSON>Content } from 'hast';
import type { RootContent as MdastRootContent } from 'mdast';
import type { MdxJsxAttribute, MdxJsxAttributeValueExpression } from 'mdast-util-mdx-jsx';
type RootContent = MdastRootContent | HastRootContent;
export declare const createMdxJsxAttribute: (key: string, value: MdxJsxAttributeValueExpression | string | null | undefined | number | boolean) => MdxJsxAttribute;
export declare function getUnicodeId(title: string): string;
export declare const getTableOfContentsTitle: (node: RootContent, index?: number, children?: RootContent[]) => string;
export {};
