import { AnchorColor } from './colors.js';
import { IconType } from './iconTypes.js';
import { OpenApiAnchorPropertyType } from './openapi.js';
export type TopAnchor = {
    name: string;
    icon?: string;
    iconType?: IconType;
};
export type Anchor = {
    name: string;
    url: string;
    version?: string;
    isDefaultHidden?: boolean;
    openapi?: OpenApiAnchorPropertyType;
    icon?: string;
    iconType?: IconType;
    color?: AnchorColor;
};
