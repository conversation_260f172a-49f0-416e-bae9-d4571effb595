export const ANTHROPIC_PREMIUM_MODELS = [
    'claude-sonnet-4-20250514',
    'claude-3-7-sonnet-20250219',
];
export const ANTHROPIC_MODELS = [...ANTHROPIC_PREMIUM_MODELS];
export const OPENAI_PREMIUM_MODELS = ['gpt-4.1-2025-04-14', 'gpt-4o-2024-08-06'];
export const OPENAI_REGULAR_MODELS = ['o3-mini-2025-01-31'];
export const OPENAI_MODELS = [...OPENAI_PREMIUM_MODELS, ...OPENAI_REGULAR_MODELS];
export const GEMINI_PREMIUM_MODELS = ['gemini/gemini-2.5-pro-preview-03-25'];
export const GEMINI_MODELS = [...GEMINI_PREMIUM_MODELS];
export const DEEPSEEK_PREMIUM_MODELS = ['deepseek-r1-distill-llama-70b'];
export const DEEPSEEK_MODELS = [...DEEPSEEK_PREMIUM_MODELS];
export const GROQ_PREMIUM_MODELS = ['meta-llama/llama-4-maverick-17b-128e-instruct'];
export const SAMBANOVA_MODELS = [
    'sambanova/llama-3.3-70b-instruct',
    'sambanova/deepseekv3-0324',
];
export const GROQ_MODELS = [...GROQ_PREMIUM_MODELS];
export const GROK_PREMIUM_MODELS = ['grok-3'];
export const GROK_MODELS = [...GROK_PREMIUM_MODELS];
export const ALL_MODELS = [
    ...ANTHROPIC_MODELS,
    ...OPENAI_MODELS,
    ...DEEPSEEK_MODELS,
    ...GEMINI_MODELS,
    ...GROQ_MODELS,
    ...GROK_MODELS,
    ...SAMBANOVA_MODELS,
];
export const PREMIUM_MODELS = [
    ...ANTHROPIC_PREMIUM_MODELS,
    ...OPENAI_PREMIUM_MODELS,
    ...DEEPSEEK_PREMIUM_MODELS,
    ...GEMINI_PREMIUM_MODELS,
    ...GROQ_PREMIUM_MODELS,
    ...GROK_PREMIUM_MODELS,
];
export const REGULAR_MODELS = [...OPENAI_REGULAR_MODELS];
export const CEREBRAS_HOSTED_MODELS = [...DEEPSEEK_MODELS];
export const DEFAULT_MODEL = 'claude-sonnet-4-20250514';
