import { z } from 'zod';
import { DropdownNavigation } from './divisionNav.js';
export declare const baseDropdownSchema: z.ZodObject<{
    dropdown: z.ZodString;
    icon: z.ZodOptional<z.<PERSON><PERSON><[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.<PERSON>od<PERSON>ptional<z.Zod<PERSON>num<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.<PERSON>odEffe<PERSON><z.ZodString, string, string>;
        library: z.<PERSON>od<PERSON>ptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodType<PERSON>ny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    color: z.ZodOptional<z.ZodObject<{
        light: z.ZodOptional<z.ZodString>;
        dark: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        light?: string | undefined;
        dark?: string | undefined;
    }>>;
    description: z.ZodOptional<z.ZodString>;
    hidden: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    dropdown: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    description?: string | undefined;
    hidden?: boolean | undefined;
}, {
    dropdown: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    description?: string | undefined;
    hidden?: boolean | undefined;
}>;
export type BaseDropdownSchema = z.infer<typeof baseDropdownSchema>;
export declare const nonRecursiveDropdownSchema: z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    color: z.ZodOptional<z.ZodObject<{
        light: z.ZodOptional<z.ZodString>;
        dark: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        light?: string | undefined;
        dark?: string | undefined;
    }>>;
    description: z.ZodOptional<z.ZodString>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    dropdown: z.ZodString;
    href: z.ZodString;
}, "strip", z.ZodTypeAny, {
    href: string;
    dropdown: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    description?: string | undefined;
    hidden?: boolean | undefined;
}, {
    href: string;
    dropdown: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    description?: string | undefined;
    hidden?: boolean | undefined;
}>;
export declare const dropdownSchema: z.ZodType<DropdownNavigation<'default'>>;
export declare const decoratedDropdownSchema: z.ZodType<DropdownNavigation<'decorated'>>;
export declare const dropdownsSchema: z.ZodArray<z.ZodType<DropdownNavigation<"default">, z.ZodTypeDef, DropdownNavigation<"default">>, "many">;
export declare const decoratedDropdownsSchema: z.ZodArray<z.ZodType<DropdownNavigation<"decorated">, z.ZodTypeDef, DropdownNavigation<"decorated">>, "many">;
export type DropdownConfig = z.infer<typeof dropdownSchema>;
export type DropdownsConfig = z.infer<typeof dropdownsSchema>;
export type DecoratedDropdownConfig = z.infer<typeof decoratedDropdownSchema>;
export type DecoratedDropdownsConfig = z.infer<typeof decoratedDropdownsSchema>;
