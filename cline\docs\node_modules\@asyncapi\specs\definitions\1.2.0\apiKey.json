{"type": "object", "required": ["type", "in"], "properties": {"type": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "in": {"type": "string", "enum": ["user", "password"]}, "description": {"type": "string"}}, "patternProperties": {"^x-": {}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.2.0/apiKey.json"}