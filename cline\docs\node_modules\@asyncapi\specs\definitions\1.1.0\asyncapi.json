{"title": "AsyncAPI 1.1.0 schema.", "id": "http://asyncapi.com/definitions/1.1.0/asyncapi.json", "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "required": ["asyncapi", "info", "topics"], "additionalProperties": false, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.1.0/vendorExtension.json"}}, "properties": {"asyncapi": {"type": "string", "enum": ["1.0.0", "1.1.0"], "description": "The AsyncAPI specification version of this document."}, "info": {"$ref": "http://asyncapi.com/definitions/1.1.0/info.json"}, "baseTopic": {"type": "string", "pattern": "^[^/.]", "description": "The base topic to the API. Example: 'hitch'.", "default": ""}, "servers": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/1.1.0/server.json"}, "uniqueItems": true}, "topics": {"$ref": "http://asyncapi.com/definitions/1.1.0/topics.json"}, "components": {"$ref": "http://asyncapi.com/definitions/1.1.0/components.json"}, "tags": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/1.1.0/tag.json"}, "uniqueItems": true}, "security": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/1.1.0/SecurityRequirement.json"}}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/1.1.0/externalDocs.json"}}}