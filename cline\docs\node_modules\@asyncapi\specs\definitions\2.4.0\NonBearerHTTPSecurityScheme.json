{"not": {"type": "object", "properties": {"scheme": {"type": "string", "enum": ["bearer"]}}}, "type": "object", "required": ["scheme", "type"], "properties": {"scheme": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["http"]}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.4.0/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.4.0/NonBearerHTTPSecurityScheme.json"}