export type GitSource = GithubSource | GitlabSource;
export type GithubSource = {
    type: 'github';
    owner: string;
    repo: string;
    deployBranch: string;
    contentDirectory: string;
    isPrivate: boolean;
};
export type GitlabSource = {
    type: 'gitlab';
    owner: string;
    repo: string;
    projectId: number;
    deployBranch: string;
    contentDirectory: string;
    isPrivate: boolean;
    hostUrl?: string;
};
export type GitSourceType = GitSource['type'];
