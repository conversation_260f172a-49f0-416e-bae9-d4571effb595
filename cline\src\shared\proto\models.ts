// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface ModelsServiceDefinition {
  // Models service methods
}

export interface UpdateApiConfigurationRequest {
  configuration: ApiConfiguration;
}

export interface ApiConfiguration {
  provider: string;
  apiKey?: string;
  baseUrl?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface OpenAiModelsRequest {
  baseUrl?: string;
  apiKey?: string;
}

export interface OpenRouterCompatibleModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  pricing?: {
    prompt: number;
    completion: number;
  };
}

export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  maxTokens?: number;
}

export interface ModelsResponse {
  models: ModelInfo[];
}
