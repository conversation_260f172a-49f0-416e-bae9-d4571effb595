// Generated by scripts/prepare.ts

export const languageNames = [
"abap",
"actionscript-3",
"ada",
"angular-expression",
"angular-html",
"angular-inline-style",
"angular-inline-template",
"angular-let-declaration",
"angular-template",
"angular-template-blocks",
"angular-ts",
"apache",
"apex",
"apl",
"applescript",
"ara",
"asciidoc",
"asm",
"astro",
"awk",
"ballerina",
"bat",
"beancount",
"berry",
"bibtex",
"bicep",
"blade",
"bsl",
"c",
"cadence",
"cairo",
"clarity",
"clojure",
"cmake",
"cobol",
"codeowners",
"codeql",
"coffee",
"common-lisp",
"coq",
"cpp",
"cpp-macro",
"crystal",
"csharp",
"css",
"csv",
"cue",
"cypher",
"d",
"dart",
"dax",
"desktop",
"diff",
"docker",
"dotenv",
"dream-maker",
"edge",
"elixir",
"elm",
"emacs-lisp",
"erb",
"erlang",
"es-tag-css",
"es-tag-glsl",
"es-tag-html",
"es-tag-sql",
"es-tag-xml",
"fennel",
"fish",
"fluent",
"fortran-fixed-form",
"fortran-free-form",
"fsharp",
"gdresource",
"gdscript",
"gdshader",
"genie",
"gherkin",
"git-commit",
"git-rebase",
"gleam",
"glimmer-js",
"glimmer-ts",
"glsl",
"gnuplot",
"go",
"graphql",
"groovy",
"hack",
"haml",
"handlebars",
"haskell",
"haxe",
"hcl",
"hjson",
"hlsl",
"html",
"html-derivative",
"http",
"hxml",
"hy",
"imba",
"ini",
"java",
"javascript",
"jinja",
"jinja-html",
"jison",
"json",
"json5",
"jsonc",
"jsonl",
"jsonnet",
"jssm",
"jsx",
"julia",
"kotlin",
"kusto",
"latex",
"lean",
"less",
"liquid",
"llvm",
"log",
"logo",
"lua",
"luau",
"make",
"markdown",
"markdown-vue",
"marko",
"matlab",
"mdc",
"mdx",
"mermaid",
"mipsasm",
"mojo",
"move",
"narrat",
"nextflow",
"nginx",
"nim",
"nix",
"nushell",
"objective-c",
"objective-cpp",
"ocaml",
"pascal",
"perl",
"php",
"plsql",
"po",
"polar",
"postcss",
"powerquery",
"powershell",
"prisma",
"prolog",
"proto",
"pug",
"puppet",
"purescript",
"python",
"qml",
"qmldir",
"qss",
"r",
"racket",
"raku",
"razor",
"reg",
"regexp",
"rel",
"riscv",
"rst",
"ruby",
"rust",
"sas",
"sass",
"scala",
"scheme",
"scss",
"sdbl",
"shaderlab",
"shellscript",
"shellsession",
"smalltalk",
"solidity",
"soy",
"sparql",
"splunk",
"sql",
"ssh-config",
"stata",
"stylus",
"svelte",
"swift",
"system-verilog",
"systemd",
"talonscript",
"tasl",
"tcl",
"templ",
"terraform",
"tex",
"toml",
"ts-tags",
"tsv",
"tsx",
"turtle",
"twig",
"typescript",
"typespec",
"typst",
"v",
"vala",
"vb",
"verilog",
"vhdl",
"viml",
"vue",
"vue-directives",
"vue-html",
"vue-interpolations",
"vue-sfc-style-variable-injection",
"vue-vine",
"vyper",
"wasm",
"wenyan",
"wgsl",
"wikitext",
"wit",
"wolfram",
"xml",
"xsl",
"yaml",
"zenscript",
"zig"
]

export const languageAliasNames = [
"1c",
"1c-query",
"adoc",
"bash",
"batch",
"be",
"cdc",
"clj",
"closure-templates",
"cmd",
"coffeescript",
"console",
"cql",
"cs",
"dockerfile",
"elisp",
"erl",
"f",
"f03",
"f08",
"f18",
"f77",
"f90",
"f95",
"for",
"fs",
"fsl",
"ftl",
"gjs",
"gql",
"gts",
"hbs",
"hs",
"jade",
"jl",
"js",
"kql",
"kt",
"kts",
"lean4",
"lisp",
"lit",
"makefile",
"md",
"mediawiki",
"mips",
"mmd",
"nar",
"nf",
"nu",
"objc",
"perl6",
"pot",
"potx",
"properties",
"protobuf",
"ps",
"ps1",
"py",
"ql",
"rb",
"regex",
"rs",
"sh",
"shader",
"shell",
"spl",
"styl",
"talon",
"tf",
"tfvars",
"ts",
"tsp",
"typ",
"vim",
"vimscript",
"vy",
"wiki",
"wl",
"yml",
"zsh"
]
