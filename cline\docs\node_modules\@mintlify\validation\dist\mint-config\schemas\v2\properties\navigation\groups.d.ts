import { z } from 'zod';
export declare const baseGroupSchema: z.ZodObject<{
    group: z.ZodString;
    icon: z.<PERSON>al<z.<PERSON><PERSON>n<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.<PERSON><z.<PERSON>od<PERSON>num<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.<PERSON>ffe<PERSON><z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.<PERSON>od<PERSON>ny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    tag: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    group: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
    tag?: string | undefined;
}, {
    group: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
    tag?: string | undefined;
}>;
export declare const groupSchema: z.ZodUnion<[z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    group: z.ZodString;
    tag: z.ZodOptional<z.ZodString>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    openapi: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
        source: z.ZodEffects<z.ZodString, string, string>;
        directory: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        source: string;
        directory?: string | undefined;
    }, {
        source: string;
        directory?: string | undefined;
    }>]>;
}, "strip", z.ZodTypeAny, {
    openapi: (string | string[] | {
        source: string;
        directory?: string | undefined;
    }) & (string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined);
    group: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}, {
    openapi: (string | string[] | {
        source: string;
        directory?: string | undefined;
    }) & (string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined);
    group: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}>, z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    group: z.ZodString;
    tag: z.ZodOptional<z.ZodString>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    asyncapi: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
        source: z.ZodEffects<z.ZodString, string, string>;
        directory: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        source: string;
        directory?: string | undefined;
    }, {
        source: string;
        directory?: string | undefined;
    }>]>;
}, "strip", z.ZodTypeAny, {
    group: string;
    asyncapi: (string | string[] | {
        source: string;
        directory?: string | undefined;
    }) & (string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined);
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}, {
    group: string;
    asyncapi: (string | string[] | {
        source: string;
        directory?: string | undefined;
    }) & (string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined);
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}>, z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    group: z.ZodString;
    tag: z.ZodOptional<z.ZodString>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    pages: z.ZodLazy<z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">>;
}, "strip", z.ZodTypeAny, {
    group: string;
    pages: any[];
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}, {
    group: string;
    pages: any[];
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}>]>;
export declare const decoratedGroupSchema: z.ZodObject<{
    group: z.ZodString;
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodObject<{
        href: z.ZodString;
        title: z.ZodString;
        sidebarTitle: z.ZodOptional<z.ZodString>;
        description: z.ZodOptional<z.ZodString>;
        api: z.ZodOptional<z.ZodString>;
        openapi: z.ZodOptional<z.ZodString>;
        asyncapi: z.ZodOptional<z.ZodString>;
        contentType: z.ZodOptional<z.ZodString>;
        authMethod: z.ZodOptional<z.ZodString>;
        auth: z.ZodOptional<z.ZodString>;
        version: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodString>;
        hideFooterPagination: z.ZodOptional<z.ZodBoolean>;
        authors: z.ZodOptional<z.ZodUnknown>;
        lastUpdatedDate: z.ZodOptional<z.ZodString>;
        createdDate: z.ZodOptional<z.ZodString>;
        'openapi-schema': z.ZodOptional<z.ZodString>;
        icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
            style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
            name: z.ZodEffects<z.ZodString, string, string>;
            library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }>]>>;
        tag: z.ZodOptional<z.ZodString>;
        url: z.ZodOptional<z.ZodString>;
        hideApiMarker: z.ZodOptional<z.ZodBoolean>;
        noindex: z.ZodOptional<z.ZodBoolean>;
        isPublic: z.ZodOptional<z.ZodBoolean>;
        public: z.ZodOptional<z.ZodBoolean>;
        deprecated: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        href: string;
        title: string;
        sidebarTitle?: string | undefined;
        description?: string | undefined;
        api?: string | undefined;
        openapi?: string | undefined;
        asyncapi?: string | undefined;
        contentType?: string | undefined;
        authMethod?: string | undefined;
        auth?: string | undefined;
        version?: string | undefined;
        mode?: string | undefined;
        hideFooterPagination?: boolean | undefined;
        authors?: unknown;
        lastUpdatedDate?: string | undefined;
        createdDate?: string | undefined;
        'openapi-schema'?: string | undefined;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        url?: string | undefined;
        hideApiMarker?: boolean | undefined;
        noindex?: boolean | undefined;
        isPublic?: boolean | undefined;
        public?: boolean | undefined;
        deprecated?: boolean | undefined;
    }, {
        href: string;
        title: string;
        sidebarTitle?: string | undefined;
        description?: string | undefined;
        api?: string | undefined;
        openapi?: string | undefined;
        asyncapi?: string | undefined;
        contentType?: string | undefined;
        authMethod?: string | undefined;
        auth?: string | undefined;
        version?: string | undefined;
        mode?: string | undefined;
        hideFooterPagination?: boolean | undefined;
        authors?: unknown;
        lastUpdatedDate?: string | undefined;
        createdDate?: string | undefined;
        'openapi-schema'?: string | undefined;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        url?: string | undefined;
        hideApiMarker?: boolean | undefined;
        noindex?: boolean | undefined;
        isPublic?: boolean | undefined;
        public?: boolean | undefined;
        deprecated?: boolean | undefined;
    }>>;
    pages: z.ZodLazy<z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">>;
    tag: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    group: string;
    pages: any[];
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
    root?: {
        href: string;
        title: string;
        sidebarTitle?: string | undefined;
        description?: string | undefined;
        api?: string | undefined;
        openapi?: string | undefined;
        asyncapi?: string | undefined;
        contentType?: string | undefined;
        authMethod?: string | undefined;
        auth?: string | undefined;
        version?: string | undefined;
        mode?: string | undefined;
        hideFooterPagination?: boolean | undefined;
        authors?: unknown;
        lastUpdatedDate?: string | undefined;
        createdDate?: string | undefined;
        'openapi-schema'?: string | undefined;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        url?: string | undefined;
        hideApiMarker?: boolean | undefined;
        noindex?: boolean | undefined;
        isPublic?: boolean | undefined;
        public?: boolean | undefined;
        deprecated?: boolean | undefined;
    } | undefined;
    tag?: string | undefined;
}, {
    group: string;
    pages: any[];
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
    root?: {
        href: string;
        title: string;
        sidebarTitle?: string | undefined;
        description?: string | undefined;
        api?: string | undefined;
        openapi?: string | undefined;
        asyncapi?: string | undefined;
        contentType?: string | undefined;
        authMethod?: string | undefined;
        auth?: string | undefined;
        version?: string | undefined;
        mode?: string | undefined;
        hideFooterPagination?: boolean | undefined;
        authors?: unknown;
        lastUpdatedDate?: string | undefined;
        createdDate?: string | undefined;
        'openapi-schema'?: string | undefined;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        url?: string | undefined;
        hideApiMarker?: boolean | undefined;
        noindex?: boolean | undefined;
        isPublic?: boolean | undefined;
        public?: boolean | undefined;
        deprecated?: boolean | undefined;
    } | undefined;
    tag?: string | undefined;
}>;
export declare const groupsSchema: z.ZodArray<z.ZodUnion<[z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    group: z.ZodString;
    tag: z.ZodOptional<z.ZodString>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    openapi: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
        source: z.ZodEffects<z.ZodString, string, string>;
        directory: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        source: string;
        directory?: string | undefined;
    }, {
        source: string;
        directory?: string | undefined;
    }>]>;
}, "strip", z.ZodTypeAny, {
    openapi: (string | string[] | {
        source: string;
        directory?: string | undefined;
    }) & (string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined);
    group: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}, {
    openapi: (string | string[] | {
        source: string;
        directory?: string | undefined;
    }) & (string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined);
    group: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}>, z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    group: z.ZodString;
    tag: z.ZodOptional<z.ZodString>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    asyncapi: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
        source: z.ZodEffects<z.ZodString, string, string>;
        directory: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        source: string;
        directory?: string | undefined;
    }, {
        source: string;
        directory?: string | undefined;
    }>]>;
}, "strip", z.ZodTypeAny, {
    group: string;
    asyncapi: (string | string[] | {
        source: string;
        directory?: string | undefined;
    }) & (string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined);
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}, {
    group: string;
    asyncapi: (string | string[] | {
        source: string;
        directory?: string | undefined;
    }) & (string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined);
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}>, z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    group: z.ZodString;
    tag: z.ZodOptional<z.ZodString>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    pages: z.ZodLazy<z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">>;
}, "strip", z.ZodTypeAny, {
    group: string;
    pages: any[];
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}, {
    group: string;
    pages: any[];
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    hidden?: boolean | undefined;
    root?: string | undefined;
}>]>, "many">;
export declare const decoratedGroupsSchema: z.ZodArray<z.ZodObject<{
    group: z.ZodString;
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    root: z.ZodOptional<z.ZodObject<{
        href: z.ZodString;
        title: z.ZodString;
        sidebarTitle: z.ZodOptional<z.ZodString>;
        description: z.ZodOptional<z.ZodString>;
        api: z.ZodOptional<z.ZodString>;
        openapi: z.ZodOptional<z.ZodString>;
        asyncapi: z.ZodOptional<z.ZodString>;
        contentType: z.ZodOptional<z.ZodString>;
        authMethod: z.ZodOptional<z.ZodString>;
        auth: z.ZodOptional<z.ZodString>;
        version: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodString>;
        hideFooterPagination: z.ZodOptional<z.ZodBoolean>;
        authors: z.ZodOptional<z.ZodUnknown>;
        lastUpdatedDate: z.ZodOptional<z.ZodString>;
        createdDate: z.ZodOptional<z.ZodString>;
        'openapi-schema': z.ZodOptional<z.ZodString>;
        icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
            style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
            name: z.ZodEffects<z.ZodString, string, string>;
            library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }>]>>;
        tag: z.ZodOptional<z.ZodString>;
        url: z.ZodOptional<z.ZodString>;
        hideApiMarker: z.ZodOptional<z.ZodBoolean>;
        noindex: z.ZodOptional<z.ZodBoolean>;
        isPublic: z.ZodOptional<z.ZodBoolean>;
        public: z.ZodOptional<z.ZodBoolean>;
        deprecated: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        href: string;
        title: string;
        sidebarTitle?: string | undefined;
        description?: string | undefined;
        api?: string | undefined;
        openapi?: string | undefined;
        asyncapi?: string | undefined;
        contentType?: string | undefined;
        authMethod?: string | undefined;
        auth?: string | undefined;
        version?: string | undefined;
        mode?: string | undefined;
        hideFooterPagination?: boolean | undefined;
        authors?: unknown;
        lastUpdatedDate?: string | undefined;
        createdDate?: string | undefined;
        'openapi-schema'?: string | undefined;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        url?: string | undefined;
        hideApiMarker?: boolean | undefined;
        noindex?: boolean | undefined;
        isPublic?: boolean | undefined;
        public?: boolean | undefined;
        deprecated?: boolean | undefined;
    }, {
        href: string;
        title: string;
        sidebarTitle?: string | undefined;
        description?: string | undefined;
        api?: string | undefined;
        openapi?: string | undefined;
        asyncapi?: string | undefined;
        contentType?: string | undefined;
        authMethod?: string | undefined;
        auth?: string | undefined;
        version?: string | undefined;
        mode?: string | undefined;
        hideFooterPagination?: boolean | undefined;
        authors?: unknown;
        lastUpdatedDate?: string | undefined;
        createdDate?: string | undefined;
        'openapi-schema'?: string | undefined;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        url?: string | undefined;
        hideApiMarker?: boolean | undefined;
        noindex?: boolean | undefined;
        isPublic?: boolean | undefined;
        public?: boolean | undefined;
        deprecated?: boolean | undefined;
    }>>;
    pages: z.ZodLazy<z.ZodArray<z.ZodType<any, z.ZodTypeDef, any>, "many">>;
    tag: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    group: string;
    pages: any[];
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
    root?: {
        href: string;
        title: string;
        sidebarTitle?: string | undefined;
        description?: string | undefined;
        api?: string | undefined;
        openapi?: string | undefined;
        asyncapi?: string | undefined;
        contentType?: string | undefined;
        authMethod?: string | undefined;
        auth?: string | undefined;
        version?: string | undefined;
        mode?: string | undefined;
        hideFooterPagination?: boolean | undefined;
        authors?: unknown;
        lastUpdatedDate?: string | undefined;
        createdDate?: string | undefined;
        'openapi-schema'?: string | undefined;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        url?: string | undefined;
        hideApiMarker?: boolean | undefined;
        noindex?: boolean | undefined;
        isPublic?: boolean | undefined;
        public?: boolean | undefined;
        deprecated?: boolean | undefined;
    } | undefined;
    tag?: string | undefined;
}, {
    group: string;
    pages: any[];
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
    root?: {
        href: string;
        title: string;
        sidebarTitle?: string | undefined;
        description?: string | undefined;
        api?: string | undefined;
        openapi?: string | undefined;
        asyncapi?: string | undefined;
        contentType?: string | undefined;
        authMethod?: string | undefined;
        auth?: string | undefined;
        version?: string | undefined;
        mode?: string | undefined;
        hideFooterPagination?: boolean | undefined;
        authors?: unknown;
        lastUpdatedDate?: string | undefined;
        createdDate?: string | undefined;
        'openapi-schema'?: string | undefined;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
        tag?: string | undefined;
        url?: string | undefined;
        hideApiMarker?: boolean | undefined;
        noindex?: boolean | undefined;
        isPublic?: boolean | undefined;
        public?: boolean | undefined;
        deprecated?: boolean | undefined;
    } | undefined;
    tag?: string | undefined;
}>, "many">;
export type GroupConfig = z.infer<typeof groupSchema>;
export type GroupsConfig = z.infer<typeof groupsSchema>;
export type DecoratedGroupConfig = z.infer<typeof decoratedGroupSchema>;
export type DecoratedGroupsConfig = z.infer<typeof decoratedGroupsSchema>;
