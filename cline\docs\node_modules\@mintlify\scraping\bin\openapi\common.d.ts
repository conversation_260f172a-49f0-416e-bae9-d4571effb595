import type { DecoratedNavigationPage } from '@mintlify/models';
import { OpenAPI, OpenAPIV3 } from 'openapi-types';
export declare const getOpenApiDefinition: (pathOrDocumentOrUrl: string | OpenAPI.Document | URL, localSchema?: boolean) => Promise<{
    document: OpenAPI.Document;
    isUrl: boolean;
}>;
export declare const createOpenApiFrontmatter: ({ filename, openApiMetaTag, version, deprecated, }: {
    filename: string;
    openApiMetaTag: string;
    version?: string;
    deprecated?: boolean;
}) => Promise<void>;
export type GenerateOpenApiPagesOptions = {
    openApiFilePath?: string;
    version?: string;
    writeFiles?: boolean;
    outDir?: string;
    outDirBasePath?: string;
    overwrite?: boolean;
    localSchema?: boolean;
};
export type OpenApiPageGenerationResult<N, DN> = {
    nav: N;
    decoratedNav: DN;
    spec: OpenAPI.Document;
    pagesAcc: Record<string, DecoratedNavigationPage>;
    isUrl: boolean;
};
export declare function processOpenApiPath<N, DN>(path: string, pathItemObject: OpenAPIV3.PathItemObject, schema: OpenAPI.Document, nav: N, decoratedNav: DN, writePromises: Promise<void>[], pagesAcc: Record<string, DecoratedNavigationPage>, options: GenerateOpenApiPagesOptions, findNavGroup: (nav: any, groupName?: string) => any): void;
export declare function processOpenApiWebhook<N, DN>(webhook: string, webhookObject: OpenAPIV3.PathItemObject, _schema: OpenAPI.Document, nav: N, decoratedNav: DN, writePromises: Promise<void>[], pagesAcc: Record<string, DecoratedNavigationPage>, options: GenerateOpenApiPagesOptions, findNavGroup: (nav: any, groupName?: string) => any): void;
