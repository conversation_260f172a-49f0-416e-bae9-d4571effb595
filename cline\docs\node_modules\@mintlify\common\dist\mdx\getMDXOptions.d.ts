import type { SerializeOptions } from '@mintlify/mdx';
import type { MintConfig, PageMetaTags } from '@mintlify/models';
import { DocsConfig } from '@mintlify/validation';
import type { Root } from 'mdast';
import type { PluggableList } from 'unified';
import { MdxExtracts } from '../types/mdx/MdxExtracts.js';
type MDXOptionsData = {
    snippetTreeMap: Record<string, Root>;
    allowedComponents: string[];
    pageMetadata: PageMetaTags;
    config?: DocsConfig | MintConfig;
    subdomain?: string;
    tailwindSelectors?: string[];
};
export declare const getMDXOptions: ({ data, remarkPlugins, rehypePlugins, mdxExtracts, }: {
    data: MDXOptionsData;
    remarkPlugins?: PluggableList;
    rehypePlugins?: PluggableList;
    mdxExtracts?: MdxExtracts;
}) => SerializeOptions["mdxOptions"];
export {};
