{"version": 3, "file": "yamlAST.js", "sourceRoot": "", "sources": ["../../src/yamlAST.ts"], "names": [], "mappings": ";;AAKA,IAAY,IAOX;AAPD,WAAY,IAAI;IACZ,mCAAM,CAAA;IACN,qCAAO,CAAA;IACP,6BAAG,CAAA;IACH,6BAAG,CAAA;IACH,2CAAU,CAAA;IACV,6CAAW,CAAA;AACf,CAAC,EAPW,IAAI,GAAJ,YAAI,KAAJ,YAAI,QAOf;AA0DD,SAAgB,UAAU,CAAC,GAAc,EAAC,KAAc;IACpD,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IAE5D,IAAI,IAAI,GAAG;QACT,GAAG,EAAE,GAAG;QACR,KAAK,EAAE,KAAK;QACZ,aAAa,EAAE,GAAG,CAAC,aAAa;QAChC,WAAW,EAAE,GAAG;QAChB,IAAI,EAAE,IAAI,CAAC,OAAO;QAClB,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,EAAE;KACb,CAAC;IACF,OAAO,IAAI,CAAA;AACb,CAAC;AAbD,gCAaC;AACD,SAAgB,YAAY,CAAC,GAAU,EAAC,KAAY,EAAC,GAAU,EAAC,KAAc;IAC1E,OAAO;QACH,MAAM,EAAC,EAAE;QACT,gBAAgB,EAAC,GAAG;QACpB,KAAK,EAAC,KAAK;QACX,aAAa,EAAC,KAAK;QACnB,WAAW,EAAC,GAAG;QACf,IAAI,EAAC,IAAI,CAAC,UAAU;QACpB,MAAM,EAAC,IAAI;KACd,CAAA;AACL,CAAC;AAVD,oCAUC;AACD,SAAgB,SAAS,CAAC,IAAwB,EAAE;IAChD,MAAM,MAAM,GAAc;QACtB,MAAM,EAAC,EAAE;QACT,aAAa,EAAC,CAAC,CAAC;QAChB,WAAW,EAAC,CAAC,CAAC;QACd,KAAK,EAAC,EAAE,GAAC,CAAC;QACV,IAAI,EAAC,IAAI,CAAC,MAAM;QAChB,MAAM,EAAC,IAAI;QACX,YAAY,EAAC,KAAK;QAClB,QAAQ,EAAC,EAAE,GAAC,CAAC;KAChB,CAAC;IACF,IAAG,OAAO,CAAC,KAAK,QAAQ,EAAC;QACrB,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC;KAC1B;IACD,OAAO,MAAM,CAAA;AACjB,CAAC;AAfD,8BAeC;AACD,SAAgB,QAAQ;IACpB,OAAO;QACH,MAAM,EAAC,EAAE;QACT,aAAa,EAAC,CAAC,CAAC;QAChB,WAAW,EAAC,CAAC,CAAC;QACd,KAAK,EAAC,EAAE;QACR,IAAI,EAAC,IAAI,CAAC,GAAG;QACb,MAAM,EAAC,IAAI;KACd,CAAA;AACL,CAAC;AATD,4BASC;AACD,SAAgB,MAAM;IAClB,OAAO,QAAQ,EAAE,CAAC;AACtB,CAAC;AAFD,wBAEC;AACD,SAAgB,MAAM,CAAC,QAAwB;IAC3C,OAAO;QACH,MAAM,EAAC,EAAE;QACT,aAAa,EAAC,CAAC,CAAC;QAChB,WAAW,EAAC,CAAC,CAAC;QACd,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAClC,IAAI,EAAC,IAAI,CAAC,GAAG;QACb,MAAM,EAAC,IAAI;KACd,CAAA;AACL,CAAC;AATD,wBASC"}