{"version": 3, "file": "spectral.js", "sourceRoot": "", "sources": ["../src/spectral.ts"], "names": [], "mappings": ";;;;AAAA,0CAA4C;AAC5C,4CAAsD;AACtD,kFAAuD;AACvD,4EAAuF;AAEvF,yCAAgG;AAChG,2DAAwD;AACxD,qCAAkC;AAGlC,+CAA4C;AAC5C,mFAAgF;AAChF,uCAAkD;AAElD,uDAAwB;AAExB,MAAa,QAAQ;IAKnB,YAA+B,IAAuB;QAAvB,SAAI,GAAJ,IAAI,CAAmB;QACpD,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,MAAK,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;SAChC;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,IAAA,iDAAyB,GAAE,CAAC;SAC9C;IACH,CAAC;IAES,aAAa,CAAC,MAAoE;QAC1F,OAAO,MAAM,YAAY,mBAAQ;YAC/B,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,IAAA,yBAAc,EAAC,MAAM,CAAC;gBACxB,CAAC,CAAC,IAAI,yBAAc,CAAC,MAAM,CAAC;gBAC5B,CAAC,CAAC,IAAI,mBAAQ,CACV,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAA,gBAAS,EAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAClE,OAAO,CAAC,IAAI,CACb,CAAC;IACR,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,MAAoE,EACpE,OAAiB,EAAE;QAEnB,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;SAC/E;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEzD,MAAM,SAAS,GAAG,IAAI,qCAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAClE,MAAM,SAAS,CAAC,OAAO,EAAE,CAAC;QAE1B,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,SAAS,CAAC,CAAC;QACrC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;QAE9F,IAAI,QAAQ,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE;YAC/B,MAAM,YAAY,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YACxG,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,EAAE;gBAClE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE;oBAC5B,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,gCAAgC,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBAChG;aACF;iBAAM;gBACL,QAAQ,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC;aAC1C;SACF;QAED,MAAM,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1B,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QAEpC,OAAO;YACL,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,OAAO;SACR,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,GAAG,CACd,MAAoE,EACpE,OAAiB,EAAE;QAEnB,OAAO,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;IAC5D,CAAC;IAEM,UAAU,CAAC,OAAoC;QACpD,IAAI,CAAC,OAAO,GAAG,OAAO,YAAY,iBAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,iBAAO,CAAC,OAAO,CAAC,CAAC;IAC7E,CAAC;IAEO,gCAAgC,CAAC,QAAmB,EAAE,OAAiB;QAC7E,OAAO,IAAA,uDAA0B,EAC/B,QAAQ,EACR,uEAAuE,OAAO;aAC3E,GAAG,CAAC,EAAE,CAAC,EAAE,WAAC,OAAA,MAAA,EAAE,CAAC,WAAW,mCAAI,EAAE,CAAC,IAAI,CAAA,EAAA,CAAC;aACpC,IAAI,CAAC,IAAI,CAAC,GAAG,EAChB,0BAAkB,CAAC,OAAO,EAC1B,qBAAqB,CACtB,CAAC;IACJ,CAAC;IAEO,mBAAmB,CACzB,WAA+C,EAC/C,aAA4B;QAE5B,OAAO,WAAW,CAAC,MAAM,CAAwB,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE;YAC3E,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ;gBAAE,OAAO,WAAW,CAAC;YAErD,IAAI,QAAQ,CAAC;YAEb,IAAI,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,iDAAiD,CAAC,EAAE;gBACpF,QAAQ,GAAG,IAAA,+BAAqB,EAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;aACpE;iBAAM,IAAI,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;gBACzD,QAAQ,GAAG,IAAA,+BAAqB,EAAC,aAAa,CAAC,aAAa,CAAC,CAAC;aAC/D;iBAAM;gBACL,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC7B,OAAO,WAAW,CAAC;aACpB;YAED,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;gBACnB,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC7B,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;aAChC;YAED,OAAO,WAAW,CAAC;QACrB,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;CACF;AA9GD,4BA8GC"}