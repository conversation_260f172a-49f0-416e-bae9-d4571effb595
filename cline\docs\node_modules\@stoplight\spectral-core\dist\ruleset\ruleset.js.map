{"version": 3, "file": "ruleset.js", "sourceRoot": "", "sources": ["../../src/ruleset/ruleset.ts"], "names": [], "mappings": ";;;;;AAAA,0CAAoD;AACpD,0CAA6F;AAE7F,iDAA8C;AAC9C,iCAA+C;AAS/C,8CAAwD;AACxD,2CAA4C;AAC5C,0BAAmE;AACnE,iDAAmD;AACnD,uCAAoC;AACpC,2CAAyD;AAGzD,MAAM,YAAY,GAAG,MAAM,CAAC,oCAAoC,CAAC,CAAC;AAClE,MAAM,iBAAiB,GAAG,MAAM,CAAC,gDAAgD,CAAC,CAAC;AACnF,MAAM,oBAAoB,GAAG,kCAAkC,CAAC;AAShE,IAAI,IAAI,GAAG,CAAC,CAAC;AAeb,MAAa,OAAO;IAalB,YAAqB,eAAwB,EAAE,OAAwB;;QAAlD,oBAAe,GAAf,eAAe,CAAS;;QAZ7B,OAAE,GAAG,IAAI,EAAE,CAAC;QAGZ,YAAO,GAAG,IAAI,iBAAO,EAAE,CAAC;QAOxC,mCAAgF;QAG9E,IAAI,UAA6B,CAAC;QAClC,IAAI,IAAA,oBAAa,EAAC,eAAe,CAAC,IAAI,SAAS,IAAI,eAAe,EAAE;YAClE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,eAAe,CAAC;YAE/C,IAAA,0BAAkB,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC;YAClD,UAAU,GAAG,eAAoC,CAAC;SACnD;aAAM;YACL,IAAA,0BAAkB,EAAC,eAAe,EAAE,IAAI,CAAC,CAAC;YAC1C,UAAU,GAAG,eAAe,CAAC;SAC9B;QAED,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,oCAAA,IAAI,oBAAY;YACd,QAAQ,EAAE,aAAa;YACvB,GAAG,OAAO;SACX,MAAA,CAAC;QAEF,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,OAAO;YACV,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC;gBAC3B,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBAC7C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;oBAE5B,IAAI,IAAA,gCAAuB,EAAC,KAAK,CAAC,EAAE;wBAClC,OAAO,KAAK,CAAC;qBACd;oBAED,iBAAiB,GAAG,IAAI,CAAC;oBAEzB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;wBAC3C,OAAO,EAAE,IAAI,iBAAO,CAAC,MAAM,CAAC,OAAO,CAAC;wBACpC,KAAK,EAAE,MAAM,CAAC,KAAK;qBACpB,CAAC,CAAC,CAAC;oBAEJ,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;gBACvC,CAAC,CAAC,CACH,CAAC;QAER,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAE3C,MAAM,KAAK,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,YAAY,CAAC,mCAAI,IAAI,GAAG,EAA8B,CAAC;QAE/E,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAEjC,IAAI,CAAC,OAAO;YACV,SAAS,IAAI,UAAU;gBACrB,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CACpF,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;oBACxB,IAAI,eAAe,CAAC;oBACpB,IAAI,QAAQ,GAAkC,aAAa,CAAC;oBAC5D,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBAElD,IAAI,gBAAgB,EAAE;wBACpB,CAAC,eAAe,EAAE,QAAQ,CAAC,GAAG,SAAS,CAAC;qBACzC;yBAAM;wBACL,eAAe,GAAG,SAAS,CAAC;qBAC7B;oBAED,MAAM,gBAAgB,GAAG,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;oBAEpD,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE;wBAC/B,OAAO,UAAU,CAAC;qBACnB;oBAED,UAAU,CAAC,IAAI,CACb,IAAI,OAAO,CAAC,eAAe,EAAE;wBAC3B,QAAQ;wBACR,CAAC,YAAY,CAAC,EAAE,KAAK;wBACrB,CAAC,iBAAiB,CAAC,EAAE,gBAAgB;qBACtC,CAAC,CACH,CAAC;oBACF,OAAO,UAAU,CAAC;gBACpB,CAAC,EACD,EAAE,CACH;gBACH,CAAC,CAAC,IAAI,CAAC;QAEX,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,EAAE;YAC5C,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;SACvC;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACvB;QAED,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE9B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC1C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;gBAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aAC1B;SACF;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC/B,KAAK,MAAM,EAAE,OAAO,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE;gBACtC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;oBAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBAC1B;aACF;SACF;QAED,IAAI,CAAC,KAAK,GAAG,oCAAA,IAAI,6CAAU,MAAd,IAAI,CAAY,CAAC;IAChC,CAAC;IAED,IAAI,MAAM;;QACR,OAAO,MAAA,oCAAA,IAAI,wBAAS,CAAC,MAAM,mCAAI,IAAI,CAAC;IACtC,CAAC;IAEM,UAAU,CAAC,MAAqB;QACrC,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YAC3B,OAAO,IAAI,CAAC;SACb;QAED,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAEvC,IAAI,MAAM,KAAK,IAAI,EAAE;YACnB,MAAM,IAAI,KAAK,CACb,wHAAwH,CACzH,CAAC;SACH;QAED,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CACb,sHAAsH,CACvH,CAAC;SACH;QAED,MAAM,cAAc,GAAG,IAAA,eAAQ,EAAC,IAAA,cAAO,EAAC,aAAa,CAAC,EAAE,MAAM,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAMlB,EAAE,CAAC;QAEP,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE;;YACjE,MAAM,aAAa,GAAa,EAAE,CAAC;YAEnC,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE;gBAC3B,MAAM,aAAa,GAAG,MAAA,IAAA,2BAAoB,EAAC,OAAO,CAAC,mCAAI,OAAO,CAAC;gBAE/D,IAAI,CAAC,IAAA,qBAAS,EAAC,cAAc,EAAE,aAAa,CAAC;oBAAE,SAAS;gBAExD,MAAM,OAAO,GAAG,IAAA,4BAAqB,EAAC,OAAO,CAAC,CAAC;gBAE/C,IAAI,aAAa,KAAK,OAAO,EAAE;oBAC7B,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC7B;qBAAM,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK,IAAI,EAAE;oBACpD,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;iBACtE;qBAAM;oBACL,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC5D,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;4BACzD,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;yBACtE;wBAED,MAAM,EAAE,UAAU,EAAE,oBAAoB,EAAE,GAAG,OAAC,gBAAgB,CAAC,QAAQ,qCAAzB,gBAAgB,CAAC,QAAQ,IAAM;4BAC3E,aAAa;4BACb,UAAU,EAAE,IAAI,GAAG,EAAE;yBACtB,EAAC,CAAC;wBAEH,MAAM,QAAQ,GAAG,IAAA,yBAAqB,EAAC,IAAI,CAAC,CAAC;wBAC7C,IAAI,0BAA0B,GAAG,oBAAoB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;wBAEzE,IAAI,0BAA0B,KAAK,KAAK,CAAC,EAAE;4BACzC,0BAA0B,GAAG,IAAI,GAAG,EAAE,CAAC;4BACvC,oBAAoB,CAAC,GAAG,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAC;yBACrE;wBAED,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;qBACnD;iBACF;aACF;YAED,OAAO,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QAExD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACxE,OAAO,IAAI,CAAC;SACb;QAED,MAAM,eAAe,GACnB,SAAS,CAAC,MAAM,KAAK,CAAC;YACpB,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;gBACtB,CAAC,CAAC,SAAS;qBACN,KAAK,CAAC,CAAC,CAAC;qBACR,MAAM,CACL,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,IAAA,wBAAa,EAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,EACjD,SAAS,CAAC,CAAC,CAAsB,CAClC;gBACL,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEnB,MAAM,OAAO,GAAG,IAAI,OAAO,CACzB,eAAe,KAAK,IAAI,CAAC,CAAC,CAAE,UAAgC,CAAC,CAAC,CAAC,IAAA,wBAAa,EAAC,UAAU,EAAE,eAAe,EAAE,KAAK,CAAC,EAChH;YACE,QAAQ,EAAE,aAAa;YACvB,MAAM,EAAE,aAAa;SACtB,CACF,CAAC;QAEF,KAAK,MAAM,CAAC,QAAQ,EAAE,oBAAoB,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;YAC/E,IAAI,QAAQ,IAAI,OAAO,CAAC,KAAK,EAAE;gBAC7B,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,SAAS,GAAG,oBAAoB,CAAC;aAC1D;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IA6CD,IAAW,aAAa;QACtB,OAAO,EAAE,GAAG,0BAAsB,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;IACzE,CAAC;IAEM,MAAM,CAAC,oBAAoB,CAAC,GAAW;QAC5C,OAAO,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAEM,MAAM;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;YACtD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC;IACJ,CAAC;CACF;AAlSD,0BAkSC;;IA9DG,MAAM,KAAK,GAAyB,EAAE,CAAC;IAEvC,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;QACpD,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,OAAO,EAAE;YAC1C,IAAI,eAAe,KAAK,IAAI;gBAAE,SAAS;YACvC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;gBACvD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;gBACxB,IAAI,oCAAA,IAAI,wBAAS,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,IAAI,oCAAA,IAAI,wBAAS,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;oBACvF,IAAI,CAAC,OAAO,GAAG,WAAI,CAAC,SAAS,CAAC,IAAI,EAAE,oCAAA,IAAI,wBAAS,CAAC,QAAQ,CAAC,CAAC;iBAC7D;aACF;SACF;KACF;IAED,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;QAC9B,KAAK,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;YACtE,MAAM,IAAI,GAAG,IAAA,iBAAS,EAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YAC5D,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YAEnB,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;gBACvB,IAAI,CAAC,OAAO,GAAG,WAAI,CAAC,SAAS,CAAC,IAAI,EAAE,oCAAA,IAAI,wBAAS,CAAC,QAAQ,CAAC,CAAC;aAC7D;YAED,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;gBACzB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;oBACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBAC1B;aACF;iBAAM,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;gBAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,iBAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;aAC7G;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE;gBAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;aACrD;YAED,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;gBACjF,IAAI,CAAC,gBAAgB,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;aACvE;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}