import { BaseConverter } from './BaseConverter.js';
export class ServersConverter extends BaseConverter {
    constructor(servers) {
        super();
        this.servers = servers;
    }
    convert() {
        if (this.servers === undefined || this.servers.length === 0) {
            return undefined;
        }
        return this.servers.map(({ url, description, variables }) => {
            return {
                url,
                description,
                variables: this.convertVariables(variables),
            };
        });
    }
    convertVariables(variables) {
        if (variables === undefined || Object.keys(variables).length === 0) {
            return undefined;
        }
        const newEntries = Object.entries(variables).map(([name, variable]) => {
            if (variable.enum) {
                return [
                    name,
                    {
                        type: 'enum<string>',
                        enum: variable.enum,
                        description: variable.description,
                        default: variable.default,
                    },
                ];
            }
            return [
                name,
                {
                    type: 'string',
                    description: variable.description,
                    default: variable.default,
                },
            ];
        });
        return Object.fromEntries(newEntries);
    }
    static convert(servers) {
        return new ServersConverter(servers).convert();
    }
}
