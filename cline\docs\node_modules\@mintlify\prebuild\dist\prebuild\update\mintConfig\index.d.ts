import { DecoratedNavigationPage, MintConfig, OpenApiFile } from '@mintlify/models';
export declare function updateMintConfigFile(contentDirectoryPath: string, openApiFiles: OpenApiFile[], localSchema?: boolean): Promise<{
    mintConfig: MintConfig;
    originalMintConfig: MintConfig;
    pagesAcc: Record<string, DecoratedNavigationPage>;
    openApiFiles: OpenApiFile[];
} | null>;
export { generateOpenApiAnchorsOrTabs, generateOpenApiAnchorOrTab, } from './generateOpenApiAnchorsOrTabs.js';
