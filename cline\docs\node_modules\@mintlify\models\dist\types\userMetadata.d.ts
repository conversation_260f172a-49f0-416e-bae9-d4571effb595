import { UserType } from '../entities/userType.js';
import { Permission } from './authorization/resource.js';
import { Role } from './authorization/role.js';
export type UserMetadata = {
    isPaid: boolean;
    isOnTrial: boolean;
    shouldShowTrialBanner: boolean;
    isAdminUser: boolean;
};
export type UserAuthorizationMetadata = {
    roles?: Role[];
    permissions?: Permission[];
};
export type UserWithMetadata = UserType & UserAuthorizationMetadata & {
    metadata: UserMetadata;
};
