{"version": 3, "file": "regexp.js", "sourceRoot": "", "sources": ["../../../../src/type/js/regexp.ts"], "names": [], "mappings": "AAEA,YAAY,CAAC;AAEb,qCAAgC;AAEhC,SAAS,uBAAuB,CAAC,IAAI;IACnC,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;QACrB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,MAAM,GAAG,IAAI,EACb,IAAI,GAAK,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,SAAS,GAAG,EAAE,CAAC;IAInB,IAAI,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;QACrB,IAAI,IAAI,EAAE;YACR,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;SACrB;QAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAAE,OAAO,KAAK,CAAC;SAAE;QAE3C,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YAAE,OAAO,KAAK,CAAC;SAAE;QAE3E,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAChE;IAED,IAAI;QACF,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,SAAS,yBAAyB,CAAC,IAAI;IACrC,IAAI,MAAM,GAAG,IAAI,EACb,IAAI,GAAK,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,SAAS,GAAG,EAAE,CAAC;IAGnB,IAAI,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;QACrB,IAAI,IAAI,EAAE;YACR,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;SACrB;QACD,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAChE;IAED,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,yBAAyB,CAAC,MAAM;IACvC,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;IAEvC,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,MAAM,IAAI,GAAG,CAAC;KACf;IAED,IAAI,MAAM,CAAC,SAAS,EAAE;QACpB,MAAM,IAAI,GAAG,CAAC;KACf;IAED,IAAI,MAAM,CAAC,UAAU,EAAE;QACrB,MAAM,IAAI,GAAG,CAAC;KACf;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,QAAQ,CAAC,MAAM;IACtB,OAAO,iBAAiB,KAAK,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtE,CAAC;AAED,iBAAS,IAAI,WAAI,CAAC,6BAA6B,EAAE;IAC/C,IAAI,EAAE,QAAQ;IACd,OAAO,EAAE,uBAAuB;IAChC,SAAS,EAAE,yBAAyB;IACpC,SAAS,EAAE,QAAQ;IACnB,SAAS,EAAE,yBAAyB;CACrC,CAAC,CAAC"}