{"name": "@mintlify/models", "version": "0.0.207", "description": "Mintlify models", "engines": {"node": ">=18.0.0"}, "author": "Mintlify, Inc.", "bugs": {"url": "https://github.com/mintlify/docs/issues"}, "license": "Elastic-2.0", "keywords": ["mintlify", "mint", "models"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "type": "module", "sideEffects": false, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"prepare": "npm run build", "build": "tsc", "clean:build": "<PERSON><PERSON><PERSON> dist", "clean:all": "rimraf node_modules .eslintcache && yarn clean:build", "watch": "tsc --watch", "format": "prettier \"./src/**/*.ts\" --write", "format:check": "prettier \"./src/**/*.ts\" --check", "lint": "eslint src --cache", "type": "tsc --noEmit"}, "dependencies": {"axios": "^1.8.3", "openapi-types": "^12.0.0"}, "devDependencies": {"@mintlify/eslint-config-typescript": "1.0.13", "@mintlify/prettier-config": "1.0.4", "@mintlify/ts-config": "2.0.2", "@trivago/prettier-plugin-sort-imports": "^4.2.1", "@tsconfig/recommended": "1.x", "@types/node": "^18.7.13", "@typescript-eslint/parser": "6.x", "eslint": "8.x", "prettier": "^3.1.1", "rimraf": "^5.0.1", "typescript": "^5.5.3"}, "gitHead": "8d05f7f71ec30ad9738917d67694fba55dee67ab"}