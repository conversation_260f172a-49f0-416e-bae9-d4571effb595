/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Link, PhrasingContent} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Link}
 *   mdast node.
 */
export function a(state: State, node: Readonly<Element>): Link;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Link } from 'mdast';
//# sourceMappingURL=a.d.ts.map