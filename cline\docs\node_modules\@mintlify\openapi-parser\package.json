{"name": "@mintlify/openapi-parser", "description": "modern OpenAPI parser written in TypeScript", "license": "MIT", "author": "Scalar (https://github.com/scalar)", "homepage": "https://github.com/scalar/scalar", "bugs": "https://github.com/scalar/scalar/issues/new/choose", "repository": {"type": "git", "url": "https://github.com/scalar/scalar.git", "directory": "packages/openapi-parser"}, "keywords": ["openapi", "scalar", "swagger", "parser", "typescript"], "version": "0.0.7", "engines": {"node": ">=18"}, "scripts": {"build": "scalar-build-rollup", "format": "prettier --write .", "lint:check": "eslint .", "lint:fix": "eslint .  --fix", "test": "vitest", "test:prepare": "vite-node scripts/load-files.ts", "test:unit": "vite-node scripts/load-files.ts && vitest", "types:build": "scalar-types-build", "types:check": "scalar-types-check"}, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./plugins/fetch-urls": {"import": "./dist/plugins/fetch-urls/index.js", "types": "./dist/plugins/fetch-urls/index.d.ts", "default": "./dist/plugins/fetch-urls/index.js"}, "./plugins/read-files": {"import": "./dist/plugins/read-files/index.js", "types": "./dist/plugins/read-files/index.d.ts", "default": "./dist/plugins/read-files/index.js"}}, "files": ["dist", "CHANGELOG.md"], "module": "./dist/index.js", "sideEffects": false, "dependencies": {"ajv": "^8.17.1", "ajv-draft-04": "^1.0.0", "ajv-formats": "^3.0.1", "jsonpointer": "^5.0.1", "leven": "^4.0.0", "yaml": "^2.4.5"}, "devDependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@babel/code-frame": "^7.24.7", "@google-cloud/storage": "^7.12.1", "@mintlify/build-tooling": "workspace:*", "@mintlify/openapi-types": "0.0.0", "@types/node": "^20.14.10", "glob": "^10.3.10", "json-to-ast": "^2.1.0", "just-diff": "^6.0.2", "tinybench": "^2.8.0"}}