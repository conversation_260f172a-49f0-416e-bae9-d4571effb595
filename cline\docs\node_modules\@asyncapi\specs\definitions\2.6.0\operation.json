{"type": "object", "description": "Describes a publish or a subscribe operation. This provides a place to document how and why messages are sent and received.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "properties": {"traits": {"type": "array", "description": "A list of traits to apply to the operation object.", "items": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.6.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/operationTrait.json"}]}}, "summary": {"type": "string", "description": "A short summary of what the operation is about."}, "description": {"type": "string", "description": "A verbose explanation of the operation."}, "security": {"type": "array", "description": "A declaration of which security mechanisms are associated with this operation.", "items": {"$ref": "http://asyncapi.com/definitions/2.6.0/SecurityRequirement.json"}}, "tags": {"type": "array", "description": "A list of tags for logical grouping and categorization of operations.", "items": {"$ref": "http://asyncapi.com/definitions/2.6.0/tag.json"}, "uniqueItems": true}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/2.6.0/externalDocs.json"}, "operationId": {"type": "string"}, "bindings": {"$ref": "http://asyncapi.com/definitions/2.6.0/bindingsObject.json"}, "message": {"$ref": "http://asyncapi.com/definitions/2.6.0/message.json"}}, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/channels.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/operation.json"}