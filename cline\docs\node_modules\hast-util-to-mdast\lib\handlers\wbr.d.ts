/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Text} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Text}
 *   mdast node.
 */
export function wbr(state: State, node: Readonly<Element>): Text;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Text } from 'mdast';
//# sourceMappingURL=wbr.d.ts.map