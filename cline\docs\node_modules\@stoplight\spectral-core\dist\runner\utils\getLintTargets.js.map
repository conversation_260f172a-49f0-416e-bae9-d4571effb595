{"version": 3, "file": "getLintTargets.js", "sourceRoot": "", "sources": ["../../../src/runner/utils/getLintTargets.ts"], "names": [], "mappings": ";;;AACA,iDAAyC;AACzC,mCAA+C;AAOxC,MAAM,cAAc,GAAG,CAAC,WAAoB,EAAE,KAAuB,EAAiB,EAAE;IAC7F,MAAM,OAAO,GAAkB,EAAE,CAAC;IAElC,IAAI,IAAA,iBAAQ,EAAC,WAAW,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACtD,IAAI,KAAK,KAAK,MAAM,EAAE;YACpB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC1C,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,CAAC,GAAG,CAAC;oBACX,KAAK,EAAE,GAAG;iBACX,CAAC,CAAC;aACJ;SACF;aAAM,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAChC,IAAA,wBAAQ,EAAC;gBACP,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,WAAW;gBACjB,UAAU,EAAE,KAAK;gBACjB,QAAQ,CAAC,MAAM;oBACb,OAAO,CAAC,IAAI,CAAC;wBAEX,IAAI,EAAE,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAElC,KAAK,EAAE,MAAM,CAAC,KAAK;qBACpB,CAAC,CAAC;gBACL,CAAC;aACF,CAAC,CAAC;SACJ;aAAM;YAEL,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,IAAA,eAAM,EAAC,KAAK,CAAC;gBACnB,KAAK,EAAE,IAAA,YAAG,EAAC,WAAW,EAAE,KAAK,CAAC;aAC/B,CAAC,CAAC;SACJ;KACF;SAAM;QACL,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;KACJ;IAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QAExB,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,KAAK,CAAC;SACd,CAAC,CAAC;KACJ;IAED,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAhDW,QAAA,cAAc,kBAgDzB"}