import { SpecificationExtensionsModel } from './mixins';
import { Info } from './info';
import { Server } from './server';
import { Channel } from './channel';
import { Components } from './components';
import { Message } from './message';
import { Schema } from './schema';
import type { v2 } from '../spec-types';
import type { SchemaTypesToIterate, TraverseCallback } from './iterator';
export declare class AsyncAPIDocument extends SpecificationExtensionsModel<v2.AsyncAPIObject> {
    version(): string;
    info(): Info;
    id(): string | undefined;
    externalDocs(): import("./external-docs").ExternalDocs | null;
    hasExternalDocs(): boolean;
    hasTags(): boolean;
    tags(): import("./tag").Tag[];
    tagNames(): string[];
    hasTag(name: string): boolean;
    tag(name: string): import("./tag").Tag | null;
    hasServers(): boolean;
    servers(): Record<string, Server>;
    serverNames(): string[];
    server(name: string): Server | null;
    hasDefaultContentType(): boolean;
    defaultContentType(): string | null;
    hasChannels(): boolean;
    channels(): Record<string, Channel>;
    channelNames(): string[];
    channel(name: string): Channel | null;
    hasComponents(): boolean;
    components(): Components | null;
    hasMessages(): boolean;
    allMessages(): Map<string, Message>;
    allSchemas(): Map<string, Schema>;
    hasCircular(): boolean;
    traverseSchemas(callback: TraverseCallback, schemaTypesToIterate?: Array<`${SchemaTypesToIterate}`>): void;
    static stringify(doc: AsyncAPIDocument, space?: number): string | undefined;
    static parse(doc: string | Record<string, any>): AsyncAPIDocument | undefined;
}
