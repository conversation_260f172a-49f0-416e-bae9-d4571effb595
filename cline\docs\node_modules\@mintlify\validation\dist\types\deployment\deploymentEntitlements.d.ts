import { EntitlementConfiguration } from '@mintlify/models';
export type DeploymentEntitlements = Partial<Record<DeploymentEntitlement | AuthEntitlement | UserAuthEntitlement, EntitlementConfiguration>>;
export type AuthEntitlement = (typeof authEntitlements)[number];
export type UserAuthEntitlement = (typeof userAuthEntitlements)[number];
export declare const authEntitlements: readonly ["AUTH", "AUTH_OAUTH", "AUTH_PASSWORD", "AUTH_JWT", "AUTH_MINTLIFY"];
export declare const userAuthEntitlements: readonly ["USER_AUTH", "PERSONALIZATION_JWT", "PERSONALIZATION_OAUTH", "PERSONALIZATION_SHARED_SESSION"];
export declare const deploymentEntitlements: readonly ["AI_ASSISTANT", "AI_CHAT", "ANALYTICS", "CUSTOM_CSS_JS", "CUSTOM_SUBPATH", "DISCOVERY_API_ACCESS", "PARTIAL_AUTH", "PREVIEW_DEPLOYMENTS", "PREVIEW_DEPLOYMENT_AUTH", "REMOVE_BRANDING", "UNLIMITED_EDITORS", "WORKFLOW_API_ACCESS", "SOURCE_CHECK_LINK_ROT", "SOURCE_CHECK_VALE_SPELLCHECK", "LLM_LOCALIZATION", "EXPORT_PDF", "CHAT_PROJECT", "AUTH", "AUTH_OAUTH", "AUTH_PASSWORD", "AUTH_JWT", "AUTH_MINTLIFY", "USER_AUTH", "PERSONALIZATION_JWT", "PERSONALIZATION_OAUTH", "PERSONALIZATION_SHARED_SESSION"];
export type DeploymentEntitlement = (typeof deploymentEntitlements)[number];
