import type { Root } from 'mdast';
import type { FindAndRemoveImportsResponse } from '../../types/mdx/index.js';
/**
  An import like "import {Chart} from './chart.mdx'" looks like this in the AST:
  {
    type: 'mdxjsEsm',
    data: {
      estree: {
        type: 'Program',
        sourceType: 'module',
        body: [{
          type: 'ImportDeclaration',
          source: {
            value: './chart.mdx'
          },
          specifiers: [{
            type: 'ImportSpecifier',
            local: {
              type: 'Identifier',
              name: 'Chart'
            }
          }]
        }]
      }
    }
  }
 * this function takes in a tree finds and removes imports in it (by detecting the node with the above characteristics)
 * @param tree mdx file tree
 * @returns importMap - an array of the imports
 * tree - the mdx file tree with stripped imports
 */
export declare const findAndRemoveImports: (tree: Root) => Promise<FindAndRemoveImportsResponse>;
