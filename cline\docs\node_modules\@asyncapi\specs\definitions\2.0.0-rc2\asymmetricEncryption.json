{"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["asymmetricEncryption"]}, "description": {"type": "string"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc2/asymmetricEncryption.json"}