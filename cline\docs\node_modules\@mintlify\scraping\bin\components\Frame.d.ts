import type { Element } from 'hast';
import type { <PERSON><PERSON><PERSON><PERSON>, HastNodeIndex, HastNodeParent } from '../types/hast.js';
export declare function gitBookScrapeFrame(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
export declare function readmeScrapeFrame(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
export declare function docusaurusScrapeFrame(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
