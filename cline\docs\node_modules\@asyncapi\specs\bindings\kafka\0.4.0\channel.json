{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/kafka/0.4.0/channel.json", "title": "Channel Schema", "description": "This object contains information about the channel representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"topic": {"type": "string", "description": "Kafka topic name if different from channel name."}, "partitions": {"type": "integer", "minimum": 1, "description": "Number of partitions configured on this topic."}, "replicas": {"type": "integer", "minimum": 1, "description": "Number of replicas configured on this topic."}, "topicConfiguration": {"description": "Topic configuration properties that are relevant for the API.", "type": "object", "additionalProperties": false, "properties": {"cleanup.policy": {"description": "The [`cleanup.policy`](https://kafka.apache.org/documentation/#topicconfigs_cleanup.policy) configuration option.", "type": "array", "items": {"type": "string", "enum": ["compact", "delete"]}}, "retention.ms": {"description": "The [`retention.ms`](https://kafka.apache.org/documentation/#topicconfigs_retention.ms) configuration option.", "type": "integer", "minimum": -1}, "retention.bytes": {"description": "The [`retention.bytes`](https://kafka.apache.org/documentation/#topicconfigs_retention.bytes) configuration option.", "type": "integer", "minimum": -1}, "delete.retention.ms": {"description": "The [`delete.retention.ms`](https://kafka.apache.org/documentation/#topicconfigs_delete.retention.ms) configuration option.", "type": "integer", "minimum": 0}, "max.message.bytes": {"description": "The [`max.message.bytes`](https://kafka.apache.org/documentation/#topicconfigs_max.message.bytes) configuration option.", "type": "integer", "minimum": 0}}}, "bindingVersion": {"type": "string", "enum": ["0.4.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"topic": "my-specific-topic", "partitions": 20, "replicas": 3, "bindingVersion": "0.4.0"}]}