import { z } from 'zod';
import { almondConfigSchema } from './themes/almond.js';
import { aspenConfigSchema } from './themes/aspen.js';
import { lindenConfigSchema } from './themes/linden.js';
import { mapleConfigSchema } from './themes/maple.js';
import { mintConfigSchema } from './themes/mint.js';
import { palmConfigSchema } from './themes/palm.js';
import { willowConfigSchema } from './themes/willow.js';
export const docsConfigSchema = z.discriminatedUnion('theme', [
    mintConfigSchema,
    mapleConfigSchema,
    palmConfigSchema,
    willowConfigSchema,
    lindenConfigSchema,
    almondConfigSchema,
    aspenConfigSchema,
]);
