{"type": "object", "description": "Allows configuration of the supported OAuth Flows.", "required": ["type", "flows"], "properties": {"type": {"type": "string", "description": "The type of the security scheme.", "enum": ["oauth2"]}, "description": {"type": "string", "description": "A short description for security scheme."}, "flows": {"type": "object", "properties": {"implicit": {"description": "Configuration for the OAuth Implicit flow.", "allOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/oauth2Flow.json"}, {"required": ["authorizationUrl", "availableScopes"]}, {"not": {"required": ["tokenUrl"]}}]}, "password": {"description": "Configuration for the OAuth Resource Owner Protected Credentials flow.", "allOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/oauth2Flow.json"}, {"required": ["tokenUrl", "availableScopes"]}, {"not": {"required": ["authorizationUrl"]}}]}, "clientCredentials": {"description": "Configuration for the OAuth Client Credentials flow.", "allOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/oauth2Flow.json"}, {"required": ["tokenUrl", "availableScopes"]}, {"not": {"required": ["authorizationUrl"]}}]}, "authorizationCode": {"description": "Configuration for the OAuth Authorization Code flow.", "allOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/oauth2Flow.json"}, {"required": ["authorizationUrl", "tokenUrl", "availableScopes"]}]}}, "additionalProperties": false}, "scopes": {"type": "array", "description": "List of the needed scope names.", "items": {"type": "string"}}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/oauth2Flows.json"}