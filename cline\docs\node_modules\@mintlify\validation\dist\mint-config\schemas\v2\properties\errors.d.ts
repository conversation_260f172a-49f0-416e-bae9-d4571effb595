import { z } from 'zod';
export declare const errorsSchema: z.ZodObject<{
    '404': z.ZodObject<{
        redirect: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    }, "strip", z.<PERSON>ny, {
        redirect: boolean;
    }, {
        redirect?: boolean | undefined;
    }>;
}, "strip", z.Z<PERSON>, {
    '404': {
        redirect: boolean;
    };
}, {
    '404': {
        redirect?: boolean | undefined;
    };
}>;
