export type StripeDeploymentDataType = {
    subscriptionItemId: string;
    addOns?: {
        cssAndJs?: string;
        previewDeployments?: string;
    };
    meteredBilling?: {
        aiChat?: string;
    };
};
export type StripeOrgDataType = {
    customerId: string;
};
export declare const deploymentAddOnTypes: readonly ["cssAndJs", "previewDeployments"];
export type DeploymentAddOnType = (typeof deploymentAddOnTypes)[number];
