export type TrieveType = {
    datasetId: string;
    config?: TrieveConfigType;
};
export type TrieveConfigType = {
    systemPrompt?: string;
    ragPrompt?: string;
    model?: ModelType;
};
export declare const ANTHROPIC_PREMIUM_MODELS: readonly ["claude-sonnet-4-20250514", "claude-3-7-sonnet-20250219"];
export type AnthropicPremiumModel = (typeof ANTHROPIC_PREMIUM_MODELS)[number];
export declare const ANTHROPIC_MODELS: readonly ["claude-sonnet-4-20250514", "claude-3-7-sonnet-20250219"];
export type AnthropicModel = (typeof ANTHROPIC_MODELS)[number];
export declare const OPENAI_PREMIUM_MODELS: readonly ["gpt-4.1-2025-04-14", "gpt-4o-2024-08-06"];
export type OpenAIPremiumModel = (typeof OPENAI_PREMIUM_MODELS)[number];
export declare const OPENAI_REGULAR_MODELS: readonly ["o3-mini-2025-01-31"];
export type OpenAIRegularModel = (typeof OPENAI_REGULAR_MODELS)[number];
export declare const OPENAI_MODELS: readonly ["gpt-4.1-2025-04-14", "gpt-4o-2024-08-06", "o3-mini-2025-01-31"];
export type OpenAIModel = (typeof OPENAI_MODELS)[number];
export declare const GEMINI_PREMIUM_MODELS: readonly ["gemini/gemini-2.5-pro-preview-03-25"];
export type GeminiPremiumModel = (typeof GEMINI_PREMIUM_MODELS)[number];
export declare const GEMINI_MODELS: readonly ["gemini/gemini-2.5-pro-preview-03-25"];
export type GeminiModel = (typeof GEMINI_MODELS)[number];
export declare const DEEPSEEK_PREMIUM_MODELS: readonly ["deepseek-r1-distill-llama-70b"];
export type DeepSeekPremiumModel = (typeof DEEPSEEK_PREMIUM_MODELS)[number];
export declare const DEEPSEEK_MODELS: readonly ["deepseek-r1-distill-llama-70b"];
export type DeepSeekModel = (typeof DEEPSEEK_MODELS)[number];
export declare const GROQ_PREMIUM_MODELS: readonly ["meta-llama/llama-4-maverick-17b-128e-instruct"];
export type GroqPremiumModel = (typeof GROQ_PREMIUM_MODELS)[number];
export declare const SAMBANOVA_MODELS: readonly ["sambanova/llama-3.3-70b-instruct", "sambanova/deepseekv3-0324"];
export type SambanovaModel = (typeof SAMBANOVA_MODELS)[number];
export declare const GROQ_MODELS: readonly ["meta-llama/llama-4-maverick-17b-128e-instruct"];
export type GroqModel = (typeof GROQ_MODELS)[number];
export declare const GROK_PREMIUM_MODELS: readonly ["grok-3"];
export type GrokPremiumModel = (typeof GROK_PREMIUM_MODELS)[number];
export declare const GROK_MODELS: readonly ["grok-3"];
export type GrokModel = (typeof GROK_MODELS)[number];
export declare const ALL_MODELS: readonly ["claude-sonnet-4-20250514", "claude-3-7-sonnet-20250219", "gpt-4.1-2025-04-14", "gpt-4o-2024-08-06", "o3-mini-2025-01-31", "deepseek-r1-distill-llama-70b", "gemini/gemini-2.5-pro-preview-03-25", "meta-llama/llama-4-maverick-17b-128e-instruct", "grok-3", "sambanova/llama-3.3-70b-instruct", "sambanova/deepseekv3-0324"];
export type ModelType = (typeof ALL_MODELS)[number];
export declare const PREMIUM_MODELS: readonly ["claude-sonnet-4-20250514", "claude-3-7-sonnet-20250219", "gpt-4.1-2025-04-14", "gpt-4o-2024-08-06", "deepseek-r1-distill-llama-70b", "gemini/gemini-2.5-pro-preview-03-25", "meta-llama/llama-4-maverick-17b-128e-instruct", "grok-3"];
export type PremiumModel = (typeof PREMIUM_MODELS)[number];
export declare const REGULAR_MODELS: readonly ["o3-mini-2025-01-31"];
export type RegularModel = (typeof REGULAR_MODELS)[number];
export declare const CEREBRAS_HOSTED_MODELS: readonly ["deepseek-r1-distill-llama-70b"];
export type CerebrasModel = (typeof CEREBRAS_HOSTED_MODELS)[number];
export type SupportedModelType = OpenAIModel | AnthropicModel | GeminiModel | GroqModel | GrokModel;
export declare const DEFAULT_MODEL: "claude-sonnet-4-20250514";
