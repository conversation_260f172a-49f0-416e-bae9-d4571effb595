const MAX_DEPTH = 100;
export const generateExampleFromSchema = (schema, depth = 0) => {
    if (schema.example !== undefined) {
        return schema.example;
    }
    if (schema.default !== undefined) {
        return schema.default;
    }
    switch (schema.type) {
        case 'string':
            return generateStringExample(schema);
        case 'boolean':
            return true;
        case 'number':
        case 'integer':
            return generateNumericExample(schema);
        case 'enum<string>':
        case 'enum<number>':
        case 'enum<integer>':
            return schema.enum[0];
        case 'null':
            return null;
        case 'any':
            return '<any>';
        case 'object':
            const entries = Object.entries(schema.properties)
                // generate examples for all properties until depth reached - then only required properties
                .filter(([_, [{ required }]]) => depth < MAX_DEPTH || required)
                .map(([propName, subschema]) => [
                propName,
                generateExampleFromSchema(subschema[0], depth + 1),
            ]);
            return Object.fromEntries(entries);
        case 'array':
            return depth < MAX_DEPTH
                ? [
                    generateExampleFromSchema(schema.items[0], 
                    // prevent the weird [{}] example value by counting array<object> as one level
                    schema.items[0].type === 'object' ? depth : depth + 1),
                ]
                : [];
    }
};
const generateStringExample = (schema) => {
    var _a;
    switch ((_a = schema.format) === null || _a === void 0 ? void 0 : _a.toLowerCase()) {
        case 'byte':
        case 'base64':
            return 'aSDinaTvuI8gbWludGxpZnk=';
        case 'date':
            return '2023-12-25';
        case 'date-time':
            return '2023-11-07T05:31:56Z';
        case 'email':
            return '<EMAIL>';
        case 'uuid':
            return '3c90c3cc-0d44-4b50-8888-8dd25736052a';
        case 'ipv4':
            return '127.0.0.1';
        case 'ipv6':
            return '2606:4700:3108::ac42:2835';
        default:
            return '<string>';
    }
};
const generateNumericExample = (schema) => {
    // if schema type is integer, make sure example is integer
    const format = schema.type === 'integer' ? Math.floor : (n) => n;
    if (schema.minimum !== undefined && schema.maximum !== undefined) {
        return format((schema.minimum + schema.maximum) / 2);
    }
    else if (schema.minimum !== undefined) {
        return format(schema.minimum + 1);
    }
    else if (schema.maximum !== undefined) {
        return 123 < schema.maximum ? 123 : format(schema.maximum - 1);
    }
    else {
        return 123;
    }
};
