{"version": 3, "file": "metadata.js", "sourceRoot": "", "sources": ["../../src/utils/metadata.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,MAAM,UAAU,0BAA0B;IACxC,OAAO,UAAU,IAAe;QAC9B,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAe;IAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,eAAe,EAAE,CAAC;QACnD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,WAAW,EAAE,CAAC;QAC/C,IAAI,YAAY,GAAG,KAAgB,CAAC;QACpC,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,IAAI;YAChC,IACE,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBAC3E,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAC1B,CAAC;gBACD,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,YAAY;YAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;IACxC,CAAC;AACH,CAAC"}