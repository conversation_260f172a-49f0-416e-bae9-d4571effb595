import type { Element } from 'hast';
import type { <PERSON><PERSON><PERSON><PERSON>, HastNodeIndex, HastNodeParent } from '../types/hast.js';
export declare function gitBookScrapeCallout(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
export declare function readmeScrapeCallout(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
export declare function docusaurusScrapeCallout(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
