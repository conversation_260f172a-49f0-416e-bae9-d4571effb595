{"type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/specificationExtension.json"}}, "minProperties": 1, "properties": {"$ref": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/ReferenceObject.json"}, "parameters": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/parameters.json"}, "description": {"type": "string", "description": "A description of the channel."}, "publish": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/operation.json"}, "subscribe": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/operation.json"}, "deprecated": {"type": "boolean", "default": false}, "bindings": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/bindingsObject.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc2/channelItem.json"}