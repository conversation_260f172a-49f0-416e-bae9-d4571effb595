import { visit } from 'unist-util-visit';
import { getUnicodeId, getTableOfContentsTitle } from '../../lib/remark-utils.js';
const DEEPLINKABLE_COMPONENTS = ['Heading', 'Update', 'Accordion'];
export const rehypeUnicodeIds = () => {
    return (tree) => {
        visit(tree, 'mdxJsxFlowElement', (node) => {
            if (node.name && DEEPLINKABLE_COMPONENTS.includes(node.name)) {
                const title = getTableOfContentsTitle(node);
                const encodedId = getUnicodeId(title);
                const existingIdIndex = node.attributes.findIndex((attr) => 'name' in attr && attr.name === 'id');
                if (existingIdIndex !== -1 &&
                    node.attributes[existingIdIndex] &&
                    !node.attributes[existingIdIndex].value) {
                    node.attributes[existingIdIndex].value = encodedId;
                }
            }
        });
    };
};
