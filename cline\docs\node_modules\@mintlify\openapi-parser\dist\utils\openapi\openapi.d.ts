import type { AnyApiDefinitionFormat, OpenApiOptions } from '../../types/index.js';
import type { LoadOptions } from '../load/index.js';
/**
 * Creates a fluent OpenAPI pipeline
 */
export declare function openapi(globalOptions?: OpenApiOptions): {
    load: (input: AnyApiDefinitionFormat, options?: LoadOptions) => {
        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
            details: () => Promise<import("../../index.js").DetailsResult>;
            files: () => Promise<import("../../index.js").Filesystem>;
            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>;
            toJson: () => Promise<string>;
            toYaml: () => Promise<string>;
        };
        details: () => Promise<import("../../index.js").DetailsResult>;
        files: () => Promise<import("../../index.js").Filesystem>;
        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                details: () => Promise<import("../../index.js").DetailsResult>;
                files: () => Promise<import("../../index.js").Filesystem>;
                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>;
                toJson: () => Promise<string>;
                toYaml: () => Promise<string>;
            };
            details: () => Promise<import("../../index.js").DetailsResult>;
            files: () => Promise<import("../../index.js").Filesystem>;
            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>;
            toJson: () => Promise<string>;
            toYaml: () => Promise<string>;
        };
        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").EmptyCommandChainResult>>;
        upgrade: () => {
            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                details: () => Promise<import("../../index.js").DetailsResult>;
                files: () => Promise<import("../../index.js").Filesystem>;
                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>;
                toJson: () => Promise<string>;
                toYaml: () => Promise<string>;
            };
            details: () => Promise<import("../../index.js").DetailsResult>;
            files: () => Promise<import("../../index.js").Filesystem>;
            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                    details: () => Promise<import("../../index.js").DetailsResult>;
                    files: () => Promise<import("../../index.js").Filesystem>;
                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>;
                    toJson: () => Promise<string>;
                    toYaml: () => Promise<string>;
                };
                details: () => Promise<import("../../index.js").DetailsResult>;
                files: () => Promise<import("../../index.js").Filesystem>;
                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>;
                toJson: () => Promise<string>;
                toYaml: () => Promise<string>;
            };
            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>;
            toJson: () => Promise<string>;
            toYaml: () => Promise<string>;
            validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                    details: () => Promise<import("../../index.js").DetailsResult>;
                    files: () => Promise<import("../../index.js").Filesystem>;
                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>;
                    toJson: () => Promise<string>;
                    toYaml: () => Promise<string>;
                };
                details: () => Promise<import("../../index.js").DetailsResult>;
                files: () => Promise<import("../../index.js").Filesystem>;
                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                        details: () => Promise<import("../../index.js").DetailsResult>;
                        files: () => Promise<import("../../index.js").Filesystem>;
                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>;
                        toJson: () => Promise<string>;
                        toYaml: () => Promise<string>;
                    };
                    details: () => Promise<import("../../index.js").DetailsResult>;
                    files: () => Promise<import("../../index.js").Filesystem>;
                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>;
                    toJson: () => Promise<string>;
                    toYaml: () => Promise<string>;
                };
                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>;
                toJson: () => Promise<string>;
                toYaml: () => Promise<string>;
                upgrade: () => {
                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                        details: () => Promise<import("../../index.js").DetailsResult>;
                        files: () => Promise<import("../../index.js").Filesystem>;
                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>;
                        toJson: () => Promise<string>;
                        toYaml: () => Promise<string>;
                    };
                    details: () => Promise<import("../../index.js").DetailsResult>;
                    files: () => Promise<import("../../index.js").Filesystem>;
                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                            details: () => Promise<import("../../index.js").DetailsResult>;
                            files: () => Promise<import("../../index.js").Filesystem>;
                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>;
                            toJson: () => Promise<string>;
                            toYaml: () => Promise<string>;
                        };
                        details: () => Promise<import("../../index.js").DetailsResult>;
                        files: () => Promise<import("../../index.js").Filesystem>;
                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>;
                        toJson: () => Promise<string>;
                        toYaml: () => Promise<string>;
                    };
                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>;
                    toJson: () => Promise<string>;
                    toYaml: () => Promise<string>;
                    validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                            details: () => Promise<import("../../index.js").DetailsResult>;
                            files: () => Promise<import("../../index.js").Filesystem>;
                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>;
                            toJson: () => Promise<string>;
                            toYaml: () => Promise<string>;
                        };
                        details: () => Promise<import("../../index.js").DetailsResult>;
                        files: () => Promise<import("../../index.js").Filesystem>;
                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                details: () => Promise<import("../../index.js").DetailsResult>;
                                files: () => Promise<import("../../index.js").Filesystem>;
                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>;
                                toJson: () => Promise<string>;
                                toYaml: () => Promise<string>;
                            };
                            details: () => Promise<import("../../index.js").DetailsResult>;
                            files: () => Promise<import("../../index.js").Filesystem>;
                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>;
                            toJson: () => Promise<string>;
                            toYaml: () => Promise<string>;
                        };
                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>;
                        toJson: () => Promise<string>;
                        toYaml: () => Promise<string>;
                        upgrade: () => {
                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                details: () => Promise<import("../../index.js").DetailsResult>;
                                files: () => Promise<import("../../index.js").Filesystem>;
                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>;
                                toJson: () => Promise<string>;
                                toYaml: () => Promise<string>;
                            };
                            details: () => Promise<import("../../index.js").DetailsResult>;
                            files: () => Promise<import("../../index.js").Filesystem>;
                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                    files: () => Promise<import("../../index.js").Filesystem>;
                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>;
                                    toJson: () => Promise<string>;
                                    toYaml: () => Promise<string>;
                                };
                                details: () => Promise<import("../../index.js").DetailsResult>;
                                files: () => Promise<import("../../index.js").Filesystem>;
                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>;
                                toJson: () => Promise<string>;
                                toYaml: () => Promise<string>;
                            };
                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>;
                            toJson: () => Promise<string>;
                            toYaml: () => Promise<string>;
                            validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                    files: () => Promise<import("../../index.js").Filesystem>;
                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>;
                                    toJson: () => Promise<string>;
                                    toYaml: () => Promise<string>;
                                };
                                details: () => Promise<import("../../index.js").DetailsResult>;
                                files: () => Promise<import("../../index.js").Filesystem>;
                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                        files: () => Promise<import("../../index.js").Filesystem>;
                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>;
                                        toJson: () => Promise<string>;
                                        toYaml: () => Promise<string>;
                                    };
                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                    files: () => Promise<import("../../index.js").Filesystem>;
                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>;
                                    toJson: () => Promise<string>;
                                    toYaml: () => Promise<string>;
                                };
                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>;
                                toJson: () => Promise<string>;
                                toYaml: () => Promise<string>;
                                upgrade: () => {
                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                        files: () => Promise<import("../../index.js").Filesystem>;
                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>;
                                        toJson: () => Promise<string>;
                                        toYaml: () => Promise<string>;
                                    };
                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                    files: () => Promise<import("../../index.js").Filesystem>;
                                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                            files: () => Promise<import("../../index.js").Filesystem>;
                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>;
                                            toJson: () => Promise<string>;
                                            toYaml: () => Promise<string>;
                                        };
                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                        files: () => Promise<import("../../index.js").Filesystem>;
                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>;
                                        toJson: () => Promise<string>;
                                        toYaml: () => Promise<string>;
                                    };
                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>;
                                    toJson: () => Promise<string>;
                                    toYaml: () => Promise<string>;
                                    validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                            files: () => Promise<import("../../index.js").Filesystem>;
                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>;
                                            toJson: () => Promise<string>;
                                            toYaml: () => Promise<string>;
                                        };
                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                        files: () => Promise<import("../../index.js").Filesystem>;
                                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>;
                                                toJson: () => Promise<string>;
                                                toYaml: () => Promise<string>;
                                            };
                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                            files: () => Promise<import("../../index.js").Filesystem>;
                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>;
                                            toJson: () => Promise<string>;
                                            toYaml: () => Promise<string>;
                                        };
                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>;
                                        toJson: () => Promise<string>;
                                        toYaml: () => Promise<string>;
                                        upgrade: () => {
                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>;
                                                toJson: () => Promise<string>;
                                                toYaml: () => Promise<string>;
                                            };
                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                            files: () => Promise<import("../../index.js").Filesystem>;
                                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>;
                                                    toJson: () => Promise<string>;
                                                    toYaml: () => Promise<string>;
                                                };
                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>;
                                                toJson: () => Promise<string>;
                                                toYaml: () => Promise<string>;
                                            };
                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>;
                                            toJson: () => Promise<string>;
                                            toYaml: () => Promise<string>;
                                            validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>;
                                                    toJson: () => Promise<string>;
                                                    toYaml: () => Promise<string>;
                                                };
                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>;
                                                        toJson: () => Promise<string>;
                                                        toYaml: () => Promise<string>;
                                                    };
                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>;
                                                    toJson: () => Promise<string>;
                                                    toYaml: () => Promise<string>;
                                                };
                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>;
                                                toJson: () => Promise<string>;
                                                toYaml: () => Promise<string>;
                                                upgrade: () => {
                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>;
                                                        toJson: () => Promise<string>;
                                                        toYaml: () => Promise<string>;
                                                    };
                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>;
                                                            toJson: () => Promise<string>;
                                                            toYaml: () => Promise<string>;
                                                        };
                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>;
                                                        toJson: () => Promise<string>;
                                                        toYaml: () => Promise<string>;
                                                    };
                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>;
                                                    toJson: () => Promise<string>;
                                                    toYaml: () => Promise<string>;
                                                    validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>;
                                                            toJson: () => Promise<string>;
                                                            toYaml: () => Promise<string>;
                                                        };
                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>;
                                                                toJson: () => Promise<string>;
                                                                toYaml: () => Promise<string>;
                                                            };
                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>;
                                                            toJson: () => Promise<string>;
                                                            toYaml: () => Promise<string>;
                                                        };
                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>;
                                                        toJson: () => Promise<string>;
                                                        toYaml: () => Promise<string>;
                                                        upgrade: () => {
                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>;
                                                                toJson: () => Promise<string>;
                                                                toYaml: () => Promise<string>;
                                                            };
                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>;
                                                                    toJson: () => Promise<string>;
                                                                    toYaml: () => Promise<string>;
                                                                };
                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>;
                                                                toJson: () => Promise<string>;
                                                                toYaml: () => Promise<string>;
                                                            };
                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>;
                                                            toJson: () => Promise<string>;
                                                            toYaml: () => Promise<string>;
                                                            validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>;
                                                                    toJson: () => Promise<string>;
                                                                    toYaml: () => Promise<string>;
                                                                };
                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>;
                                                                        toJson: () => Promise<string>;
                                                                        toYaml: () => Promise<string>;
                                                                    };
                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>;
                                                                    toJson: () => Promise<string>;
                                                                    toYaml: () => Promise<string>;
                                                                };
                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>;
                                                                toJson: () => Promise<string>;
                                                                toYaml: () => Promise<string>;
                                                                upgrade: () => {
                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>;
                                                                        toJson: () => Promise<string>;
                                                                        toYaml: () => Promise<string>;
                                                                    };
                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>;
                                                                            toJson: () => Promise<string>;
                                                                            toYaml: () => Promise<string>;
                                                                        };
                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>;
                                                                        toJson: () => Promise<string>;
                                                                        toYaml: () => Promise<string>;
                                                                    };
                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>;
                                                                    toJson: () => Promise<string>;
                                                                    toYaml: () => Promise<string>;
                                                                    validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>;
                                                                            toJson: () => Promise<string>;
                                                                            toYaml: () => Promise<string>;
                                                                        };
                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>;
                                                                                toJson: () => Promise<string>;
                                                                                toYaml: () => Promise<string>;
                                                                            };
                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>;
                                                                            toJson: () => Promise<string>;
                                                                            toYaml: () => Promise<string>;
                                                                        };
                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>;
                                                                        toJson: () => Promise<string>;
                                                                        toYaml: () => Promise<string>;
                                                                        upgrade: () => {
                                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>;
                                                                                toJson: () => Promise<string>;
                                                                                toYaml: () => Promise<string>;
                                                                            };
                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>;
                                                                                    toJson: () => Promise<string>;
                                                                                    toYaml: () => Promise<string>;
                                                                                };
                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>;
                                                                                toJson: () => Promise<string>;
                                                                                toYaml: () => Promise<string>;
                                                                            };
                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>;
                                                                            toJson: () => Promise<string>;
                                                                            toYaml: () => Promise<string>;
                                                                            validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>;
                                                                                    toJson: () => Promise<string>;
                                                                                    toYaml: () => Promise<string>;
                                                                                };
                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>;
                                                                                        toJson: () => Promise<string>;
                                                                                        toYaml: () => Promise<string>;
                                                                                    };
                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>;
                                                                                    toJson: () => Promise<string>;
                                                                                    toYaml: () => Promise<string>;
                                                                                };
                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>;
                                                                                toJson: () => Promise<string>;
                                                                                toYaml: () => Promise<string>;
                                                                                upgrade: () => {
                                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>;
                                                                                        toJson: () => Promise<string>;
                                                                                        toYaml: () => Promise<string>;
                                                                                    };
                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                            toJson: () => Promise<string>;
                                                                                            toYaml: () => Promise<string>;
                                                                                        };
                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>;
                                                                                        toJson: () => Promise<string>;
                                                                                        toYaml: () => Promise<string>;
                                                                                    };
                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>;
                                                                                    toJson: () => Promise<string>;
                                                                                    toYaml: () => Promise<string>;
                                                                                    validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                            toJson: () => Promise<string>;
                                                                                            toYaml: () => Promise<string>;
                                                                                        };
                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                toJson: () => Promise<string>;
                                                                                                toYaml: () => Promise<string>;
                                                                                            };
                                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                            toJson: () => Promise<string>;
                                                                                            toYaml: () => Promise<string>;
                                                                                        };
                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>;
                                                                                        toJson: () => Promise<string>;
                                                                                        toYaml: () => Promise<string>;
                                                                                        upgrade: () => {
                                                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                toJson: () => Promise<string>;
                                                                                                toYaml: () => Promise<string>;
                                                                                            };
                                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                    toJson: () => Promise<string>;
                                                                                                    toYaml: () => Promise<string>;
                                                                                                };
                                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                toJson: () => Promise<string>;
                                                                                                toYaml: () => Promise<string>;
                                                                                            };
                                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                            toJson: () => Promise<string>;
                                                                                            toYaml: () => Promise<string>;
                                                                                            validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                    toJson: () => Promise<string>;
                                                                                                    toYaml: () => Promise<string>;
                                                                                                };
                                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                        toJson: () => Promise<string>;
                                                                                                        toYaml: () => Promise<string>;
                                                                                                    };
                                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                    toJson: () => Promise<string>;
                                                                                                    toYaml: () => Promise<string>;
                                                                                                };
                                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                toJson: () => Promise<string>;
                                                                                                toYaml: () => Promise<string>;
                                                                                                upgrade: () => any;
                                                                                            };
                                                                                        };
                                                                                    };
                                                                                };
                                                                            };
                                                                        };
                                                                    };
                                                                };
                                                            };
                                                        };
                                                    };
                                                };
                                            };
                                        };
                                    };
                                };
                            };
                        };
                    };
                };
            };
        };
        toJson: () => Promise<string>;
        toYaml: () => Promise<string>;
        validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                details: () => Promise<import("../../index.js").DetailsResult>;
                files: () => Promise<import("../../index.js").Filesystem>;
                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>;
                toJson: () => Promise<string>;
                toYaml: () => Promise<string>;
            };
            details: () => Promise<import("../../index.js").DetailsResult>;
            files: () => Promise<import("../../index.js").Filesystem>;
            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                    details: () => Promise<import("../../index.js").DetailsResult>;
                    files: () => Promise<import("../../index.js").Filesystem>;
                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>;
                    toJson: () => Promise<string>;
                    toYaml: () => Promise<string>;
                };
                details: () => Promise<import("../../index.js").DetailsResult>;
                files: () => Promise<import("../../index.js").Filesystem>;
                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>;
                toJson: () => Promise<string>;
                toYaml: () => Promise<string>;
            };
            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>;
            toJson: () => Promise<string>;
            toYaml: () => Promise<string>;
            upgrade: () => {
                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                    details: () => Promise<import("../../index.js").DetailsResult>;
                    files: () => Promise<import("../../index.js").Filesystem>;
                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>;
                    toJson: () => Promise<string>;
                    toYaml: () => Promise<string>;
                };
                details: () => Promise<import("../../index.js").DetailsResult>;
                files: () => Promise<import("../../index.js").Filesystem>;
                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                        details: () => Promise<import("../../index.js").DetailsResult>;
                        files: () => Promise<import("../../index.js").Filesystem>;
                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>;
                        toJson: () => Promise<string>;
                        toYaml: () => Promise<string>;
                    };
                    details: () => Promise<import("../../index.js").DetailsResult>;
                    files: () => Promise<import("../../index.js").Filesystem>;
                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>;
                    toJson: () => Promise<string>;
                    toYaml: () => Promise<string>;
                };
                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>;
                toJson: () => Promise<string>;
                toYaml: () => Promise<string>;
                validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                        details: () => Promise<import("../../index.js").DetailsResult>;
                        files: () => Promise<import("../../index.js").Filesystem>;
                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>;
                        toJson: () => Promise<string>;
                        toYaml: () => Promise<string>;
                    };
                    details: () => Promise<import("../../index.js").DetailsResult>;
                    files: () => Promise<import("../../index.js").Filesystem>;
                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                            details: () => Promise<import("../../index.js").DetailsResult>;
                            files: () => Promise<import("../../index.js").Filesystem>;
                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>;
                            toJson: () => Promise<string>;
                            toYaml: () => Promise<string>;
                        };
                        details: () => Promise<import("../../index.js").DetailsResult>;
                        files: () => Promise<import("../../index.js").Filesystem>;
                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>;
                        toJson: () => Promise<string>;
                        toYaml: () => Promise<string>;
                    };
                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>;
                    toJson: () => Promise<string>;
                    toYaml: () => Promise<string>;
                    upgrade: () => {
                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                            details: () => Promise<import("../../index.js").DetailsResult>;
                            files: () => Promise<import("../../index.js").Filesystem>;
                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>;
                            toJson: () => Promise<string>;
                            toYaml: () => Promise<string>;
                        };
                        details: () => Promise<import("../../index.js").DetailsResult>;
                        files: () => Promise<import("../../index.js").Filesystem>;
                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                details: () => Promise<import("../../index.js").DetailsResult>;
                                files: () => Promise<import("../../index.js").Filesystem>;
                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>;
                                toJson: () => Promise<string>;
                                toYaml: () => Promise<string>;
                            };
                            details: () => Promise<import("../../index.js").DetailsResult>;
                            files: () => Promise<import("../../index.js").Filesystem>;
                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>;
                            toJson: () => Promise<string>;
                            toYaml: () => Promise<string>;
                        };
                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>;
                        toJson: () => Promise<string>;
                        toYaml: () => Promise<string>;
                        validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                details: () => Promise<import("../../index.js").DetailsResult>;
                                files: () => Promise<import("../../index.js").Filesystem>;
                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>;
                                toJson: () => Promise<string>;
                                toYaml: () => Promise<string>;
                            };
                            details: () => Promise<import("../../index.js").DetailsResult>;
                            files: () => Promise<import("../../index.js").Filesystem>;
                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                    files: () => Promise<import("../../index.js").Filesystem>;
                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>;
                                    toJson: () => Promise<string>;
                                    toYaml: () => Promise<string>;
                                };
                                details: () => Promise<import("../../index.js").DetailsResult>;
                                files: () => Promise<import("../../index.js").Filesystem>;
                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>;
                                toJson: () => Promise<string>;
                                toYaml: () => Promise<string>;
                            };
                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>;
                            toJson: () => Promise<string>;
                            toYaml: () => Promise<string>;
                            upgrade: () => {
                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                    files: () => Promise<import("../../index.js").Filesystem>;
                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>;
                                    toJson: () => Promise<string>;
                                    toYaml: () => Promise<string>;
                                };
                                details: () => Promise<import("../../index.js").DetailsResult>;
                                files: () => Promise<import("../../index.js").Filesystem>;
                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                        files: () => Promise<import("../../index.js").Filesystem>;
                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>;
                                        toJson: () => Promise<string>;
                                        toYaml: () => Promise<string>;
                                    };
                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                    files: () => Promise<import("../../index.js").Filesystem>;
                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>;
                                    toJson: () => Promise<string>;
                                    toYaml: () => Promise<string>;
                                };
                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>;
                                toJson: () => Promise<string>;
                                toYaml: () => Promise<string>;
                                validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                        files: () => Promise<import("../../index.js").Filesystem>;
                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>;
                                        toJson: () => Promise<string>;
                                        toYaml: () => Promise<string>;
                                    };
                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                    files: () => Promise<import("../../index.js").Filesystem>;
                                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                            files: () => Promise<import("../../index.js").Filesystem>;
                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>;
                                            toJson: () => Promise<string>;
                                            toYaml: () => Promise<string>;
                                        };
                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                        files: () => Promise<import("../../index.js").Filesystem>;
                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>;
                                        toJson: () => Promise<string>;
                                        toYaml: () => Promise<string>;
                                    };
                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>;
                                    toJson: () => Promise<string>;
                                    toYaml: () => Promise<string>;
                                    upgrade: () => {
                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                            files: () => Promise<import("../../index.js").Filesystem>;
                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>;
                                            toJson: () => Promise<string>;
                                            toYaml: () => Promise<string>;
                                        };
                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                        files: () => Promise<import("../../index.js").Filesystem>;
                                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>;
                                                toJson: () => Promise<string>;
                                                toYaml: () => Promise<string>;
                                            };
                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                            files: () => Promise<import("../../index.js").Filesystem>;
                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>;
                                            toJson: () => Promise<string>;
                                            toYaml: () => Promise<string>;
                                        };
                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>;
                                        toJson: () => Promise<string>;
                                        toYaml: () => Promise<string>;
                                        validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>;
                                                toJson: () => Promise<string>;
                                                toYaml: () => Promise<string>;
                                            };
                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                            files: () => Promise<import("../../index.js").Filesystem>;
                                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>;
                                                    toJson: () => Promise<string>;
                                                    toYaml: () => Promise<string>;
                                                };
                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>;
                                                toJson: () => Promise<string>;
                                                toYaml: () => Promise<string>;
                                            };
                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>;
                                            toJson: () => Promise<string>;
                                            toYaml: () => Promise<string>;
                                            upgrade: () => {
                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>;
                                                    toJson: () => Promise<string>;
                                                    toYaml: () => Promise<string>;
                                                };
                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>;
                                                        toJson: () => Promise<string>;
                                                        toYaml: () => Promise<string>;
                                                    };
                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>;
                                                    toJson: () => Promise<string>;
                                                    toYaml: () => Promise<string>;
                                                };
                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>;
                                                toJson: () => Promise<string>;
                                                toYaml: () => Promise<string>;
                                                validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>;
                                                        toJson: () => Promise<string>;
                                                        toYaml: () => Promise<string>;
                                                    };
                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>;
                                                            toJson: () => Promise<string>;
                                                            toYaml: () => Promise<string>;
                                                        };
                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>;
                                                        toJson: () => Promise<string>;
                                                        toYaml: () => Promise<string>;
                                                    };
                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>;
                                                    toJson: () => Promise<string>;
                                                    toYaml: () => Promise<string>;
                                                    upgrade: () => {
                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>;
                                                            toJson: () => Promise<string>;
                                                            toYaml: () => Promise<string>;
                                                        };
                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>;
                                                                toJson: () => Promise<string>;
                                                                toYaml: () => Promise<string>;
                                                            };
                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>;
                                                            toJson: () => Promise<string>;
                                                            toYaml: () => Promise<string>;
                                                        };
                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>;
                                                        toJson: () => Promise<string>;
                                                        toYaml: () => Promise<string>;
                                                        validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>;
                                                                toJson: () => Promise<string>;
                                                                toYaml: () => Promise<string>;
                                                            };
                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>;
                                                                    toJson: () => Promise<string>;
                                                                    toYaml: () => Promise<string>;
                                                                };
                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>;
                                                                toJson: () => Promise<string>;
                                                                toYaml: () => Promise<string>;
                                                            };
                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>;
                                                            toJson: () => Promise<string>;
                                                            toYaml: () => Promise<string>;
                                                            upgrade: () => {
                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>;
                                                                    toJson: () => Promise<string>;
                                                                    toYaml: () => Promise<string>;
                                                                };
                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>;
                                                                        toJson: () => Promise<string>;
                                                                        toYaml: () => Promise<string>;
                                                                    };
                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>;
                                                                    toJson: () => Promise<string>;
                                                                    toYaml: () => Promise<string>;
                                                                };
                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>;
                                                                toJson: () => Promise<string>;
                                                                toYaml: () => Promise<string>;
                                                                validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>;
                                                                        toJson: () => Promise<string>;
                                                                        toYaml: () => Promise<string>;
                                                                    };
                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>;
                                                                            toJson: () => Promise<string>;
                                                                            toYaml: () => Promise<string>;
                                                                        };
                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>;
                                                                        toJson: () => Promise<string>;
                                                                        toYaml: () => Promise<string>;
                                                                    };
                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>;
                                                                    toJson: () => Promise<string>;
                                                                    toYaml: () => Promise<string>;
                                                                    upgrade: () => {
                                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>;
                                                                            toJson: () => Promise<string>;
                                                                            toYaml: () => Promise<string>;
                                                                        };
                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>;
                                                                                toJson: () => Promise<string>;
                                                                                toYaml: () => Promise<string>;
                                                                            };
                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>;
                                                                            toJson: () => Promise<string>;
                                                                            toYaml: () => Promise<string>;
                                                                        };
                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>;
                                                                        toJson: () => Promise<string>;
                                                                        toYaml: () => Promise<string>;
                                                                        validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>;
                                                                                toJson: () => Promise<string>;
                                                                                toYaml: () => Promise<string>;
                                                                            };
                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>;
                                                                                    toJson: () => Promise<string>;
                                                                                    toYaml: () => Promise<string>;
                                                                                };
                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>;
                                                                                toJson: () => Promise<string>;
                                                                                toYaml: () => Promise<string>;
                                                                            };
                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>;
                                                                            toJson: () => Promise<string>;
                                                                            toYaml: () => Promise<string>;
                                                                            upgrade: () => {
                                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>;
                                                                                    toJson: () => Promise<string>;
                                                                                    toYaml: () => Promise<string>;
                                                                                };
                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>;
                                                                                        toJson: () => Promise<string>;
                                                                                        toYaml: () => Promise<string>;
                                                                                    };
                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>;
                                                                                    toJson: () => Promise<string>;
                                                                                    toYaml: () => Promise<string>;
                                                                                };
                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>;
                                                                                toJson: () => Promise<string>;
                                                                                toYaml: () => Promise<string>;
                                                                                validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>;
                                                                                        toJson: () => Promise<string>;
                                                                                        toYaml: () => Promise<string>;
                                                                                    };
                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                    filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                            toJson: () => Promise<string>;
                                                                                            toYaml: () => Promise<string>;
                                                                                        };
                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>;
                                                                                        toJson: () => Promise<string>;
                                                                                        toYaml: () => Promise<string>;
                                                                                    };
                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>;
                                                                                    toJson: () => Promise<string>;
                                                                                    toYaml: () => Promise<string>;
                                                                                    upgrade: () => {
                                                                                        dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                            toJson: () => Promise<string>;
                                                                                            toYaml: () => Promise<string>;
                                                                                        };
                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                        filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                toJson: () => Promise<string>;
                                                                                                toYaml: () => Promise<string>;
                                                                                            };
                                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                            toJson: () => Promise<string>;
                                                                                            toYaml: () => Promise<string>;
                                                                                        };
                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>;
                                                                                        toJson: () => Promise<string>;
                                                                                        toYaml: () => Promise<string>;
                                                                                        validate: (validateOptions?: import("../validate.js").ValidateOptions) => {
                                                                                            dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                toJson: () => Promise<string>;
                                                                                                toYaml: () => Promise<string>;
                                                                                            };
                                                                                            details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                            files: () => Promise<import("../../index.js").Filesystem>;
                                                                                            filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                    toJson: () => Promise<string>;
                                                                                                    toYaml: () => Promise<string>;
                                                                                                };
                                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                toJson: () => Promise<string>;
                                                                                                toYaml: () => Promise<string>;
                                                                                            };
                                                                                            get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                            toJson: () => Promise<string>;
                                                                                            toYaml: () => Promise<string>;
                                                                                            upgrade: () => {
                                                                                                dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                    toJson: () => Promise<string>;
                                                                                                    toYaml: () => Promise<string>;
                                                                                                };
                                                                                                details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                filter: (callback: (specification: import("../../index.js").AnyObject) => boolean) => {
                                                                                                    dereference: (dereferenceOptions?: import("../dereference.js").DereferenceOptions) => {
                                                                                                        details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                        files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                        get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").Merge<import("../../index.js").DereferenceResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                        toJson: () => Promise<string>;
                                                                                                        toYaml: () => Promise<string>;
                                                                                                    };
                                                                                                    details: () => Promise<import("../../index.js").DetailsResult>;
                                                                                                    files: () => Promise<import("../../index.js").Filesystem>;
                                                                                                    get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").FilterResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                    toJson: () => Promise<string>;
                                                                                                    toYaml: () => Promise<string>;
                                                                                                };
                                                                                                get: () => Promise<import("../../index.js").Merge<import("../../index.js").LoadResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").Merge<import("../../index.js").ValidateResult, import("../../index.js").Merge<import("../../index.js").UpgradeResult, import("../../index.js").EmptyCommandChainResult>>>>>>>>>>>>>>>>>>>>>>>>;
                                                                                                toJson: () => Promise<string>;
                                                                                                toYaml: () => Promise<string>;
                                                                                                validate: (validateOptions?: import("../validate.js").ValidateOptions) => any;
                                                                                            };
                                                                                        };
                                                                                    };
                                                                                };
                                                                            };
                                                                        };
                                                                    };
                                                                };
                                                            };
                                                        };
                                                    };
                                                };
                                            };
                                        };
                                    };
                                };
                            };
                        };
                    };
                };
            };
        };
    };
};
//# sourceMappingURL=openapi.d.ts.map