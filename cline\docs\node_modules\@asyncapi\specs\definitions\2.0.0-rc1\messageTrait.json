{"type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/specificationExtension.json"}}, "properties": {"schemaFormat": {"type": "string"}, "contentType": {"type": "string"}, "headers": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/schema.json"}]}}, "correlationId": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/correlationId.json"}]}, "tags": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/tag.json"}, "uniqueItems": true}, "summary": {"type": "string", "description": "A brief summary of the message."}, "name": {"type": "string", "description": "Name of the message."}, "title": {"type": "string", "description": "A human-friendly title for the message."}, "description": {"type": "string", "description": "A longer description of the message. CommonMark is allowed."}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/externalDocs.json"}, "deprecated": {"type": "boolean", "default": false}, "examples": {"type": "array", "items": {"type": "object"}}, "protocolInfo": {"type": "object", "additionalProperties": {"type": "object"}}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc1/messageTrait.json"}