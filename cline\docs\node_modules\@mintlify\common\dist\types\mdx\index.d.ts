import type { Directive, Statement, ModuleDeclaration } from 'estree-jsx';
import { Root } from 'mdast';
export type MdxNodeBodyChildType = Directive | Statement | ModuleDeclaration;
export type MdxNodeBodyType = Array<MdxNodeBodyChildType>;
export type FileType = {
    filename: string;
    tree: Root;
};
export type { MdxImportNode, FindAndRemoveImportsResponse, FileWithImports, } from './snippets/import.js';
export * from './snippets/import.js';
export * from './MdxCodeExampleData.js';
export * from './TableOfContentsSectionType.js';
export * from './MdxExtracts.js';
export * from './ChangelogFilter.js';
