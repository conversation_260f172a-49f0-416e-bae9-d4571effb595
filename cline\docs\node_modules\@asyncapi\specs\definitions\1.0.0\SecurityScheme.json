{"oneOf": [{"$ref": "http://asyncapi.com/definitions/1.0.0/userPassword.json"}, {"$ref": "http://asyncapi.com/definitions/1.0.0/apiKey.json"}, {"$ref": "http://asyncapi.com/definitions/1.0.0/X509.json"}, {"$ref": "http://asyncapi.com/definitions/1.0.0/symmetricEncryption.json"}, {"$ref": "http://asyncapi.com/definitions/1.0.0/asymmetricEncryption.json"}, {"$ref": "http://asyncapi.com/definitions/1.0.0/HTTPSecurityScheme.json"}], "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.0.0/SecurityScheme.json"}