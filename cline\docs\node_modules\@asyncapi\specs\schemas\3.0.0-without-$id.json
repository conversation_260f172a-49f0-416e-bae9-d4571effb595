{"$schema": "http://json-schema.org/draft-07/schema", "title": "AsyncAPI 3.0.0 schema.", "type": "object", "required": ["asyncapi", "info"], "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"asyncapi": {"type": "string", "const": "3.0.0", "description": "The AsyncAPI specification version of this document."}, "id": {"type": "string", "description": "A unique id representing the application.", "format": "uri"}, "info": {"$ref": "#/definitions/info"}, "servers": {"$ref": "#/definitions/servers"}, "defaultContentType": {"type": "string", "description": "Default content type to use when encoding/decoding a message's payload."}, "channels": {"$ref": "#/definitions/channels"}, "operations": {"$ref": "#/definitions/operations"}, "components": {"$ref": "#/definitions/components"}}, "definitions": {"specificationExtension": {"description": "Any property starting with x- is valid.", "additionalProperties": true, "additionalItems": true}, "info": {"description": "The object provides metadata about the API. The metadata can be used by the clients if needed.", "allOf": [{"type": "object", "required": ["version", "title"], "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"title": {"type": "string", "description": "A unique and precise title of the API."}, "version": {"type": "string", "description": "A semantic version number of the API."}, "description": {"type": "string", "description": "A longer description of the API. Should be different from the title. CommonMark is allowed."}, "termsOfService": {"type": "string", "description": "A URL to the Terms of Service for the API. MUST be in the format of a URL.", "format": "uri"}, "contact": {"$ref": "#/definitions/contact"}, "license": {"$ref": "#/definitions/license"}, "tags": {"type": "array", "description": "A list of tags for application API documentation control. Tags can be used for logical grouping of applications.", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/tag"}]}, "uniqueItems": true}, "externalDocs": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}}}, {"$ref": "#/definitions/infoExtensions"}], "examples": [{"title": "AsyncAPI Sample App", "version": "1.0.1", "description": "This is a sample app.", "termsOfService": "https://asyncapi.org/terms/", "contact": {"name": "API Support", "url": "https://www.asyncapi.org/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.html"}, "externalDocs": {"description": "Find more info here", "url": "https://www.asyncapi.org"}, "tags": [{"name": "e-commerce"}]}]}, "contact": {"type": "object", "description": "Contact information for the exposed API.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The identifying name of the contact person/organization."}, "url": {"type": "string", "description": "The URL pointing to the contact information.", "format": "uri"}, "email": {"type": "string", "description": "The email address of the contact person/organization.", "format": "email"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "examples": [{"name": "API Support", "url": "https://www.example.com/support", "email": "<EMAIL>"}]}, "license": {"type": "object", "required": ["name"], "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the license type. It's encouraged to use an OSI compatible license."}, "url": {"type": "string", "description": "The URL pointing to the license.", "format": "uri"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "examples": [{"name": "Apache 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.html"}]}, "Reference": {"type": "object", "description": "A simple object to allow referencing other components in the specification, internally and externally.", "required": ["$ref"], "properties": {"$ref": {"description": "The reference string.", "$ref": "#/definitions/ReferenceObject"}}, "examples": [{"$ref": "#/components/schemas/Pet"}]}, "ReferenceObject": {"type": "string", "format": "uri-reference"}, "tag": {"type": "object", "description": "Allows adding metadata to a single tag.", "additionalProperties": false, "required": ["name"], "properties": {"name": {"type": "string", "description": "The name of the tag."}, "description": {"type": "string", "description": "A short description for the tag. CommonMark syntax can be used for rich text representation."}, "externalDocs": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "examples": [{"name": "user", "description": "User-related messages"}]}, "externalDocs": {"type": "object", "additionalProperties": false, "description": "Allows referencing an external resource for extended documentation.", "required": ["url"], "properties": {"description": {"type": "string", "description": "A short description of the target documentation. CommonMark syntax can be used for rich text representation."}, "url": {"type": "string", "description": "The URL for the target documentation. This MUST be in the form of an absolute URL.", "format": "uri"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "examples": [{"description": "Find more info here", "url": "https://example.com"}]}, "infoExtensions": {"type": "object", "description": "The object that lists all the extensions of Info", "properties": {"x-x": {"$ref": "#/definitions/extensions-x-0.1.0-schema"}, "x-linkedin": {"$ref": "#/definitions/extensions-linkedin-0.1.0-schema"}}}, "extensions-x-0.1.0-schema": {"type": "string", "description": "This extension allows you to provide the Twitter username of the account representing the team/company of the API.", "example": ["sambhavgupta75", "AsyncAPISpec"]}, "extensions-linkedin-0.1.0-schema": {"type": "string", "pattern": "^http(s)?://(www\\.)?linkedin\\.com.*$", "description": "This extension allows you to provide the Linkedin profile URL of the account representing the team/company of the API.", "example": ["https://www.linkedin.com/company/asyncapi/", "https://www.linkedin.com/in/sambhavgupta0705/"]}, "servers": {"description": "An object representing multiple servers.", "type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/server"}]}, "examples": [{"development": {"host": "localhost:5672", "description": "Development AMQP broker.", "protocol": "amqp", "protocolVersion": "0-9-1", "tags": [{"name": "env:development", "description": "This environment is meant for developers to run their own tests."}]}, "staging": {"host": "rabbitmq-staging.in.mycompany.com:5672", "description": "RabbitMQ broker for the staging environment.", "protocol": "amqp", "protocolVersion": "0-9-1", "tags": [{"name": "env:staging", "description": "This environment is a replica of the production environment."}]}, "production": {"host": "rabbitmq.in.mycompany.com:5672", "description": "RabbitMQ broker for the production environment.", "protocol": "amqp", "protocolVersion": "0-9-1", "tags": [{"name": "env:production", "description": "This environment is the live environment available for final users."}]}}]}, "server": {"type": "object", "description": "An object representing a message broker, a server or any other kind of computer program capable of sending and/or receiving data.", "required": ["host", "protocol"], "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"host": {"type": "string", "description": "The server host name. It MAY include the port. This field supports Server Variables. Variable substitutions will be made when a variable is named in {braces}."}, "pathname": {"type": "string", "description": "The path to a resource in the host. This field supports Server Variables. Variable substitutions will be made when a variable is named in {braces}."}, "title": {"type": "string", "description": "A human-friendly title for the server."}, "summary": {"type": "string", "description": "A brief summary of the server."}, "description": {"type": "string", "description": "A longer description of the server. CommonMark is allowed."}, "protocol": {"type": "string", "description": "The protocol this server supports for connection."}, "protocolVersion": {"type": "string", "description": "An optional string describing the server. CommonMark syntax MAY be used for rich text representation."}, "variables": {"$ref": "#/definitions/serverVariables"}, "security": {"$ref": "#/definitions/securityRequirements"}, "tags": {"type": "array", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/tag"}]}, "uniqueItems": true}, "externalDocs": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}, "bindings": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/serverBindingsObject"}]}}, "examples": [{"host": "kafka.in.mycompany.com:9092", "description": "Production Kafka broker.", "protocol": "kafka", "protocolVersion": "3.2"}, {"host": "rabbitmq.in.mycompany.com:5672", "pathname": "/production", "protocol": "amqp", "description": "Production RabbitMQ broker (uses the `production` vhost)."}]}, "serverVariables": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/serverVariable"}]}}, "serverVariable": {"type": "object", "description": "An object representing a Server Variable for server URL template substitution.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"enum": {"type": "array", "description": "An enumeration of string values to be used if the substitution options are from a limited set.", "items": {"type": "string"}, "uniqueItems": true}, "default": {"type": "string", "description": "The default value to use for substitution, and to send, if an alternate value is not supplied."}, "description": {"type": "string", "description": "An optional description for the server variable. CommonMark syntax MAY be used for rich text representation."}, "examples": {"type": "array", "description": "An array of examples of the server variable.", "items": {"type": "string"}}}, "examples": [{"host": "rabbitmq.in.mycompany.com:5672", "pathname": "/{env}", "protocol": "amqp", "description": "RabbitMQ broker. Use the `env` variable to point to either `production` or `staging`.", "variables": {"env": {"description": "Environment to connect to. It can be either `production` or `staging`.", "enum": ["production", "staging"]}}}]}, "securityRequirements": {"description": "An array representing security requirements.", "type": "array", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/SecurityScheme"}]}}, "SecurityScheme": {"description": "Defines a security scheme that can be used by the operations.", "oneOf": [{"$ref": "#/definitions/userPassword"}, {"$ref": "#/definitions/apiKey"}, {"$ref": "#/definitions/X509"}, {"$ref": "#/definitions/symmetricEncryption"}, {"$ref": "#/definitions/asymmetricEncryption"}, {"$ref": "#/definitions/HTTPSecurityScheme"}, {"$ref": "#/definitions/oauth2Flows"}, {"$ref": "#/definitions/openIdConnect"}, {"$ref": "#/definitions/SaslSecurityScheme"}], "examples": [{"type": "userPassword"}]}, "userPassword": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["userPassword"]}, "description": {"type": "string"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"type": "userPassword"}]}, "apiKey": {"type": "object", "required": ["type", "in"], "properties": {"type": {"type": "string", "description": "The type of the security scheme", "enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "in": {"type": "string", "description": " The location of the API key.", "enum": ["user", "password"]}, "description": {"type": "string", "description": "A short description for security scheme. CommonMark syntax MAY be used for rich text representation."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "user"}]}, "X509": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["X509"]}, "description": {"type": "string"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"type": "X509"}]}, "symmetricEncryption": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["symmetricEncryption"]}, "description": {"type": "string"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"type": "symmetricEncryption"}]}, "asymmetricEncryption": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "description": "The type of the security scheme.", "enum": ["asymmetricEncryption"]}, "description": {"type": "string", "description": "A short description for security scheme."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false}, "HTTPSecurityScheme": {"oneOf": [{"$ref": "#/definitions/NonBearerHTTPSecurityScheme"}, {"$ref": "#/definitions/BearerHTTPSecurityScheme"}, {"$ref": "#/definitions/APIKeyHTTPSecurityScheme"}]}, "NonBearerHTTPSecurityScheme": {"not": {"type": "object", "properties": {"scheme": {"type": "string", "description": "A short description for security scheme.", "enum": ["bearer"]}}}, "type": "object", "required": ["scheme", "type"], "properties": {"scheme": {"type": "string", "description": "The name of the HTTP Authorization scheme to be used in the Authorization header as defined in RFC7235."}, "description": {"type": "string", "description": "A short description for security scheme."}, "type": {"type": "string", "description": "The type of the security scheme.", "enum": ["http"]}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false}, "BearerHTTPSecurityScheme": {"type": "object", "required": ["type", "scheme"], "properties": {"scheme": {"type": "string", "description": "The name of the HTTP Authorization scheme to be used in the Authorization header as defined in RFC7235.", "enum": ["bearer"]}, "bearerFormat": {"type": "string", "description": "A hint to the client to identify how the bearer token is formatted. Bearer tokens are usually generated by an authorization server, so this information is primarily for documentation purposes."}, "type": {"type": "string", "description": "The type of the security scheme.", "enum": ["http"]}, "description": {"type": "string", "description": "A short description for security scheme. CommonMark syntax MAY be used for rich text representation."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false}, "APIKeyHTTPSecurityScheme": {"type": "object", "required": ["type", "name", "in"], "properties": {"type": {"type": "string", "description": "The type of the security scheme.", "enum": ["httpApiKey"]}, "name": {"type": "string", "description": "The name of the header, query or cookie parameter to be used."}, "in": {"type": "string", "description": "The location of the API key", "enum": ["header", "query", "cookie"]}, "description": {"type": "string", "description": "A short description for security scheme. CommonMark syntax MAY be used for rich text representation."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"type": "httpApiKey", "name": "api_key", "in": "header"}]}, "oauth2Flows": {"type": "object", "description": "Allows configuration of the supported OAuth Flows.", "required": ["type", "flows"], "properties": {"type": {"type": "string", "description": "The type of the security scheme.", "enum": ["oauth2"]}, "description": {"type": "string", "description": "A short description for security scheme."}, "flows": {"type": "object", "properties": {"implicit": {"description": "Configuration for the OAuth Implicit flow.", "allOf": [{"$ref": "#/definitions/oauth2Flow"}, {"required": ["authorizationUrl", "availableScopes"]}, {"not": {"required": ["tokenUrl"]}}]}, "password": {"description": "Configuration for the OAuth Resource Owner Protected Credentials flow.", "allOf": [{"$ref": "#/definitions/oauth2Flow"}, {"required": ["tokenUrl", "availableScopes"]}, {"not": {"required": ["authorizationUrl"]}}]}, "clientCredentials": {"description": "Configuration for the OAuth Client Credentials flow.", "allOf": [{"$ref": "#/definitions/oauth2Flow"}, {"required": ["tokenUrl", "availableScopes"]}, {"not": {"required": ["authorizationUrl"]}}]}, "authorizationCode": {"description": "Configuration for the OAuth Authorization Code flow.", "allOf": [{"$ref": "#/definitions/oauth2Flow"}, {"required": ["authorizationUrl", "tokenUrl", "availableScopes"]}]}}, "additionalProperties": false}, "scopes": {"type": "array", "description": "List of the needed scope names.", "items": {"type": "string"}}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}}, "oauth2Flow": {"type": "object", "description": "Configuration details for a supported OAuth Flow", "properties": {"authorizationUrl": {"type": "string", "format": "uri", "description": "The authorization URL to be used for this flow. This MUST be in the form of an absolute URL."}, "tokenUrl": {"type": "string", "format": "uri", "description": "The token URL to be used for this flow. This MUST be in the form of an absolute URL."}, "refreshUrl": {"type": "string", "format": "uri", "description": "The URL to be used for obtaining refresh tokens. This MUST be in the form of an absolute URL."}, "availableScopes": {"$ref": "#/definitions/oauth2Scopes", "description": "The available scopes for the OAuth2 security scheme. A map between the scope name and a short description for it."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"authorizationUrl": "https://example.com/api/oauth/dialog", "tokenUrl": "https://example.com/api/oauth/token", "availableScopes": {"write:pets": "modify pets in your account", "read:pets": "read your pets"}}]}, "oauth2Scopes": {"type": "object", "additionalProperties": {"type": "string"}}, "openIdConnect": {"type": "object", "required": ["type", "openIdConnectUrl"], "properties": {"type": {"type": "string", "description": "The type of the security scheme.", "enum": ["openIdConnect"]}, "description": {"type": "string", "description": "A short description for security scheme. CommonMark syntax MAY be used for rich text representation."}, "openIdConnectUrl": {"type": "string", "format": "uri", "description": "OpenId Connect URL to discover OAuth2 configuration values. This MUST be in the form of an absolute URL."}, "scopes": {"type": "array", "description": "List of the needed scope names. An empty array means no scopes are needed.", "items": {"type": "string"}}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false}, "SaslSecurityScheme": {"oneOf": [{"$ref": "#/definitions/SaslPlainSecurityScheme"}, {"$ref": "#/definitions/SaslScramSecurityScheme"}, {"$ref": "#/definitions/SaslGssapiSecurityScheme"}]}, "SaslPlainSecurityScheme": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "description": "The type of the security scheme. Valid values", "enum": ["plain"]}, "description": {"type": "string", "description": "A short description for security scheme."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"type": "scramSha512"}]}, "SaslScramSecurityScheme": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "description": "The type of the security scheme.", "enum": ["scramSha256", "scramSha512"]}, "description": {"type": "string", "description": "A short description for security scheme."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"type": "scramSha512"}]}, "SaslGssapiSecurityScheme": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "description": "The type of the security scheme.", "enum": ["gssapi"]}, "description": {"type": "string", "description": "A short description for security scheme."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": false, "examples": [{"type": "scramSha512"}]}, "serverBindingsObject": {"type": "object", "description": "Map describing protocol-specific definitions for a server.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"http": {}, "ws": {}, "amqp": {}, "amqp1": {}, "mqtt": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-mqtt-0.2.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-mqtt-0.2.0-server"}}]}, "kafka": {"properties": {"bindingVersion": {"enum": ["0.5.0", "0.4.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-kafka-0.5.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.5.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.5.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.4.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.3.0-server"}}]}, "anypointmq": {}, "nats": {}, "jms": {"properties": {"bindingVersion": {"enum": ["0.0.1"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-jms-0.0.1-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.0.1"}}}, "then": {"$ref": "#/definitions/bindings-jms-0.0.1-server"}}]}, "sns": {}, "sqs": {}, "stomp": {}, "redis": {}, "ibmmq": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-ibmmq-0.1.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-ibmmq-0.1.0-server"}}]}, "solace": {"properties": {"bindingVersion": {"enum": ["0.4.0", "0.3.0", "0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-solace-0.4.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "#/definitions/bindings-solace-0.4.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-solace-0.3.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-solace-0.2.0-server"}}]}, "googlepubsub": {}, "pulsar": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-pulsar-0.1.0-server"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-pulsar-0.1.0-server"}}]}}}, "bindings-mqtt-0.2.0-server": {"title": "Server <PERSON><PERSON>a", "description": "This object contains information about the server representation in MQTT.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"clientId": {"type": "string", "description": "The client identifier."}, "cleanSession": {"type": "boolean", "description": "Whether to create a persistent connection or not. When 'false', the connection will be persistent. This is called clean start in MQTTv5."}, "lastWill": {"type": "object", "description": "Last Will and Testament configuration.", "properties": {"topic": {"type": "string", "description": "The topic where the Last Will and Testament message will be sent."}, "qos": {"type": "integer", "enum": [0, 1, 2], "description": "Defines how hard the broker/client will try to ensure that the Last Will and Testament message is received. Its value MUST be either 0, 1 or 2."}, "message": {"type": "string", "description": "Last Will message."}, "retain": {"type": "boolean", "description": "Whether the broker should retain the Last Will and Testament message or not."}}}, "keepAlive": {"type": "integer", "description": "Interval in seconds of the longest period of time the broker and the client can endure without sending a message."}, "sessionExpiryInterval": {"oneOf": [{"type": "integer", "minimum": 0}, {"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/Reference"}], "description": "Interval time in seconds or a Schema Object containing the definition of the interval.  The broker maintains a session for a disconnected client until this interval expires."}, "maximumPacketSize": {"oneOf": [{"type": "integer", "minimum": 1, "maximum": 4294967295}, {"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/Reference"}], "description": "Number of bytes or a Schema Object representing the Maximum Packet Size the Client is willing to accept."}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"clientId": "guest", "cleanSession": true, "lastWill": {"topic": "/last-wills", "qos": 2, "message": "Guest gone offline.", "retain": false}, "keepAlive": 60, "sessionExpiryInterval": 120, "maximumPacketSize": 1024, "bindingVersion": "0.2.0"}]}, "schema": {"description": "The Schema Object allows the definition of input and output data types. These types can be objects, but also primitives and arrays. This object is a superset of the JSON Schema Specification Draft 07. The empty schema (which allows any instance to validate) MAY be represented by the boolean value true and a schema which allows no instance to validate MAY be represented by the boolean value false.", "allOf": [{"$ref": "#/definitions/json-schema-draft-07-schema"}, {"patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"additionalProperties": {"anyOf": [{"$ref": "#/definitions/schema"}, {"type": "boolean"}], "default": {}}, "items": {"anyOf": [{"$ref": "#/definitions/schema"}, {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/schema"}}], "default": {}}, "allOf": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/schema"}}, "oneOf": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/schema"}}, "anyOf": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/schema"}}, "not": {"$ref": "#/definitions/schema"}, "properties": {"type": "object", "additionalProperties": {"$ref": "#/definitions/schema"}, "default": {}}, "patternProperties": {"type": "object", "additionalProperties": {"$ref": "#/definitions/schema"}, "default": {}}, "propertyNames": {"$ref": "#/definitions/schema"}, "contains": {"$ref": "#/definitions/schema"}, "discriminator": {"type": "string", "description": "Adds support for polymorphism. The discriminator is the schema property name that is used to differentiate between other schema that inherit this schema. The property name used MUST be defined at this schema and it MUST be in the required property list. When used, the value MUST be the name of this schema or any schema that inherits it. See Composition and Inheritance for more details."}, "externalDocs": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}, "deprecated": {"type": "boolean", "description": "Specifies that a schema is deprecated and SHOULD be transitioned out of usage. Default value is false.", "default": false}}}]}, "json-schema-draft-07-schema": {"title": "Core schema meta-schema", "definitions": {"schemaArray": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/json-schema-draft-07-schema"}}, "nonNegativeInteger": {"type": "integer", "minimum": 0}, "nonNegativeIntegerDefault0": {"allOf": [{"$ref": "#/definitions/json-schema-draft-07-schema/definitions/nonNegativeInteger"}, {"default": 0}]}, "simpleTypes": {"enum": ["array", "boolean", "integer", "null", "number", "object", "string"]}, "stringArray": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "default": []}}, "type": ["object", "boolean"], "properties": {"$id": {"type": "string", "format": "uri-reference"}, "$schema": {"type": "string", "format": "uri"}, "$ref": {"type": "string", "format": "uri-reference"}, "$comment": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "default": true, "readOnly": {"type": "boolean", "default": false}, "writeOnly": {"type": "boolean", "default": false}, "examples": {"type": "array", "items": true}, "multipleOf": {"type": "number", "exclusiveMinimum": 0}, "maximum": {"type": "number"}, "exclusiveMaximum": {"type": "number"}, "minimum": {"type": "number"}, "exclusiveMinimum": {"type": "number"}, "maxLength": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/nonNegativeInteger"}, "minLength": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/nonNegativeIntegerDefault0"}, "pattern": {"type": "string", "format": "regex"}, "additionalItems": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "items": {"anyOf": [{"$ref": "#/definitions/json-schema-draft-07-schema"}, {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/schemaArray"}], "default": true}, "maxItems": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/nonNegativeInteger"}, "minItems": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/nonNegativeIntegerDefault0"}, "uniqueItems": {"type": "boolean", "default": false}, "contains": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "maxProperties": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/nonNegativeInteger"}, "minProperties": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/nonNegativeIntegerDefault0"}, "required": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/stringArray"}, "additionalProperties": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "definitions": {"type": "object", "additionalProperties": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "default": {}}, "properties": {"type": "object", "additionalProperties": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "default": {}}, "patternProperties": {"type": "object", "additionalProperties": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "propertyNames": {"format": "regex"}, "default": {}}, "dependencies": {"type": "object", "additionalProperties": {"anyOf": [{"$ref": "#/definitions/json-schema-draft-07-schema"}, {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/stringArray"}]}}, "propertyNames": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "const": true, "enum": {"type": "array", "items": true, "minItems": 1, "uniqueItems": true}, "type": {"anyOf": [{"$ref": "#/definitions/json-schema-draft-07-schema/definitions/simpleTypes"}, {"type": "array", "items": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/simpleTypes"}, "minItems": 1, "uniqueItems": true}]}, "format": {"type": "string"}, "contentMediaType": {"type": "string"}, "contentEncoding": {"type": "string"}, "if": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "then": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "else": {"$ref": "#/definitions/json-schema-draft-07-schema"}, "allOf": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/schemaArray"}, "anyOf": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/schemaArray"}, "oneOf": {"$ref": "#/definitions/json-schema-draft-07-schema/definitions/schemaArray"}, "not": {"$ref": "#/definitions/json-schema-draft-07-schema"}}, "default": true}, "bindings-kafka-0.5.0-server": {"title": "Server <PERSON><PERSON>a", "description": "This object contains server connection information to a Kafka broker. This object contains additional information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"schemaRegistryUrl": {"type": "string", "description": "API URL for the Schema Registry used when producing Kafka messages (if a Schema Registry was used)."}, "schemaRegistryVendor": {"type": "string", "description": "The vendor of the Schema Registry and Kafka serdes library that should be used."}, "bindingVersion": {"type": "string", "enum": ["0.5.0"], "description": "The version of this binding."}}, "examples": [{"schemaRegistryUrl": "https://my-schema-registry.com", "schemaRegistryVendor": "confluent", "bindingVersion": "0.5.0"}]}, "bindings-kafka-0.4.0-server": {"title": "Server <PERSON><PERSON>a", "description": "This object contains server connection information to a Kafka broker. This object contains additional information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"schemaRegistryUrl": {"type": "string", "description": "API URL for the Schema Registry used when producing Kafka messages (if a Schema Registry was used)."}, "schemaRegistryVendor": {"type": "string", "description": "The vendor of the Schema Registry and Kafka serdes library that should be used."}, "bindingVersion": {"type": "string", "enum": ["0.4.0"], "description": "The version of this binding."}}, "examples": [{"schemaRegistryUrl": "https://my-schema-registry.com", "schemaRegistryVendor": "confluent", "bindingVersion": "0.4.0"}]}, "bindings-kafka-0.3.0-server": {"title": "Server <PERSON><PERSON>a", "description": "This object contains server connection information to a Kafka broker. This object contains additional information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"schemaRegistryUrl": {"type": "string", "description": "API URL for the Schema Registry used when producing Kafka messages (if a Schema Registry was used)."}, "schemaRegistryVendor": {"type": "string", "description": "The vendor of the Schema Registry and Kafka serdes library that should be used."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding."}}, "examples": [{"schemaRegistryUrl": "https://my-schema-registry.com", "schemaRegistryVendor": "confluent", "bindingVersion": "0.3.0"}]}, "bindings-jms-0.0.1-server": {"title": "Server <PERSON><PERSON>a", "description": "This object contains configuration for describing a JMS broker as an AsyncAPI server. This objects only contains configuration that can not be provided in the AsyncAPI standard server object.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "required": ["jmsConnectionFactory"], "properties": {"jmsConnectionFactory": {"type": "string", "description": "The classname of the ConnectionFactory implementation for the JMS Provider."}, "properties": {"type": "array", "items": {"$ref": "#/definitions/bindings-jms-0.0.1-server/definitions/property"}, "description": "Additional properties to set on the JMS ConnectionFactory implementation for the JMS Provider."}, "clientID": {"type": "string", "description": "A client identifier for applications that use this JMS connection factory. If the Client ID Policy is set to 'Restricted' (the default), then configuring a Client ID on the ConnectionFactory prevents more than one JMS client from using a connection from this factory."}, "bindingVersion": {"type": "string", "enum": ["0.0.1"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "definitions": {"property": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string", "description": "The name of a property"}, "value": {"type": ["string", "boolean", "number", "null"], "description": "The name of a property"}}}}, "examples": [{"jmsConnectionFactory": "org.apache.activemq.ActiveMQConnectionFactory", "properties": [{"name": "disableTimeStampsByDefault", "value": false}], "clientID": "my-application-1", "bindingVersion": "0.0.1"}]}, "bindings-ibmmq-0.1.0-server": {"title": "IBM MQ server bindings object", "description": "This object contains server connection information about the IBM MQ server, referred to as an IBM MQ queue manager. This object contains additional connectivity information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"groupId": {"type": "string", "description": "Defines a logical group of IBM MQ server objects. This is necessary to specify multi-endpoint configurations used in high availability deployments. If omitted, the server object is not part of a group."}, "ccdtQueueManagerName": {"type": "string", "default": "*", "description": "The name of the IBM MQ queue manager to bind to in the CCDT file."}, "cipherSpec": {"type": "string", "description": "The recommended cipher specification used to establish a TLS connection between the client and the IBM MQ queue manager. More information on SSL/TLS cipher specifications supported by IBM MQ can be found on this page in the IBM MQ Knowledge Center."}, "multiEndpointServer": {"type": "boolean", "default": false, "description": "If 'multiEndpointServer' is 'true' then multiple connections can be workload balanced and applications should not make assumptions as to where messages are processed. Where message ordering, or affinity to specific message resources is necessary, a single endpoint ('multiEndpointServer' = 'false') may be required."}, "heartBeatInterval": {"type": "integer", "minimum": 0, "maximum": 999999, "default": 300, "description": "The recommended value (in seconds) for the heartbeat sent to the queue manager during periods of inactivity. A value of zero means that no heart beats are sent. A value of 1 means that the client will use the value defined by the queue manager. More information on heart beat interval can be found on this page in the IBM MQ Knowledge Center."}, "bindingVersion": {"type": "string", "enum": ["0.1.0"], "description": "The version of this binding."}}, "examples": [{"groupId": "PRODCLSTR1", "cipherSpec": "ANY_TLS12_OR_HIGHER", "bindingVersion": "0.1.0"}, {"groupId": "PRODCLSTR1", "bindingVersion": "0.1.0"}]}, "bindings-solace-0.4.0-server": {"title": "Solace server bindings object", "description": "This object contains server connection information about the Solace broker. This object contains additional connectivity information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"msgVpn": {"type": "string", "description": "The name of the Virtual Private Network to connect to on the Solace broker."}, "clientName": {"type": "string", "minLength": 1, "maxLength": 160, "description": "A unique client name to use to register to the appliance. If specified, it must be a valid Topic name, and a maximum of 160 bytes in length when encoded as UTF-8."}, "bindingVersion": {"type": "string", "enum": ["0.4.0"], "description": "The version of this binding."}}, "examples": [{"msgVpn": "ProdVPN", "bindingVersion": "0.4.0"}]}, "bindings-solace-0.3.0-server": {"title": "Solace server bindings object", "description": "This object contains server connection information about the Solace broker. This object contains additional connectivity information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"msgVpn": {"type": "string", "description": "The name of the Virtual Private Network to connect to on the Solace broker."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding."}}, "examples": [{"msgVpn": "ProdVPN", "bindingVersion": "0.3.0"}]}, "bindings-solace-0.2.0-server": {"title": "Solace server bindings object", "description": "This object contains server connection information about the Solace broker. This object contains additional connectivity information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"msvVpn": {"type": "string", "description": "The name of the Virtual Private Network to connect to on the Solace broker."}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding."}}, "examples": [{"msgVpn": "ProdVPN", "bindingVersion": "0.2.0"}]}, "bindings-pulsar-0.1.0-server": {"title": "Server <PERSON><PERSON>a", "description": "This object contains server information of Pulsar broker, which covers cluster and tenant admin configuration. This object contains additional information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"tenant": {"type": "string", "description": "The pulsar tenant. If omitted, 'public' MUST be assumed."}, "bindingVersion": {"type": "string", "enum": ["0.1.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"tenant": "contoso", "bindingVersion": "0.1.0"}]}, "channels": {"type": "object", "description": "An object containing all the Channel Object definitions the Application MUST use during runtime.", "additionalProperties": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/channel"}]}, "examples": [{"userSignedUp": {"address": "user.signedup", "messages": {"userSignedUp": {"$ref": "#/components/messages/userSignedUp"}}}}]}, "channel": {"type": "object", "description": "Describes a shared communication channel.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"address": {"type": ["string", "null"], "description": "An optional string representation of this channel's address. The address is typically the \"topic name\", \"routing key\", \"event type\", or \"path\". When `null` or absent, it MUST be interpreted as unknown. This is useful when the address is generated dynamically at runtime or can't be known upfront. It MAY contain Channel Address Expressions."}, "messages": {"$ref": "#/definitions/channelMessages"}, "parameters": {"$ref": "#/definitions/parameters"}, "title": {"type": "string", "description": "A human-friendly title for the channel."}, "summary": {"type": "string", "description": "A brief summary of the channel."}, "description": {"type": "string", "description": "A longer description of the channel. CommonMark is allowed."}, "servers": {"type": "array", "description": "The references of the servers on which this channel is available. If absent or empty then this channel must be available on all servers.", "items": {"$ref": "#/definitions/Reference"}, "uniqueItems": true}, "tags": {"type": "array", "description": "A list of tags for logical grouping of channels.", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/tag"}]}, "uniqueItems": true}, "externalDocs": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}, "bindings": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/channelBindingsObject"}]}}, "examples": [{"address": "users.{userId}", "title": "Users channel", "description": "This channel is used to exchange messages about user events.", "messages": {"userSignedUp": {"$ref": "#/components/messages/userSignedUp"}, "userCompletedOrder": {"$ref": "#/components/messages/userCompletedOrder"}}, "parameters": {"userId": {"$ref": "#/components/parameters/userId"}}, "servers": [{"$ref": "#/servers/rabbitmqInProd"}, {"$ref": "#/servers/rabbitmqInStaging"}], "bindings": {"amqp": {"is": "queue", "queue": {"exclusive": true}}}, "tags": [{"name": "user", "description": "User-related messages"}], "externalDocs": {"description": "Find more info here", "url": "https://example.com"}}]}, "channelMessages": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/messageObject"}]}, "description": "A map of the messages that will be sent to this channel by any application at any time. **Every message sent to this channel MUST be valid against one, and only one, of the message objects defined in this map.**"}, "messageObject": {"type": "object", "description": "Describes a message received on a given channel and operation.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"contentType": {"type": "string", "description": "The content type to use when encoding/decoding a message's payload. The value MUST be a specific media type (e.g. application/json). When omitted, the value MUST be the one specified on the defaultContentType field."}, "headers": {"$ref": "#/definitions/anySchema"}, "payload": {"$ref": "#/definitions/anySchema"}, "correlationId": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/correlationId"}]}, "tags": {"type": "array", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/tag"}]}, "uniqueItems": true}, "summary": {"type": "string", "description": "A brief summary of the message."}, "name": {"type": "string", "description": "Name of the message."}, "title": {"type": "string", "description": "A human-friendly title for the message."}, "description": {"type": "string", "description": "A longer description of the message. CommonMark is allowed."}, "externalDocs": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}, "deprecated": {"type": "boolean", "default": false}, "examples": {"type": "array", "description": "List of examples.", "items": {"$ref": "#/definitions/messageExampleObject"}}, "bindings": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/messageBindingsObject"}]}, "traits": {"type": "array", "description": "A list of traits to apply to the message object. Traits MUST be merged using traits merge mechanism. The resulting object MUST be a valid Message Object.", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/messageTrait"}, {"type": "array", "items": [{"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/messageTrait"}]}, {"type": "object", "additionalItems": true}]}]}}}, "examples": [{"messageId": "userSignup", "name": "UserSignup", "title": "User signup", "summary": "Action to sign a user up.", "description": "A longer description", "contentType": "application/json", "tags": [{"name": "user"}, {"name": "signup"}, {"name": "register"}], "headers": {"type": "object", "properties": {"correlationId": {"description": "Correlation ID set by application", "type": "string"}, "applicationInstanceId": {"description": "Unique identifier for a given instance of the publishing application", "type": "string"}}}, "payload": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/userCreate"}, "signup": {"$ref": "#/components/schemas/signup"}}}, "correlationId": {"description": "Default Correlation ID", "location": "$message.header#/correlationId"}, "traits": [{"$ref": "#/components/messageTraits/commonHeaders"}], "examples": [{"name": "SimpleSignup", "summary": "A simple UserSignup example message", "headers": {"correlationId": "my-correlation-id", "applicationInstanceId": "myInstanceId"}, "payload": {"user": {"someUserKey": "someUserValue"}, "signup": {"someSignupKey": "someSignupValue"}}}]}]}, "anySchema": {"if": {"required": ["schema"]}, "then": {"$ref": "#/definitions/multiFormatSchema"}, "else": {"$ref": "#/definitions/schema"}, "description": "An object representing either a schema or a multiFormatSchema based on the existence of the 'schema' property. If the property 'schema' is present, use the multi-format schema. Use the default AsyncAPI Schema otherwise."}, "multiFormatSchema": {"description": "The Multi Format Schema Object represents a schema definition. It differs from the Schema Object in that it supports multiple schema formats or languages (e.g., JSON Schema, Avro, etc.).", "type": "object", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "if": {"not": {"type": "object"}}, "then": {"$ref": "#/definitions/schema"}, "else": {"properties": {"schemaFormat": {"description": "A string containing the name of the schema format that is used to define the information. If schemaFormat is missing, it MUST default to application/vnd.aai.asyncapi+json;version={{asyncapi}} where {{asyncapi}} matches the AsyncAPI Version String. In such a case, this would make the Multi Format Schema Object equivalent to the Schema Object. When using Reference Object within the schema, the schemaFormat of the resource being referenced MUST match the schemaFormat of the schema that contains the initial reference. For example, if you reference Avro schema, then schemaFormat of referencing resource and the resource being reference MUST match.", "anyOf": [{"type": "string"}, {"description": "All the schema formats tooling MUST support", "enum": ["application/schema+json;version=draft-07", "application/schema+yaml;version=draft-07", "application/vnd.aai.asyncapi;version=3.0.0", "application/vnd.aai.asyncapi+json;version=3.0.0", "application/vnd.aai.asyncapi+yaml;version=3.0.0"]}, {"description": "All the schema formats tools are RECOMMENDED to support", "enum": ["application/vnd.oai.openapi;version=3.0.0", "application/vnd.oai.openapi+json;version=3.0.0", "application/vnd.oai.openapi+yaml;version=3.0.0", "application/vnd.apache.avro;version=1.9.0", "application/vnd.apache.avro+json;version=1.9.0", "application/vnd.apache.avro+yaml;version=1.9.0", "application/raml+yaml;version=1.0"]}]}}, "allOf": [{"if": {"not": {"description": "If no schemaFormat has been defined, default to schema or reference", "required": ["schemaFormat"]}}, "then": {"properties": {"schema": {"$ref": "#/definitions/schema"}}}}, {"if": {"description": "If schemaFormat has been defined check if it's one of the AsyncAPI Schema Object formats", "required": ["schemaFormat"], "properties": {"schemaFormat": {"enum": ["application/vnd.aai.asyncapi;version=2.0.0", "application/vnd.aai.asyncapi+json;version=2.0.0", "application/vnd.aai.asyncapi+yaml;version=2.0.0", "application/vnd.aai.asyncapi;version=2.1.0", "application/vnd.aai.asyncapi+json;version=2.1.0", "application/vnd.aai.asyncapi+yaml;version=2.1.0", "application/vnd.aai.asyncapi;version=2.2.0", "application/vnd.aai.asyncapi+json;version=2.2.0", "application/vnd.aai.asyncapi+yaml;version=2.2.0", "application/vnd.aai.asyncapi;version=2.3.0", "application/vnd.aai.asyncapi+json;version=2.3.0", "application/vnd.aai.asyncapi+yaml;version=2.3.0", "application/vnd.aai.asyncapi;version=2.4.0", "application/vnd.aai.asyncapi+json;version=2.4.0", "application/vnd.aai.asyncapi+yaml;version=2.4.0", "application/vnd.aai.asyncapi;version=2.5.0", "application/vnd.aai.asyncapi+json;version=2.5.0", "application/vnd.aai.asyncapi+yaml;version=2.5.0", "application/vnd.aai.asyncapi;version=2.6.0", "application/vnd.aai.asyncapi+json;version=2.6.0", "application/vnd.aai.asyncapi+yaml;version=2.6.0", "application/vnd.aai.asyncapi;version=3.0.0", "application/vnd.aai.asyncapi+json;version=3.0.0", "application/vnd.aai.asyncapi+yaml;version=3.0.0"]}}}, "then": {"properties": {"schema": {"$ref": "#/definitions/schema"}}}}, {"if": {"required": ["schemaFormat"], "properties": {"schemaFormat": {"enum": ["application/schema+json;version=draft-07", "application/schema+yaml;version=draft-07"]}}}, "then": {"properties": {"schema": {"$ref": "#/definitions/json-schema-draft-07-schema"}}}}, {"if": {"required": ["schemaFormat"], "properties": {"schemaFormat": {"enum": ["application/vnd.oai.openapi;version=3.0.0", "application/vnd.oai.openapi+json;version=3.0.0", "application/vnd.oai.openapi+yaml;version=3.0.0"]}}}, "then": {"properties": {"schema": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/openapiSchema_3_0"}]}}}}, {"if": {"required": ["schemaFormat"], "properties": {"schemaFormat": {"enum": ["application/vnd.apache.avro;version=1.9.0", "application/vnd.apache.avro+json;version=1.9.0", "application/vnd.apache.avro+yaml;version=1.9.0"]}}}, "then": {"properties": {"schema": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/avroSchema_v1"}]}}}}]}}, "openapiSchema_3_0": {"type": "object", "definitions": {"ExternalDocumentation": {"type": "object", "required": ["url"], "properties": {"description": {"type": "string"}, "url": {"type": "string", "format": "uri-reference"}}, "patternProperties": {"^x-": {}}, "additionalProperties": false}, "Discriminator": {"type": "object", "required": ["propertyName"], "properties": {"propertyName": {"type": "string"}, "mapping": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Reference": {"type": "object", "required": ["$ref"], "patternProperties": {"^\\$ref$": {"type": "string", "format": "uri-reference"}}}, "XML": {"type": "object", "properties": {"name": {"type": "string"}, "namespace": {"type": "string", "format": "uri"}, "prefix": {"type": "string"}, "attribute": {"type": "boolean", "default": false}, "wrapped": {"type": "boolean", "default": false}}, "patternProperties": {"^x-": {}}, "additionalProperties": false}}, "properties": {"title": {"type": "string"}, "multipleOf": {"type": "number", "exclusiveMinimum": 0}, "maximum": {"type": "number"}, "exclusiveMaximum": {"type": "boolean", "default": false}, "minimum": {"type": "number"}, "exclusiveMinimum": {"type": "boolean", "default": false}, "maxLength": {"type": "integer", "minimum": 0}, "minLength": {"type": "integer", "minimum": 0, "default": 0}, "pattern": {"type": "string", "format": "regex"}, "maxItems": {"type": "integer", "minimum": 0}, "minItems": {"type": "integer", "minimum": 0, "default": 0}, "uniqueItems": {"type": "boolean", "default": false}, "maxProperties": {"type": "integer", "minimum": 0}, "minProperties": {"type": "integer", "minimum": 0, "default": 0}, "required": {"type": "array", "items": {"type": "string"}, "minItems": 1, "uniqueItems": true}, "enum": {"type": "array", "items": true, "minItems": 1, "uniqueItems": false}, "type": {"type": "string", "enum": ["array", "boolean", "integer", "number", "object", "string"]}, "not": {"oneOf": [{"$ref": "#/definitions/openapiSchema_3_0"}, {"$ref": "#/definitions/openapiSchema_3_0/definitions/Reference"}]}, "allOf": {"type": "array", "items": {"oneOf": [{"$ref": "#/definitions/openapiSchema_3_0"}, {"$ref": "#/definitions/openapiSchema_3_0/definitions/Reference"}]}}, "oneOf": {"type": "array", "items": {"oneOf": [{"$ref": "#/definitions/openapiSchema_3_0"}, {"$ref": "#/definitions/openapiSchema_3_0/definitions/Reference"}]}}, "anyOf": {"type": "array", "items": {"oneOf": [{"$ref": "#/definitions/openapiSchema_3_0"}, {"$ref": "#/definitions/openapiSchema_3_0/definitions/Reference"}]}}, "items": {"oneOf": [{"$ref": "#/definitions/openapiSchema_3_0"}, {"$ref": "#/definitions/openapiSchema_3_0/definitions/Reference"}]}, "properties": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/definitions/openapiSchema_3_0"}, {"$ref": "#/definitions/openapiSchema_3_0/definitions/Reference"}]}}, "additionalProperties": {"oneOf": [{"$ref": "#/definitions/openapiSchema_3_0"}, {"$ref": "#/definitions/openapiSchema_3_0/definitions/Reference"}, {"type": "boolean"}], "default": true}, "description": {"type": "string"}, "format": {"type": "string"}, "default": true, "nullable": {"type": "boolean", "default": false}, "discriminator": {"$ref": "#/definitions/openapiSchema_3_0/definitions/Discriminator"}, "readOnly": {"type": "boolean", "default": false}, "writeOnly": {"type": "boolean", "default": false}, "example": true, "externalDocs": {"$ref": "#/definitions/openapiSchema_3_0/definitions/ExternalDocumentation"}, "deprecated": {"type": "boolean", "default": false}, "xml": {"$ref": "#/definitions/openapiSchema_3_0/definitions/XML"}}, "patternProperties": {"^x-": true}, "additionalProperties": false}, "avroSchema_v1": {"definitions": {"avroSchema": {"title": "<PERSON><PERSON><PERSON>", "description": "Root Schema", "oneOf": [{"$ref": "#/definitions/avroSchema_v1/definitions/types"}]}, "types": {"title": "Avro Types", "description": "Allowed Avro types", "oneOf": [{"$ref": "#/definitions/avroSchema_v1/definitions/primitiveType"}, {"$ref": "#/definitions/avroSchema_v1/definitions/primitiveTypeWithMetadata"}, {"$ref": "#/definitions/avroSchema_v1/definitions/customTypeReference"}, {"$ref": "#/definitions/avroSchema_v1/definitions/avroRecord"}, {"$ref": "#/definitions/avroSchema_v1/definitions/avroEnum"}, {"$ref": "#/definitions/avroSchema_v1/definitions/avroArray"}, {"$ref": "#/definitions/avroSchema_v1/definitions/avroMap"}, {"$ref": "#/definitions/avroSchema_v1/definitions/avroFixed"}, {"$ref": "#/definitions/avroSchema_v1/definitions/avroUnion"}]}, "primitiveType": {"title": "Primitive Type", "description": "Basic type primitives.", "type": "string", "enum": ["null", "boolean", "int", "long", "float", "double", "bytes", "string"]}, "primitiveTypeWithMetadata": {"title": "Primitive Type With Metadata", "description": "A primitive type with metadata attached.", "type": "object", "properties": {"type": {"$ref": "#/definitions/avroSchema_v1/definitions/primitiveType"}}, "required": ["type"]}, "customTypeReference": {"title": "Custom Type", "description": "Reference to a ComplexType", "not": {"$ref": "#/definitions/avroSchema_v1/definitions/primitiveType"}, "type": "string", "pattern": "^[A-Za-z_][A-Za-z0-9_]*(\\.[A-Za-z_][A-Za-z0-9_]*)*$"}, "avroUnion": {"title": "Union", "description": "A Union of types", "type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/avroSchema"}, "minItems": 1}, "avroField": {"title": "Field", "description": "A field within a Record", "type": "object", "properties": {"name": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}, "type": {"$ref": "#/definitions/avroSchema_v1/definitions/types"}, "doc": {"type": "string"}, "default": true, "order": {"enum": ["ascending", "descending", "ignore"]}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}}}, "required": ["name", "type"]}, "avroRecord": {"title": "Record", "description": "A Record", "type": "object", "properties": {"type": {"type": "string", "const": "record"}, "name": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}, "namespace": {"$ref": "#/definitions/avroSchema_v1/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}}, "fields": {"type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/avroField"}}}, "required": ["type", "name", "fields"]}, "avroEnum": {"title": "Enum", "description": "An enumeration", "type": "object", "properties": {"type": {"type": "string", "const": "enum"}, "name": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}, "namespace": {"$ref": "#/definitions/avroSchema_v1/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}}, "symbols": {"type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}}}, "required": ["type", "name", "symbols"]}, "avroArray": {"title": "Array", "description": "An array", "type": "object", "properties": {"type": {"type": "string", "const": "array"}, "name": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}, "namespace": {"$ref": "#/definitions/avroSchema_v1/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}}, "items": {"$ref": "#/definitions/avroSchema_v1/definitions/types"}}, "required": ["type", "items"]}, "avroMap": {"title": "Map", "description": "A map of values", "type": "object", "properties": {"type": {"type": "string", "const": "map"}, "name": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}, "namespace": {"$ref": "#/definitions/avroSchema_v1/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}}, "values": {"$ref": "#/definitions/avroSchema_v1/definitions/types"}}, "required": ["type", "values"]}, "avroFixed": {"title": "Fixed", "description": "A fixed sized array of bytes", "type": "object", "properties": {"type": {"type": "string", "const": "fixed"}, "name": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}, "namespace": {"$ref": "#/definitions/avroSchema_v1/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/avroSchema_v1/definitions/name"}}, "size": {"type": "number"}}, "required": ["type", "name", "size"]}, "name": {"type": "string", "pattern": "^[A-Za-z_][A-Za-z0-9_]*$"}, "namespace": {"type": "string", "pattern": "^([A-Za-z_][A-Za-z0-9_]*(\\.[A-Za-z_][A-Za-z0-9_]*)*)*$"}}, "description": "Json-Schema definition for Avro AVSC files.", "oneOf": [{"$ref": "#/definitions/avroSchema_v1/definitions/avroSchema"}], "title": "Avro Schema Definition"}, "correlationId": {"type": "object", "description": "An object that specifies an identifier at design time that can used for message tracing and correlation.", "required": ["location"], "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"description": {"type": "string", "description": "A optional description of the correlation ID. GitHub Flavored Markdown is allowed."}, "location": {"type": "string", "description": "A runtime expression that specifies the location of the correlation ID", "pattern": "^\\$message\\.(header|payload)#(\\/(([^\\/~])|(~[01]))*)*"}}, "examples": [{"description": "Default Correlation ID", "location": "$message.header#/correlationId"}]}, "messageExampleObject": {"type": "object", "additionalProperties": false, "anyOf": [{"required": ["payload"]}, {"required": ["headers"]}], "properties": {"name": {"type": "string", "description": "Machine readable name of the message example."}, "summary": {"type": "string", "description": "A brief summary of the message example."}, "headers": {"type": "object", "description": "Example of the application headers. It MUST be a map of key-value pairs."}, "payload": {"type": ["number", "string", "boolean", "object", "array", "null"], "description": "Example of the message payload. It can be of any type."}}}, "messageBindingsObject": {"type": "object", "description": "Map describing protocol-specific definitions for a message.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"http": {"properties": {"bindingVersion": {"enum": ["0.2.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-http-0.3.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-http-0.2.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-http-0.3.0-message"}}]}, "ws": {}, "amqp": {"properties": {"bindingVersion": {"enum": ["0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-amqp-0.3.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-amqp-0.3.0-message"}}]}, "amqp1": {}, "mqtt": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-mqtt-0.2.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-mqtt-0.2.0-message"}}]}, "kafka": {"properties": {"bindingVersion": {"enum": ["0.5.0", "0.4.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-kafka-0.5.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.5.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.5.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.4.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.3.0-message"}}]}, "anypointmq": {"properties": {"bindingVersion": {"enum": ["0.0.1"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-anypointmq-0.0.1-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.0.1"}}}, "then": {"$ref": "#/definitions/bindings-anypointmq-0.0.1-message"}}]}, "nats": {}, "jms": {"properties": {"bindingVersion": {"enum": ["0.0.1"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-jms-0.0.1-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.0.1"}}}, "then": {"$ref": "#/definitions/bindings-jms-0.0.1-message"}}]}, "sns": {}, "sqs": {}, "stomp": {}, "redis": {}, "ibmmq": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-ibmmq-0.1.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-ibmmq-0.1.0-message"}}]}, "solace": {}, "googlepubsub": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-googlepubsub-0.2.0-message"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-googlepubsub-0.2.0-message"}}]}}}, "bindings-http-0.3.0-message": {"title": "HTTP message bindings object", "description": "This object contains information about the message representation in HTTP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"headers": {"$ref": "#/definitions/schema", "description": "\tA Schema object containing the definitions for HTTP-specific headers. This schema MUST be of type 'object' and have a 'properties' key."}, "statusCode": {"type": "number", "description": "The HTTP response status code according to [RFC 9110](https://httpwg.org/specs/rfc9110.html#overview.of.status.codes). `statusCode` is only relevant for messages referenced by the [Operation Reply Object](https://www.asyncapi.com/docs/reference/specification/v3.0.0#operationReplyObject), as it defines the status code for the response. In all other cases, this value can be safely ignored."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, \"latest\" MUST be assumed."}}, "examples": [{"headers": {"type": "object", "properties": {"Content-Type": {"type": "string", "enum": ["application/json"]}}}, "bindingVersion": "0.3.0"}]}, "bindings-http-0.2.0-message": {"title": "HTTP message bindings object", "description": "This object contains information about the message representation in HTTP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"headers": {"$ref": "#/definitions/schema", "description": "\tA Schema object containing the definitions for HTTP-specific headers. This schema MUST be of type 'object' and have a 'properties' key."}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding. If omitted, \"latest\" MUST be assumed."}}, "examples": [{"headers": {"type": "object", "properties": {"Content-Type": {"type": "string", "enum": ["application/json"]}}}, "bindingVersion": "0.2.0"}]}, "bindings-amqp-0.3.0-message": {"title": "AMQP message bindings object", "description": "This object contains information about the message representation in AMQP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"contentEncoding": {"type": "string", "description": "A MIME encoding for the message content."}, "messageType": {"type": "string", "description": "Application-specific message type."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, \"latest\" MUST be assumed."}}, "examples": [{"contentEncoding": "gzip", "messageType": "user.signup", "bindingVersion": "0.3.0"}]}, "bindings-mqtt-0.2.0-message": {"title": "MQTT message bindings object", "description": "This object contains information about the message representation in MQTT.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"payloadFormatIndicator": {"type": "integer", "enum": [0, 1], "description": "1 indicates that the payload is UTF-8 encoded character data.  0 indicates that the payload format is unspecified.", "default": 0}, "correlationData": {"oneOf": [{"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/Reference"}], "description": "Correlation Data is used by the sender of the request message to identify which request the response message is for when it is received."}, "contentType": {"type": "string", "description": "String describing the content type of the message payload. This should not conflict with the contentType field of the associated AsyncAPI Message object."}, "responseTopic": {"oneOf": [{"type": "string", "format": "uri-template", "minLength": 1}, {"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/Reference"}], "description": "The topic (channel URI) to be used for a response message."}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"bindingVersion": "0.2.0"}, {"contentType": "application/json", "correlationData": {"type": "string", "format": "uuid"}, "responseTopic": "application/responses", "bindingVersion": "0.2.0"}]}, "bindings-kafka-0.5.0-message": {"title": "Message Schema", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"key": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/schema"}], "description": "The message key."}, "schemaIdLocation": {"type": "string", "description": "If a Schema Registry is used when performing this operation, tells where the id of schema is stored.", "enum": ["header", "payload"]}, "schemaIdPayloadEncoding": {"type": "string", "description": "Number of bytes or vendor specific values when schema id is encoded in payload."}, "schemaLookupStrategy": {"type": "string", "description": "Freeform string for any naming strategy class to use. Clients should default to the vendor default if not supplied."}, "bindingVersion": {"type": "string", "enum": ["0.5.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"key": {"type": "string", "enum": ["my<PERSON>ey"]}, "schemaIdLocation": "payload", "schemaIdPayloadEncoding": "apicurio-new", "schemaLookupStrategy": "TopicIdStrategy", "bindingVersion": "0.5.0"}, {"key": {"$ref": "path/to/user-create.avsc#/UserCreate"}, "schemaIdLocation": "payload", "schemaIdPayloadEncoding": "4", "bindingVersion": "0.5.0"}]}, "bindings-kafka-0.4.0-message": {"title": "Message Schema", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"key": {"anyOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/avroSchema_v1"}], "description": "The message key."}, "schemaIdLocation": {"type": "string", "description": "If a Schema Registry is used when performing this operation, tells where the id of schema is stored.", "enum": ["header", "payload"]}, "schemaIdPayloadEncoding": {"type": "string", "description": "Number of bytes or vendor specific values when schema id is encoded in payload."}, "schemaLookupStrategy": {"type": "string", "description": "Freeform string for any naming strategy class to use. Clients should default to the vendor default if not supplied."}, "bindingVersion": {"type": "string", "enum": ["0.4.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"key": {"type": "string", "enum": ["my<PERSON>ey"]}, "schemaIdLocation": "payload", "schemaIdPayloadEncoding": "apicurio-new", "schemaLookupStrategy": "TopicIdStrategy", "bindingVersion": "0.4.0"}, {"key": {"$ref": "path/to/user-create.avsc#/UserCreate"}, "schemaIdLocation": "payload", "schemaIdPayloadEncoding": "4", "bindingVersion": "0.4.0"}]}, "bindings-kafka-0.3.0-message": {"title": "Message Schema", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"key": {"$ref": "#/definitions/schema", "description": "The message key."}, "schemaIdLocation": {"type": "string", "description": "If a Schema Registry is used when performing this operation, tells where the id of schema is stored.", "enum": ["header", "payload"]}, "schemaIdPayloadEncoding": {"type": "string", "description": "Number of bytes or vendor specific values when schema id is encoded in payload."}, "schemaLookupStrategy": {"type": "string", "description": "Freeform string for any naming strategy class to use. Clients should default to the vendor default if not supplied."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"key": {"type": "string", "enum": ["my<PERSON>ey"]}, "schemaIdLocation": "payload", "schemaIdPayloadEncoding": "apicurio-new", "schemaLookupStrategy": "TopicIdStrategy", "bindingVersion": "0.3.0"}, {"key": {"$ref": "path/to/user-create.avsc#/UserCreate"}, "schemaIdLocation": "payload", "schemaIdPayloadEncoding": "4", "bindingVersion": "0.3.0"}]}, "bindings-anypointmq-0.0.1-message": {"title": "Anypoint MQ message bindings object", "description": "This object contains configuration for describing an Anypoint MQ message as an AsyncAPI message. This objects only contains configuration that can not be provided in the AsyncAPI standard message object.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"headers": {"oneOf": [{"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/Reference"}], "description": "A Schema object containing the definitions for Anypoint MQ-specific headers (protocol headers). This schema MUST be of type 'object' and have a 'properties' key. Examples of Anypoint MQ protocol headers are 'messageId' and 'messageGroupId'."}, "bindingVersion": {"type": "string", "enum": ["0.0.1"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"headers": {"type": "object", "properties": {"messageId": {"type": "string"}}}, "bindingVersion": "0.0.1"}]}, "bindings-jms-0.0.1-message": {"title": "Message Schema", "description": "This object contains configuration for describing a JMS message as an AsyncAPI message. This objects only contains configuration that can not be provided in the AsyncAPI standard message object.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"headers": {"$ref": "#/definitions/schema", "description": "A Schema object containing the definitions for JMS headers (protocol headers). This schema MUST be of type 'object' and have a 'properties' key. Examples of JMS protocol headers are 'JMSMessageID', 'JMSTimestamp', and 'JMSCorrelationID'."}, "bindingVersion": {"type": "string", "enum": ["0.0.1"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"headers": {"type": "object", "required": ["JMSMessageID"], "properties": {"JMSMessageID": {"type": ["string", "null"], "description": "A unique message identifier. This may be set by your JMS Provider on your behalf."}, "JMSTimestamp": {"type": "integer", "description": "The time the message was sent. This may be set by your JMS Provider on your behalf. The time the message was sent. The value of the timestamp is the amount of time, measured in milliseconds, that has elapsed since midnight, January 1, 1970, UTC."}, "JMSDeliveryMode": {"type": "string", "enum": ["PERSISTENT", "NON_PERSISTENT"], "default": "PERSISTENT", "description": "Denotes the delivery mode for the message. This may be set by your JMS Provider on your behalf."}, "JMSPriority": {"type": "integer", "default": 4, "description": "The priority of the message. This may be set by your JMS Provider on your behalf."}, "JMSExpires": {"type": "integer", "description": "The time at which the message expires. This may be set by your JMS Provider on your behalf. A value of zero means that the message does not expire. Any non-zero value is the amount of time, measured in milliseconds, that has elapsed since midnight, January 1, 1970, UTC, at which the message will expire."}, "JMSType": {"type": ["string", "null"], "description": "The type of message. Some JMS providers use a message repository that contains the definitions of messages sent by applications. The 'JMSType' header field may reference a message's definition in the provider's repository. The JMS API does not define a standard message definition repository, nor does it define a naming policy for the definitions it contains. Some messaging systems require that a message type definition for each application message be created and that each message specify its type. In order to work with such JMS providers, JMS clients should assign a value to 'JMSType', whether the application makes use of it or not. This ensures that the field is properly set for those providers that require it."}, "JMSCorrelationID": {"type": ["string", "null"], "description": "The correlation identifier of the message. A client can use the 'JMSCorrelationID' header field to link one message with another. A typical use is to link a response message with its request message. Since each message sent by a JMS provider is assigned a message ID value, it is convenient to link messages via message ID, such message ID values must start with the 'ID:' prefix. Conversely, application-specified values must not start with the 'ID:' prefix; this is reserved for provider-generated message ID values."}, "JMSReplyTo": {"type": "string", "description": "The queue or topic that the message sender expects replies to."}}}, "bindingVersion": "0.0.1"}]}, "bindings-ibmmq-0.1.0-message": {"title": "IBM MQ message bindings object", "description": "This object contains information about the message representation in IBM MQ.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"type": {"type": "string", "enum": ["string", "jms", "binary"], "default": "string", "description": "The type of the message."}, "headers": {"type": "string", "description": "Defines the IBM MQ message headers to include with this message. More than one header can be specified as a comma separated list. Supporting information on IBM MQ message formats can be found on this [page](https://www.ibm.com/docs/en/ibm-mq/9.2?topic=mqmd-format-mqchar8) in the IBM MQ Knowledge Center."}, "description": {"type": "string", "description": "Provides additional information for application developers: describes the message type or format."}, "expiry": {"type": "integer", "minimum": 0, "default": 0, "description": "The recommended setting the client should use for the TTL (Time-To-Live) of the message. This is a period of time expressed in milliseconds and set by the application that puts the message. 'expiry' values are API dependant e.g., MQI and JMS use different units of time and default values for 'unlimited'. General information on IBM MQ message expiry can be found on this [page](https://www.ibm.com/docs/en/ibm-mq/9.2?topic=mqmd-expiry-mqlong) in the IBM MQ Knowledge Center."}, "bindingVersion": {"type": "string", "enum": ["0.1.0"], "description": "The version of this binding."}}, "oneOf": [{"properties": {"type": {"const": "binary"}}}, {"properties": {"type": {"const": "jms"}}, "not": {"required": ["headers"]}}, {"properties": {"type": {"const": "string"}}, "not": {"required": ["headers"]}}], "examples": [{"type": "string", "bindingVersion": "0.1.0"}, {"type": "jms", "description": "JMS stream message", "bindingVersion": "0.1.0"}]}, "bindings-googlepubsub-0.2.0-message": {"title": "Cloud Pub/Sub Channel Schema", "description": "This object contains information about the message representation for Google Cloud Pub/Sub.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding."}, "attributes": {"type": "object"}, "orderingKey": {"type": "string"}, "schema": {"type": "object", "additionalItems": false, "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "examples": [{"schema": {"name": "projects/your-project-id/schemas/your-avro-schema-id"}}, {"schema": {"name": "projects/your-project-id/schemas/your-protobuf-schema-id"}}]}, "messageTrait": {"type": "object", "description": "Describes a trait that MAY be applied to a Message Object. This object MAY contain any property from the Message Object, except payload and traits.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"contentType": {"type": "string", "description": "The content type to use when encoding/decoding a message's payload. The value MUST be a specific media type (e.g. application/json). When omitted, the value MUST be the one specified on the defaultContentType field."}, "headers": {"$ref": "#/definitions/anySchema"}, "correlationId": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/correlationId"}]}, "tags": {"type": "array", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/tag"}]}, "uniqueItems": true}, "summary": {"type": "string", "description": "A brief summary of the message."}, "name": {"type": "string", "description": "Name of the message."}, "title": {"type": "string", "description": "A human-friendly title for the message."}, "description": {"type": "string", "description": "A longer description of the message. CommonMark is allowed."}, "externalDocs": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}, "deprecated": {"type": "boolean", "default": false}, "examples": {"type": "array", "description": "List of examples.", "items": {"$ref": "#/definitions/messageExampleObject"}}, "bindings": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/messageBindingsObject"}]}}, "examples": [{"contentType": "application/json"}]}, "parameters": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/parameter"}]}, "description": "JSON objects describing re-usable channel parameters.", "examples": [{"address": "user/{userId}/signedup", "parameters": {"userId": {"description": "Id of the user."}}}]}, "parameter": {"description": "Describes a parameter included in a channel address.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"description": {"type": "string", "description": "A brief description of the parameter. This could contain examples of use. GitHub Flavored Markdown is allowed."}, "enum": {"description": "An enumeration of string values to be used if the substitution options are from a limited set.", "type": "array", "items": {"type": "string"}}, "default": {"description": "The default value to use for substitution, and to send, if an alternate value is not supplied.", "type": "string"}, "examples": {"description": "An array of examples of the parameter value.", "type": "array", "items": {"type": "string"}}, "location": {"type": "string", "description": "A runtime expression that specifies the location of the parameter value", "pattern": "^\\$message\\.(header|payload)#(\\/(([^\\/~])|(~[01]))*)*"}}, "examples": [{"address": "user/{userId}/signedup", "parameters": {"userId": {"description": "Id of the user.", "location": "$message.payload#/user/id"}}}]}, "channelBindingsObject": {"type": "object", "description": "Map describing protocol-specific definitions for a channel.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"http": {}, "ws": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-websockets-0.1.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-websockets-0.1.0-channel"}}]}, "amqp": {"properties": {"bindingVersion": {"enum": ["0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-amqp-0.3.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-amqp-0.3.0-channel"}}]}, "amqp1": {}, "mqtt": {}, "kafka": {"properties": {"bindingVersion": {"enum": ["0.5.0", "0.4.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-kafka-0.5.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.5.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.5.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.4.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.3.0-channel"}}]}, "anypointmq": {"properties": {"bindingVersion": {"enum": ["0.0.1"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-anypointmq-0.0.1-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.0.1"}}}, "then": {"$ref": "#/definitions/bindings-anypointmq-0.0.1-channel"}}]}, "nats": {}, "jms": {"properties": {"bindingVersion": {"enum": ["0.0.1"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-jms-0.0.1-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.0.1"}}}, "then": {"$ref": "#/definitions/bindings-jms-0.0.1-channel"}}]}, "sns": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-sns-0.1.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-sns-0.1.0-channel"}}]}, "sqs": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-sqs-0.2.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-sqs-0.2.0-channel"}}]}, "stomp": {}, "redis": {}, "ibmmq": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-ibmmq-0.1.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-ibmmq-0.1.0-channel"}}]}, "solace": {}, "googlepubsub": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-googlepubsub-0.2.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-googlepubsub-0.2.0-channel"}}]}, "pulsar": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-pulsar-0.1.0-channel"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-pulsar-0.1.0-channel"}}]}}}, "bindings-websockets-0.1.0-channel": {"title": "WebSockets channel bindings object", "description": "When using WebSockets, the channel represents the connection. Unlike other protocols that support multiple virtual channels (topics, routing keys, etc.) per connection, WebSockets doesn't support virtual channels or, put it another way, there's only one channel and its characteristics are strongly related to the protocol used for the handshake, i.e., HTTP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"method": {"type": "string", "enum": ["GET", "POST"], "description": "The HTTP method to use when establishing the connection. Its value MUST be either 'GET' or 'POST'."}, "query": {"oneOf": [{"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/Reference"}], "description": "A Schema object containing the definitions for each query parameter. This schema MUST be of type 'object' and have a 'properties' key."}, "headers": {"oneOf": [{"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/Reference"}], "description": "A Schema object containing the definitions of the HTTP headers to use when establishing the connection. This schema MUST be of type 'object' and have a 'properties' key."}, "bindingVersion": {"type": "string", "enum": ["0.1.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"method": "POST", "bindingVersion": "0.1.0"}]}, "bindings-amqp-0.3.0-channel": {"title": "AMQP channel bindings object", "description": "This object contains information about the channel representation in AMQP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"is": {"type": "string", "enum": ["queue", "routing<PERSON>ey"], "description": "Defines what type of channel is it. Can be either 'queue' or 'routingKey' (default)."}, "exchange": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255, "description": "The name of the exchange. It MUST NOT exceed 255 characters long."}, "type": {"type": "string", "enum": ["topic", "direct", "fanout", "default", "headers"], "description": "The type of the exchange. Can be either 'topic', 'direct', 'fanout', 'default' or 'headers'."}, "durable": {"type": "boolean", "description": "Whether the exchange should survive broker restarts or not."}, "autoDelete": {"type": "boolean", "description": "Whether the exchange should be deleted when the last queue is unbound from it."}, "vhost": {"type": "string", "default": "/", "description": "The virtual host of the exchange. Defaults to '/'."}}, "description": "When is=routingKey, this object defines the exchange properties."}, "queue": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255, "description": "The name of the queue. It MUST NOT exceed 255 characters long."}, "durable": {"type": "boolean", "description": "Whether the queue should survive broker restarts or not."}, "exclusive": {"type": "boolean", "description": "Whether the queue should be used only by one connection or not."}, "autoDelete": {"type": "boolean", "description": "Whether the queue should be deleted when the last consumer unsubscribes."}, "vhost": {"type": "string", "default": "/", "description": "The virtual host of the queue. Defaults to '/'."}}, "description": "When is=queue, this object defines the queue properties."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "oneOf": [{"properties": {"is": {"const": "routing<PERSON>ey"}}, "required": ["exchange"], "not": {"required": ["queue"]}}, {"properties": {"is": {"const": "queue"}}, "required": ["queue"], "not": {"required": ["exchange"]}}], "examples": [{"is": "routing<PERSON>ey", "exchange": {"name": "myExchange", "type": "topic", "durable": true, "autoDelete": false, "vhost": "/"}, "bindingVersion": "0.3.0"}, {"is": "queue", "queue": {"name": "my-queue-name", "durable": true, "exclusive": true, "autoDelete": false, "vhost": "/"}, "bindingVersion": "0.3.0"}]}, "bindings-kafka-0.5.0-channel": {"title": "Channel Schema", "description": "This object contains information about the channel representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"topic": {"type": "string", "description": "Kafka topic name if different from channel name."}, "partitions": {"type": "integer", "minimum": 1, "description": "Number of partitions configured on this topic."}, "replicas": {"type": "integer", "minimum": 1, "description": "Number of replicas configured on this topic."}, "topicConfiguration": {"description": "Topic configuration properties that are relevant for the API.", "type": "object", "additionalProperties": true, "properties": {"cleanup.policy": {"description": "The [`cleanup.policy`](https://kafka.apache.org/documentation/#topicconfigs_cleanup.policy) configuration option.", "type": "array", "items": {"type": "string", "enum": ["compact", "delete"]}}, "retention.ms": {"description": "The [`retention.ms`](https://kafka.apache.org/documentation/#topicconfigs_retention.ms) configuration option.", "type": "integer", "minimum": -1}, "retention.bytes": {"description": "The [`retention.bytes`](https://kafka.apache.org/documentation/#topicconfigs_retention.bytes) configuration option.", "type": "integer", "minimum": -1}, "delete.retention.ms": {"description": "The [`delete.retention.ms`](https://kafka.apache.org/documentation/#topicconfigs_delete.retention.ms) configuration option.", "type": "integer", "minimum": 0}, "max.message.bytes": {"description": "The [`max.message.bytes`](https://kafka.apache.org/documentation/#topicconfigs_max.message.bytes) configuration option.", "type": "integer", "minimum": 0}, "confluent.key.schema.validation": {"description": "It shows whether the schema validation for the message key is enabled. Vendor specific config. For more details: (https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#confluent-key-schema-validation)", "type": "boolean"}, "confluent.key.subject.name.strategy": {"description": "The name of the schema lookup strategy for the message key. Vendor specific config. For more details: (https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#confluent-key-subject-name-strategy)", "type": "string"}, "confluent.value.schema.validation": {"description": "It shows whether the schema validation for the message value is enabled. Vendor specific config. For more details: (https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#confluent-value-schema-validation)", "type": "boolean"}, "confluent.value.subject.name.strategy": {"description": "The name of the schema lookup strategy for the message value. Vendor specific config. For more details: (https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#confluent-value-subject-name-strategy)", "type": "string"}}}, "bindingVersion": {"type": "string", "enum": ["0.5.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"topic": "my-specific-topic", "partitions": 20, "replicas": 3, "bindingVersion": "0.5.0"}]}, "bindings-kafka-0.4.0-channel": {"title": "Channel Schema", "description": "This object contains information about the channel representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"topic": {"type": "string", "description": "Kafka topic name if different from channel name."}, "partitions": {"type": "integer", "minimum": 1, "description": "Number of partitions configured on this topic."}, "replicas": {"type": "integer", "minimum": 1, "description": "Number of replicas configured on this topic."}, "topicConfiguration": {"description": "Topic configuration properties that are relevant for the API.", "type": "object", "additionalProperties": false, "properties": {"cleanup.policy": {"description": "The [`cleanup.policy`](https://kafka.apache.org/documentation/#topicconfigs_cleanup.policy) configuration option.", "type": "array", "items": {"type": "string", "enum": ["compact", "delete"]}}, "retention.ms": {"description": "The [`retention.ms`](https://kafka.apache.org/documentation/#topicconfigs_retention.ms) configuration option.", "type": "integer", "minimum": -1}, "retention.bytes": {"description": "The [`retention.bytes`](https://kafka.apache.org/documentation/#topicconfigs_retention.bytes) configuration option.", "type": "integer", "minimum": -1}, "delete.retention.ms": {"description": "The [`delete.retention.ms`](https://kafka.apache.org/documentation/#topicconfigs_delete.retention.ms) configuration option.", "type": "integer", "minimum": 0}, "max.message.bytes": {"description": "The [`max.message.bytes`](https://kafka.apache.org/documentation/#topicconfigs_max.message.bytes) configuration option.", "type": "integer", "minimum": 0}}}, "bindingVersion": {"type": "string", "enum": ["0.4.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"topic": "my-specific-topic", "partitions": 20, "replicas": 3, "bindingVersion": "0.4.0"}]}, "bindings-kafka-0.3.0-channel": {"title": "Channel Schema", "description": "This object contains information about the channel representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"topic": {"type": "string", "description": "Kafka topic name if different from channel name."}, "partitions": {"type": "integer", "minimum": 1, "description": "Number of partitions configured on this topic."}, "replicas": {"type": "integer", "minimum": 1, "description": "Number of replicas configured on this topic."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"topic": "my-specific-topic", "partitions": 20, "replicas": 3, "bindingVersion": "0.3.0"}]}, "bindings-anypointmq-0.0.1-channel": {"title": "Anypoint MQ channel bindings object", "description": "This object contains configuration for describing an Anypoint MQ exchange, queue, or FIFO queue as an AsyncAPI channel. This objects only contains configuration that can not be provided in the AsyncAPI standard channel object.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"destination": {"type": "string", "description": "The destination (queue or exchange) name for this channel. SHOULD only be specified if the channel name differs from the actual destination name, such as when the channel name is not a valid destination name in Anypoint MQ. Defaults to the channel name."}, "destinationType": {"type": "string", "enum": ["exchange", "queue", "fifo-queue"], "default": "queue", "description": "The type of destination. SHOULD be specified to document the messaging model (publish/subscribe, point-to-point, strict message ordering) supported by this channel."}, "bindingVersion": {"type": "string", "enum": ["0.0.1"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"destination": "user-signup-exchg", "destinationType": "exchange", "bindingVersion": "0.0.1"}]}, "bindings-jms-0.0.1-channel": {"title": "Channel Schema", "description": "This object contains configuration for describing a JMS queue, or FIFO queue as an AsyncAPI channel. This objects only contains configuration that can not be provided in the AsyncAPI standard channel object.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"destination": {"type": "string", "description": "The destination (queue) name for this channel. SHOULD only be specified if the channel name differs from the actual destination name, such as when the channel name is not a valid destination name according to the JMS Provider. Defaults to the channel name."}, "destinationType": {"type": "string", "enum": ["queue", "fifo-queue"], "default": "queue", "description": "The type of destination. SHOULD be specified to document the messaging model (point-to-point, or strict message ordering) supported by this channel."}, "bindingVersion": {"type": "string", "enum": ["0.0.1"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"destination": "user-signed-up", "destinationType": "fifo-queue", "bindingVersion": "0.0.1"}]}, "bindings-sns-0.1.0-channel": {"title": "Channel Schema", "description": "This object contains information about the channel representation in SNS.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"name": {"type": "string", "description": "The name of the topic. Can be different from the channel name to allow flexibility around AWS resource naming limitations."}, "ordering": {"$ref": "#/definitions/bindings-sns-0.1.0-channel/definitions/ordering"}, "policy": {"$ref": "#/definitions/bindings-sns-0.1.0-channel/definitions/policy"}, "tags": {"type": "object", "description": "Key-value pairs that represent AWS tags on the topic."}, "bindingVersion": {"type": "string", "description": "The version of this binding.", "default": "latest"}}, "required": ["name"], "definitions": {"ordering": {"type": "object", "description": "By default, we assume an unordered SNS topic. This field allows configuration of a FIFO SNS Topic.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"type": {"type": "string", "description": "Defines the type of SNS Topic.", "enum": ["standard", "FIFO"]}, "contentBasedDeduplication": {"type": "boolean", "description": "True to turn on de-duplication of messages for a channel."}}, "required": ["type"]}, "policy": {"type": "object", "description": "The security policy for the SNS Topic.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"statements": {"type": "array", "description": "An array of statement objects, each of which controls a permission for this topic", "items": {"$ref": "#/definitions/bindings-sns-0.1.0-channel/definitions/statement"}}}, "required": ["statements"]}, "statement": {"type": "object", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"effect": {"type": "string", "enum": ["Allow", "<PERSON><PERSON>"]}, "principal": {"description": "The AWS account or resource ARN that this statement applies to.", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "action": {"description": "The SNS permission being allowed or denied e.g. sns:Publish", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["effect", "principal", "action"]}}, "examples": [{"name": "my-sns-topic", "policy": {"statements": [{"effect": "Allow", "principal": "*", "action": "SNS:Publish"}]}}]}, "bindings-sqs-0.2.0-channel": {"title": "Channel Schema", "description": "This object contains information about the channel representation in SQS.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"queue": {"description": "A definition of the queue that will be used as the channel.", "$ref": "#/definitions/bindings-sqs-0.2.0-channel/definitions/queue"}, "deadLetterQueue": {"description": "A definition of the queue that will be used for un-processable messages.", "$ref": "#/definitions/bindings-sqs-0.2.0-channel/definitions/queue"}, "bindingVersion": {"type": "string", "enum": ["0.1.0", "0.2.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed.", "default": "latest"}}, "required": ["queue"], "definitions": {"queue": {"type": "object", "description": "A definition of a queue.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"name": {"type": "string", "description": "The name of the queue. When an SNS Operation Binding Object references an SQS queue by name, the identifier should be the one in this field."}, "fifoQueue": {"type": "boolean", "description": "Is this a FIFO queue?", "default": false}, "deduplicationScope": {"type": "string", "enum": ["queue", "messageGroup"], "description": "Specifies whether message deduplication occurs at the message group or queue level. Valid values are messageGroup and queue (default).", "default": "queue"}, "fifoThroughputLimit": {"type": "string", "enum": ["perQueue", "perMessageGroupId"], "description": "Specifies whether the FIFO queue throughput quota applies to the entire queue or per message group. Valid values are perQueue (default) and perMessageGroupId.", "default": "perQueue"}, "deliveryDelay": {"type": "integer", "description": "The number of seconds to delay before a message sent to the queue can be received. used to create a delay queue.", "minimum": 0, "maximum": 15, "default": 0}, "visibilityTimeout": {"type": "integer", "description": "The length of time, in seconds, that a consumer locks a message - hiding it from reads - before it is unlocked and can be read again.", "minimum": 0, "maximum": 43200, "default": 30}, "receiveMessageWaitTime": {"type": "integer", "description": "Determines if the queue uses short polling or long polling. Set to zero the queue reads available messages and returns immediately. Set to a non-zero integer, long polling waits the specified number of seconds for messages to arrive before returning.", "default": 0}, "messageRetentionPeriod": {"type": "integer", "description": "How long to retain a message on the queue in seconds, unless deleted.", "minimum": 60, "maximum": 1209600, "default": 345600}, "redrivePolicy": {"$ref": "#/definitions/bindings-sqs-0.2.0-channel/definitions/redrivePolicy"}, "policy": {"$ref": "#/definitions/bindings-sqs-0.2.0-channel/definitions/policy"}, "tags": {"type": "object", "description": "Key-value pairs that represent AWS tags on the queue."}}, "required": ["name", "fifoQueue"]}, "redrivePolicy": {"type": "object", "description": "Prevent poison pill messages by moving un-processable messages to an SQS dead letter queue.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"deadLetterQueue": {"$ref": "#/definitions/bindings-sqs-0.2.0-channel/definitions/identifier"}, "maxReceiveCount": {"type": "integer", "description": "The number of times a message is delivered to the source queue before being moved to the dead-letter queue.", "default": 10}}, "required": ["deadLetter<PERSON><PERSON><PERSON>"]}, "identifier": {"type": "object", "description": "The SQS queue to use as a dead letter queue (DLQ).", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"arn": {"type": "string", "description": "The target is an ARN. For example, for SQS, the identifier may be an ARN, which will be of the form: arn:aws:sqs:{region}:{account-id}:{queueName}"}, "name": {"type": "string", "description": "The endpoint is identified by a name, which corresponds to an identifying field called 'name' of a binding for that protocol on this publish Operation Object. For example, if the protocol is 'sqs' then the name refers to the name field sqs binding."}}}, "policy": {"type": "object", "description": "The security policy for the SQS Queue", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"statements": {"type": "array", "description": "An array of statement objects, each of which controls a permission for this queue.", "items": {"$ref": "#/definitions/bindings-sqs-0.2.0-channel/definitions/statement"}}}, "required": ["statements"]}, "statement": {"type": "object", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"effect": {"type": "string", "enum": ["Allow", "<PERSON><PERSON>"]}, "principal": {"description": "The AWS account or resource ARN that this statement applies to.", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "action": {"description": "The SQS permission being allowed or denied e.g. sqs:ReceiveMessage", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["effect", "principal", "action"]}}, "examples": [{"queue": {"name": "myQueue", "fifoQueue": true, "deduplicationScope": "messageGroup", "fifoThroughputLimit": "perMessageGroupId", "deliveryDelay": 15, "visibilityTimeout": 60, "receiveMessageWaitTime": 0, "messageRetentionPeriod": 86400, "redrivePolicy": {"deadLetterQueue": {"arn": "arn:aws:SQS:eu-west-1:0000000:*********"}, "maxReceiveCount": 15}, "policy": {"statements": [{"effect": "<PERSON><PERSON>", "principal": "arn:aws:iam::************:user/dec.kolakowski", "action": ["sqs:SendMessage", "sqs:ReceiveMessage"]}]}, "tags": {"owner": "AsyncAPI.NET", "platform": "AsyncAPIOrg"}}, "deadLetterQueue": {"name": "myQueue_error", "deliveryDelay": 0, "visibilityTimeout": 0, "receiveMessageWaitTime": 0, "messageRetentionPeriod": 604800}}]}, "bindings-ibmmq-0.1.0-channel": {"title": "IBM MQ channel bindings object", "description": "This object contains information about the channel representation in IBM MQ. Each channel corresponds to a Queue or Topic within IBM MQ.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"destinationType": {"type": "string", "enum": ["topic", "queue"], "default": "topic", "description": "Defines the type of AsyncAPI channel."}, "queue": {"type": "object", "description": "Defines the properties of a queue.", "properties": {"objectName": {"type": "string", "maxLength": 48, "description": "Defines the name of the IBM MQ queue associated with the channel."}, "isPartitioned": {"type": "boolean", "default": false, "description": "Defines if the queue is a cluster queue and therefore partitioned. If 'true', a binding option MAY be specified when accessing the queue. More information on binding options can be found on this page in the IBM MQ Knowledge Center."}, "exclusive": {"type": "boolean", "default": false, "description": "Specifies if it is recommended to open the queue exclusively."}}, "required": ["objectName"]}, "topic": {"type": "object", "description": "Defines the properties of a topic.", "properties": {"string": {"type": "string", "maxLength": 10240, "description": "The value of the IBM MQ topic string to be used."}, "objectName": {"type": "string", "maxLength": 48, "description": "The name of the IBM MQ topic object."}, "durablePermitted": {"type": "boolean", "default": true, "description": "Defines if the subscription may be durable."}, "lastMsgRetained": {"type": "boolean", "default": false, "description": "Defines if the last message published will be made available to new subscriptions."}}}, "maxMsgLength": {"type": "integer", "minimum": 0, "maximum": 104857600, "description": "The maximum length of the physical message (in bytes) accepted by the Topic or Queue. Messages produced that are greater in size than this value may fail to be delivered. More information on the maximum message length can be found on this [page](https://www.ibm.com/support/knowledgecenter/SSFKSJ_latest/com.ibm.mq.ref.dev.doc/q097520_.html) in the IBM MQ Knowledge Center."}, "bindingVersion": {"type": "string", "enum": ["0.1.0"], "description": "The version of this binding."}}, "oneOf": [{"properties": {"destinationType": {"const": "topic"}}, "not": {"required": ["queue"]}}, {"properties": {"destinationType": {"const": "queue"}}, "required": ["queue"], "not": {"required": ["topic"]}}], "examples": [{"destinationType": "topic", "topic": {"objectName": "myTopicName"}, "bindingVersion": "0.1.0"}, {"destinationType": "queue", "queue": {"objectName": "myQueueName", "exclusive": true}, "bindingVersion": "0.1.0"}]}, "bindings-googlepubsub-0.2.0-channel": {"title": "Cloud Pub/Sub Channel Schema", "description": "This object contains information about the channel representation for Google Cloud Pub/Sub.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding."}, "labels": {"type": "object"}, "messageRetentionDuration": {"type": "string"}, "messageStoragePolicy": {"type": "object", "additionalProperties": false, "properties": {"allowedPersistenceRegions": {"type": "array", "items": {"type": "string"}}}}, "schemaSettings": {"type": "object", "additionalItems": false, "properties": {"encoding": {"type": "string"}, "firstRevisionId": {"type": "string"}, "lastRevisionId": {"type": "string"}, "name": {"type": "string"}}, "required": ["encoding", "name"]}}, "required": ["schemaSettings"], "examples": [{"labels": {"label1": "value1", "label2": "value2"}, "messageRetentionDuration": "86400s", "messageStoragePolicy": {"allowedPersistenceRegions": ["us-central1", "us-east1"]}, "schemaSettings": {"encoding": "json", "name": "projects/your-project-id/schemas/your-schema"}}]}, "bindings-pulsar-0.1.0-channel": {"title": "Channel Schema", "description": "This object contains information about the channel representation in Pulsar, which covers namespace and topic level admin configuration. This object contains additional information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "required": ["namespace", "persistence"], "properties": {"namespace": {"type": "string", "description": "The namespace, the channel is associated with."}, "persistence": {"type": "string", "enum": ["persistent", "non-persistent"], "description": "persistence of the topic in Pulsar."}, "compaction": {"type": "integer", "minimum": 0, "description": "Topic compaction threshold given in MB"}, "geo-replication": {"type": "array", "description": "A list of clusters the topic is replicated to.", "items": {"type": "string"}}, "retention": {"type": "object", "additionalProperties": false, "properties": {"time": {"type": "integer", "minimum": 0, "description": "Time given in Minutes. `0` = Disable message retention."}, "size": {"type": "integer", "minimum": 0, "description": "Size given in MegaBytes. `0` = Disable message retention."}}}, "ttl": {"type": "integer", "description": "TTL in seconds for the specified topic"}, "deduplication": {"type": "boolean", "description": "Whether deduplication of events is enabled or not."}, "bindingVersion": {"type": "string", "enum": ["0.1.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"namespace": "ns1", "persistence": "persistent", "compaction": 1000, "retention": {"time": 15, "size": 1000}, "ttl": 360, "geo-replication": ["us-west", "us-east"], "deduplication": true, "bindingVersion": "0.1.0"}]}, "operations": {"type": "object", "description": "Holds a dictionary with all the operations this application MUST implement.", "additionalProperties": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operation"}]}, "examples": [{"onUserSignUp": {"title": "User sign up", "summary": "Action to sign a user up.", "description": "A longer description", "channel": {"$ref": "#/channels/userSignup"}, "action": "send", "tags": [{"name": "user"}, {"name": "signup"}, {"name": "register"}], "bindings": {"amqp": {"ack": false}}, "traits": [{"$ref": "#/components/operationTraits/kafka"}]}}]}, "operation": {"type": "object", "description": "Describes a specific operation.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "required": ["action", "channel"], "properties": {"action": {"type": "string", "description": "Allowed values are send and receive. Use send when it's expected that the application will send a message to the given channel, and receive when the application should expect receiving messages from the given channel.", "enum": ["send", "receive"]}, "channel": {"$ref": "#/definitions/Reference"}, "messages": {"type": "array", "description": "A list of $ref pointers pointing to the supported Message Objects that can be processed by this operation. It MUST contain a subset of the messages defined in the channel referenced in this operation. Every message processed by this operation MUST be valid against one, and only one, of the message objects referenced in this list. Please note the messages property value MUST be a list of Reference Objects and, therefore, MUST NOT contain Message Objects. However, it is RECOMMENDED that parsers (or other software) dereference this property for a better development experience.", "items": {"$ref": "#/definitions/Reference"}}, "reply": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationReply"}]}, "traits": {"type": "array", "description": "A list of traits to apply to the operation object. Traits MUST be merged using traits merge mechanism. The resulting object MUST be a valid Operation Object.", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationTrait"}]}}, "title": {"type": "string", "description": "A human-friendly title for the operation."}, "summary": {"type": "string", "description": "A brief summary of the operation."}, "description": {"type": "string", "description": "A longer description of the operation. CommonMark is allowed."}, "security": {"$ref": "#/definitions/securityRequirements"}, "tags": {"type": "array", "description": "A list of tags for logical grouping and categorization of operations.", "items": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/tag"}]}, "uniqueItems": true}, "externalDocs": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}, "bindings": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationBindingsObject"}]}}, "examples": [{"title": "User sign up", "summary": "Action to sign a user up.", "description": "A longer description", "channel": {"$ref": "#/channels/userSignup"}, "action": "send", "security": [{"petstore_auth": ["write:pets", "read:pets"]}], "tags": [{"name": "user"}, {"name": "signup"}, {"name": "register"}], "bindings": {"amqp": {"ack": false}}, "traits": [{"$ref": "#/components/operationTraits/kafka"}], "messages": [{"$ref": "/components/messages/userSignedUp"}], "reply": {"address": {"location": "$message.header#/replyTo"}, "channel": {"$ref": "#/channels/userSignupReply"}, "messages": [{"$ref": "/components/messages/userSignedUpReply"}]}}]}, "operationReply": {"type": "object", "description": "Describes the reply part that MAY be applied to an Operation Object. If an operation implements the request/reply pattern, the reply object represents the response message.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"address": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationReplyAddress"}]}, "channel": {"$ref": "#/definitions/Reference"}, "messages": {"type": "array", "description": "A list of $ref pointers pointing to the supported Message Objects that can be processed by this operation as reply. It MUST contain a subset of the messages defined in the channel referenced in this operation reply. Every message processed by this operation MUST be valid against one, and only one, of the message objects referenced in this list. Please note the messages property value MUST be a list of Reference Objects and, therefore, MUST NOT contain Message Objects. However, it is RECOMMENDED that parsers (or other software) dereference this property for a better development experience.", "items": {"$ref": "#/definitions/Reference"}}}}, "operationReplyAddress": {"type": "object", "description": "An object that specifies where an operation has to send the reply", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "required": ["location"], "properties": {"location": {"type": "string", "description": "A runtime expression that specifies the location of the reply address.", "pattern": "^\\$message\\.(header|payload)#(\\/(([^\\/~])|(~[01]))*)*"}, "description": {"type": "string", "description": "An optional description of the address. CommonMark is allowed."}}, "examples": [{"description": "Consumer inbox", "location": "$message.header#/replyTo"}]}, "operationTrait": {"type": "object", "description": "Describes a trait that MAY be applied to an Operation Object. This object MAY contain any property from the Operation Object, except the action, channel and traits ones.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"title": {"description": "A human-friendly title for the operation.", "$ref": "#/definitions/operation/properties/title"}, "summary": {"description": "A short summary of what the operation is about.", "$ref": "#/definitions/operation/properties/summary"}, "description": {"description": "A verbose explanation of the operation. CommonMark syntax can be used for rich text representation.", "$ref": "#/definitions/operation/properties/description"}, "security": {"description": "A declaration of which security schemes are associated with this operation. Only one of the security scheme objects MUST be satisfied to authorize an operation. In cases where Server Security also applies, it MUST also be satisfied.", "$ref": "#/definitions/operation/properties/security"}, "tags": {"description": "A list of tags for logical grouping and categorization of operations.", "$ref": "#/definitions/operation/properties/tags"}, "externalDocs": {"description": "Additional external documentation for this operation.", "$ref": "#/definitions/operation/properties/externalDocs"}, "bindings": {"description": "A map where the keys describe the name of the protocol and the values describe protocol-specific definitions for the operation.", "oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationBindingsObject"}]}}, "examples": [{"bindings": {"amqp": {"ack": false}}}]}, "operationBindingsObject": {"type": "object", "description": "Map describing protocol-specific definitions for an operation.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"http": {"properties": {"bindingVersion": {"enum": ["0.2.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-http-0.3.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-http-0.2.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-http-0.3.0-operation"}}]}, "ws": {}, "amqp": {"properties": {"bindingVersion": {"enum": ["0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-amqp-0.3.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-amqp-0.3.0-operation"}}]}, "amqp1": {}, "mqtt": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-mqtt-0.2.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-mqtt-0.2.0-operation"}}]}, "kafka": {"properties": {"bindingVersion": {"enum": ["0.5.0", "0.4.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-kafka-0.5.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.5.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.5.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.4.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-kafka-0.3.0-operation"}}]}, "anypointmq": {}, "nats": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-nats-0.1.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-nats-0.1.0-operation"}}]}, "jms": {}, "sns": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-sns-0.1.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "#/definitions/bindings-sns-0.1.0-operation"}}]}, "sqs": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-sqs-0.2.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-sqs-0.2.0-operation"}}]}, "stomp": {}, "redis": {}, "ibmmq": {}, "solace": {"properties": {"bindingVersion": {"enum": ["0.4.0", "0.3.0", "0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "#/definitions/bindings-solace-0.4.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "#/definitions/bindings-solace-0.4.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "#/definitions/bindings-solace-0.3.0-operation"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "#/definitions/bindings-solace-0.2.0-operation"}}]}, "googlepubsub": {}}}, "bindings-http-0.3.0-operation": {"title": "HTTP operation bindings object", "description": "This object contains information about the operation representation in HTTP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"method": {"type": "string", "enum": ["GET", "PUT", "POST", "PATCH", "DELETE", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "description": "When 'type' is 'request', this is the HTTP method, otherwise it MUST be ignored. Its value MUST be one of 'GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS', 'CONNECT', and 'TRACE'."}, "query": {"$ref": "#/definitions/schema", "description": "A Schema object containing the definitions for each query parameter. This schema MUST be of type 'object' and have a properties key."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"query": {"type": "object", "required": ["companyId"], "properties": {"companyId": {"type": "number", "minimum": 1, "description": "The Id of the company."}}, "additionalProperties": false}, "bindingVersion": "0.3.0"}, {"method": "GET", "query": {"type": "object", "required": ["companyId"], "properties": {"companyId": {"type": "number", "minimum": 1, "description": "The Id of the company."}}, "additionalProperties": false}, "bindingVersion": "0.3.0"}]}, "bindings-http-0.2.0-operation": {"title": "HTTP operation bindings object", "description": "This object contains information about the operation representation in HTTP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"method": {"type": "string", "enum": ["GET", "PUT", "POST", "PATCH", "DELETE", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "description": "When 'type' is 'request', this is the HTTP method, otherwise it MUST be ignored. Its value MUST be one of 'GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS', 'CONNECT', and 'TRACE'."}, "query": {"$ref": "#/definitions/schema", "description": "A Schema object containing the definitions for each query parameter. This schema MUST be of type 'object' and have a properties key."}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"query": {"type": "object", "required": ["companyId"], "properties": {"companyId": {"type": "number", "minimum": 1, "description": "The Id of the company."}}, "additionalProperties": false}, "bindingVersion": "0.2.0"}, {"method": "GET", "query": {"type": "object", "required": ["companyId"], "properties": {"companyId": {"type": "number", "minimum": 1, "description": "The Id of the company."}}, "additionalProperties": false}, "bindingVersion": "0.2.0"}]}, "bindings-amqp-0.3.0-operation": {"title": "AMQP operation bindings object", "description": "This object contains information about the operation representation in AMQP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"expiration": {"type": "integer", "minimum": 0, "description": "TTL (Time-To-Live) for the message. It MUST be greater than or equal to zero."}, "userId": {"type": "string", "description": "Identifies the user who has sent the message."}, "cc": {"type": "array", "items": {"type": "string"}, "description": "The routing keys the message should be routed to at the time of publishing."}, "priority": {"type": "integer", "description": "A priority for the message."}, "deliveryMode": {"type": "integer", "enum": [1, 2], "description": "Delivery mode of the message. Its value MUST be either 1 (transient) or 2 (persistent)."}, "mandatory": {"type": "boolean", "description": "Whether the message is mandatory or not."}, "bcc": {"type": "array", "items": {"type": "string"}, "description": "Like cc but consumers will not receive this information."}, "timestamp": {"type": "boolean", "description": "Whether the message should include a timestamp or not."}, "ack": {"type": "boolean", "description": "Whether the consumer should ack the message or not."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, \"latest\" MUST be assumed."}}, "examples": [{"expiration": 100000, "userId": "guest", "cc": ["user.logs"], "priority": 10, "deliveryMode": 2, "mandatory": false, "bcc": ["external.audit"], "timestamp": true, "ack": false, "bindingVersion": "0.3.0"}]}, "bindings-mqtt-0.2.0-operation": {"title": "MQTT operation bindings object", "description": "This object contains information about the operation representation in MQTT.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"qos": {"type": "integer", "enum": [0, 1, 2], "description": "Defines the Quality of Service (QoS) levels for the message flow between client and server. Its value MUST be either 0 (At most once delivery), 1 (At least once delivery), or 2 (Exactly once delivery)."}, "retain": {"type": "boolean", "description": "Whether the broker should retain the message or not."}, "messageExpiryInterval": {"oneOf": [{"type": "integer", "minimum": 0, "maximum": 4294967295}, {"$ref": "#/definitions/schema"}, {"$ref": "#/definitions/Reference"}], "description": "Lifetime of the message in seconds"}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"qos": 2, "retain": true, "messageExpiryInterval": 60, "bindingVersion": "0.2.0"}]}, "bindings-kafka-0.5.0-operation": {"title": "Operation Schema", "description": "This object contains information about the operation representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"groupId": {"$ref": "#/definitions/schema", "description": "Id of the consumer group."}, "clientId": {"$ref": "#/definitions/schema", "description": "Id of the consumer inside a consumer group."}, "bindingVersion": {"type": "string", "enum": ["0.5.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"groupId": {"type": "string", "enum": ["myGroupId"]}, "clientId": {"type": "string", "enum": ["myClientId"]}, "bindingVersion": "0.5.0"}]}, "bindings-kafka-0.4.0-operation": {"title": "Operation Schema", "description": "This object contains information about the operation representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"groupId": {"$ref": "#/definitions/schema", "description": "Id of the consumer group."}, "clientId": {"$ref": "#/definitions/schema", "description": "Id of the consumer inside a consumer group."}, "bindingVersion": {"type": "string", "enum": ["0.4.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"groupId": {"type": "string", "enum": ["myGroupId"]}, "clientId": {"type": "string", "enum": ["myClientId"]}, "bindingVersion": "0.4.0"}]}, "bindings-kafka-0.3.0-operation": {"title": "Operation Schema", "description": "This object contains information about the operation representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"groupId": {"$ref": "#/definitions/schema", "description": "Id of the consumer group."}, "clientId": {"$ref": "#/definitions/schema", "description": "Id of the consumer inside a consumer group."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"groupId": {"type": "string", "enum": ["myGroupId"]}, "clientId": {"type": "string", "enum": ["myClientId"]}, "bindingVersion": "0.3.0"}]}, "bindings-nats-0.1.0-operation": {"title": "NATS operation bindings object", "description": "This object contains information about the operation representation in NATS.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"queue": {"type": "string", "description": "Defines the name of the queue to use. It MUST NOT exceed 255 characters.", "maxLength": 255}, "bindingVersion": {"type": "string", "enum": ["0.1.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"queue": "MyCustomQueue", "bindingVersion": "0.1.0"}]}, "bindings-sns-0.1.0-operation": {"title": "Operation Schema", "description": "This object contains information about the operation representation in SNS.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"topic": {"$ref": "#/definitions/bindings-sns-0.1.0-operation/definitions/identifier", "description": "Often we can assume that the SNS Topic is the channel name-we provide this field in case the you need to supply the ARN, or the Topic name is not the channel name in the AsyncAPI document."}, "consumers": {"type": "array", "description": "The protocols that listen to this topic and their endpoints.", "items": {"$ref": "#/definitions/bindings-sns-0.1.0-operation/definitions/consumer"}, "minItems": 1}, "deliveryPolicy": {"$ref": "#/definitions/bindings-sns-0.1.0-operation/definitions/deliveryPolicy", "description": "Policy for retries to HTTP. The field is the default for HTTP receivers of the SNS Topic which may be overridden by a specific consumer."}, "bindingVersion": {"type": "string", "description": "The version of this binding.", "default": "latest"}}, "required": ["consumers"], "definitions": {"identifier": {"type": "object", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"url": {"type": "string", "description": "The endpoint is a URL."}, "email": {"type": "string", "description": "The endpoint is an email address."}, "phone": {"type": "string", "description": "The endpoint is a phone number."}, "arn": {"type": "string", "description": "The target is an ARN. For example, for SQS, the identifier may be an ARN, which will be of the form: arn:aws:sqs:{region}:{account-id}:{queueName}"}, "name": {"type": "string", "description": "The endpoint is identified by a name, which corresponds to an identifying field called 'name' of a binding for that protocol on this publish Operation Object. For example, if the protocol is 'sqs' then the name refers to the name field sqs binding. We don't use $ref because we are referring, not including."}}}, "consumer": {"type": "object", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"protocol": {"description": "The protocol that this endpoint receives messages by.", "type": "string", "enum": ["http", "https", "email", "email-json", "sms", "sqs", "application", "lambda", "firehose"]}, "endpoint": {"description": "The endpoint messages are delivered to.", "$ref": "#/definitions/bindings-sns-0.1.0-operation/definitions/identifier"}, "filterPolicy": {"type": "object", "description": "Only receive a subset of messages from the channel, determined by this policy. Depending on the FilterPolicyScope, a map of either a message attribute or message body to an array of possible matches. The match may be a simple string for an exact match, but it may also be an object that represents a constraint and values for that constraint.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "additionalProperties": {"oneOf": [{"type": "array", "items": {"type": "string"}}, {"type": "string"}, {"type": "object"}]}}, "filterPolicyScope": {"type": "string", "description": "Determines whether the FilterPolicy applies to MessageAttributes or MessageBody.", "enum": ["MessageAttributes", "MessageBody"], "default": "MessageAttributes"}, "rawMessageDelivery": {"type": "boolean", "description": "If true AWS SNS attributes are removed from the body, and for SQS, SNS message attributes are copied to SQS message attributes. If false the SNS attributes are included in the body."}, "redrivePolicy": {"$ref": "#/definitions/bindings-sns-0.1.0-operation/definitions/redrivePolicy"}, "deliveryPolicy": {"$ref": "#/definitions/bindings-sns-0.1.0-operation/definitions/deliveryPolicy", "description": "Policy for retries to HTTP. The parameter is for that SNS Subscription and overrides any policy on the SNS Topic."}, "displayName": {"type": "string", "description": "The display name to use with an SNS subscription"}}, "required": ["protocol", "endpoint", "rawMessageDelivery"]}, "deliveryPolicy": {"type": "object", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"minDelayTarget": {"type": "integer", "description": "The minimum delay for a retry in seconds."}, "maxDelayTarget": {"type": "integer", "description": "The maximum delay for a retry in seconds."}, "numRetries": {"type": "integer", "description": "The total number of retries, including immediate, pre-backoff, backoff, and post-backoff retries."}, "numNoDelayRetries": {"type": "integer", "description": "The number of immediate retries (with no delay)."}, "numMinDelayRetries": {"type": "integer", "description": "The number of immediate retries (with delay)."}, "numMaxDelayRetries": {"type": "integer", "description": "The number of post-backoff phase retries, with the maximum delay between retries."}, "backoffFunction": {"type": "string", "description": "The algorithm for backoff between retries.", "enum": ["arithmetic", "exponential", "geometric", "linear"]}, "maxReceivesPerSecond": {"type": "integer", "description": "The maximum number of deliveries per second, per subscription."}}}, "redrivePolicy": {"type": "object", "description": "Prevent poison pill messages by moving un-processable messages to an SQS dead letter queue.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"deadLetterQueue": {"$ref": "#/definitions/bindings-sns-0.1.0-operation/definitions/identifier", "description": "The SQS queue to use as a dead letter queue (DLQ)."}, "maxReceiveCount": {"type": "integer", "description": "The number of times a message is delivered to the source queue before being moved to the dead-letter queue.", "default": 10}}, "required": ["deadLetter<PERSON><PERSON><PERSON>"]}}, "examples": [{"topic": {"name": "someTopic"}, "consumers": [{"protocol": "sqs", "endpoint": {"name": "someQueue"}, "filterPolicy": {"store": ["asyncapi_corp"], "event": [{"anything-but": "order_cancelled"}], "customer_interests": ["rugby", "football", "baseball"]}, "filterPolicyScope": "MessageAttributes", "rawMessageDelivery": false, "redrivePolicy": {"deadLetterQueue": {"arn": "arn:aws:SQS:eu-west-1:0000000:*********"}, "maxReceiveCount": 25}, "deliveryPolicy": {"minDelayTarget": 10, "maxDelayTarget": 100, "numRetries": 5, "numNoDelayRetries": 2, "numMinDelayRetries": 3, "numMaxDelayRetries": 5, "backoffFunction": "linear", "maxReceivesPerSecond": 2}}]}]}, "bindings-sqs-0.2.0-operation": {"title": "Operation Schema", "description": "This object contains information about the operation representation in SQS.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"queues": {"type": "array", "description": "Queue objects that are either the endpoint for an SNS Operation Binding Object, or the deadLetterQueue of the SQS Operation Binding Object.", "items": {"$ref": "#/definitions/bindings-sqs-0.2.0-operation/definitions/queue"}}, "bindingVersion": {"type": "string", "enum": ["0.1.0", "0.2.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed.", "default": "latest"}}, "required": ["queues"], "definitions": {"queue": {"type": "object", "description": "A definition of a queue.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"$ref": {"type": "string", "description": "Allows for an external definition of a queue. The referenced structure MUST be in the format of a Queue. If there are conflicts between the referenced definition and this Queue's definition, the behavior is undefined."}, "name": {"type": "string", "description": "The name of the queue. When an SNS Operation Binding Object references an SQS queue by name, the identifier should be the one in this field."}, "fifoQueue": {"type": "boolean", "description": "Is this a FIFO queue?", "default": false}, "deduplicationScope": {"type": "string", "enum": ["queue", "messageGroup"], "description": "Specifies whether message deduplication occurs at the message group or queue level. Valid values are messageGroup and queue (default).", "default": "queue"}, "fifoThroughputLimit": {"type": "string", "enum": ["perQueue", "perMessageGroupId"], "description": "Specifies whether the FIFO queue throughput quota applies to the entire queue or per message group. Valid values are perQueue (default) and perMessageGroupId.", "default": "perQueue"}, "deliveryDelay": {"type": "integer", "description": "The number of seconds to delay before a message sent to the queue can be received. Used to create a delay queue.", "minimum": 0, "maximum": 15, "default": 0}, "visibilityTimeout": {"type": "integer", "description": "The length of time, in seconds, that a consumer locks a message - hiding it from reads - before it is unlocked and can be read again.", "minimum": 0, "maximum": 43200, "default": 30}, "receiveMessageWaitTime": {"type": "integer", "description": "Determines if the queue uses short polling or long polling. Set to zero the queue reads available messages and returns immediately. Set to a non-zero integer, long polling waits the specified number of seconds for messages to arrive before returning.", "default": 0}, "messageRetentionPeriod": {"type": "integer", "description": "How long to retain a message on the queue in seconds, unless deleted.", "minimum": 60, "maximum": 1209600, "default": 345600}, "redrivePolicy": {"$ref": "#/definitions/bindings-sqs-0.2.0-operation/definitions/redrivePolicy"}, "policy": {"$ref": "#/definitions/bindings-sqs-0.2.0-operation/definitions/policy"}, "tags": {"type": "object", "description": "Key-value pairs that represent AWS tags on the queue."}}, "required": ["name"]}, "redrivePolicy": {"type": "object", "description": "Prevent poison pill messages by moving un-processable messages to an SQS dead letter queue.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"deadLetterQueue": {"$ref": "#/definitions/bindings-sqs-0.2.0-operation/definitions/identifier"}, "maxReceiveCount": {"type": "integer", "description": "The number of times a message is delivered to the source queue before being moved to the dead-letter queue.", "default": 10}}, "required": ["deadLetter<PERSON><PERSON><PERSON>"]}, "identifier": {"type": "object", "description": "The SQS queue to use as a dead letter queue (DLQ).", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"arn": {"type": "string", "description": "The target is an ARN. For example, for SQS, the identifier may be an ARN, which will be of the form: arn:aws:sqs:{region}:{account-id}:{queueName}"}, "name": {"type": "string", "description": "The endpoint is identified by a name, which corresponds to an identifying field called 'name' of a binding for that protocol on this publish Operation Object. For example, if the protocol is 'sqs' then the name refers to the name field sqs binding."}}}, "policy": {"type": "object", "description": "The security policy for the SQS Queue", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"statements": {"type": "array", "description": "An array of statement objects, each of which controls a permission for this queue.", "items": {"$ref": "#/definitions/bindings-sqs-0.2.0-operation/definitions/statement"}}}, "required": ["statements"]}, "statement": {"type": "object", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"effect": {"type": "string", "enum": ["Allow", "<PERSON><PERSON>"]}, "principal": {"description": "The AWS account or resource ARN that this statement applies to.", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "action": {"description": "The SQS permission being allowed or denied e.g. sqs:ReceiveMessage", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["effect", "principal", "action"]}}, "examples": [{"queues": [{"name": "myQueue", "fifoQueue": true, "deduplicationScope": "messageGroup", "fifoThroughputLimit": "perMessageGroupId", "deliveryDelay": 10, "redrivePolicy": {"deadLetterQueue": {"name": "myQueue_error"}, "maxReceiveCount": 15}, "policy": {"statements": [{"effect": "<PERSON><PERSON>", "principal": "arn:aws:iam::************:user/dec.kolakowski", "action": ["sqs:SendMessage", "sqs:ReceiveMessage"]}]}}, {"name": "myQueue_error", "deliveryDelay": 10}]}]}, "bindings-solace-0.4.0-operation": {"title": "Solace operation bindings object", "description": "This object contains information about the operation representation in Solace.", "type": "object", "additionalProperties": false, "properties": {"bindingVersion": {"type": "string", "enum": ["0.4.0"], "description": "The version of this binding. If omitted, \"latest\" MUST be assumed."}, "destinations": {"description": "The list of Solace destinations referenced in the operation.", "type": "array", "items": {"type": "object", "properties": {"deliveryMode": {"type": "string", "enum": ["direct", "persistent"]}}, "oneOf": [{"properties": {"destinationType": {"type": "string", "const": "queue", "description": "If the type is queue, then the subscriber can bind to the queue. The queue subscribes to the given topicSubscriptions. If no topicSubscriptions are provied, the queue will subscribe to the topic as represented by the channel name."}, "queue": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the queue"}, "topicSubscriptions": {"type": "array", "description": "The list of topics that the queue subscribes to.", "items": {"type": "string"}}, "accessType": {"type": "string", "enum": ["exclusive", "nonexclusive"]}, "maxTtl": {"type": "string", "description": "The maximum TTL to apply to messages to be spooled."}, "maxMsgSpoolUsage": {"type": "string", "description": "The maximum amount of message spool that the given queue may use"}}}}}, {"properties": {"destinationType": {"type": "string", "const": "topic", "description": "If the type is topic, then the subscriber subscribes to the given topicSubscriptions. If no topicSubscriptions are provided, the client will subscribe to the topic as represented by the channel name."}, "topicSubscriptions": {"type": "array", "description": "The list of topics that the client subscribes to.", "items": {"type": "string"}}}}]}}, "timeToLive": {"type": "integer", "description": "Interval in milliseconds or a Schema Object containing the definition of the lifetime of the message."}, "priority": {"type": "integer", "minimum": 0, "maximum": 255, "description": "The valid priority value range is 0-255 with 0 as the lowest priority and 255 as the highest or a Schema Object containing the definition of the priority."}, "dmqEligible": {"type": "boolean", "description": "Set the message to be eligible to be moved to a Dead Message Queue. The default value is false."}}, "examples": [{"bindingVersion": "0.4.0", "destinations": [{"destinationType": "queue", "queue": {"name": "sampleQueue", "topicSubscriptions": ["samples/*"], "accessType": "nonexclusive"}}, {"destinationType": "topic", "topicSubscriptions": ["samples/*"]}]}]}, "bindings-solace-0.3.0-operation": {"title": "Solace operation bindings object", "description": "This object contains information about the operation representation in Solace.", "type": "object", "additionalProperties": false, "properties": {"destinations": {"description": "The list of Solace destinations referenced in the operation.", "type": "array", "items": {"type": "object", "properties": {"deliveryMode": {"type": "string", "enum": ["direct", "persistent"]}}, "oneOf": [{"properties": {"destinationType": {"type": "string", "const": "queue", "description": "If the type is queue, then the subscriber can bind to the queue. The queue subscribes to the given topicSubscriptions. If no topicSubscriptions are provied, the queue will subscribe to the topic as represented by the channel name."}, "queue": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the queue"}, "topicSubscriptions": {"type": "array", "description": "The list of topics that the queue subscribes to.", "items": {"type": "string"}}, "accessType": {"type": "string", "enum": ["exclusive", "nonexclusive"]}, "maxTtl": {"type": "string", "description": "The maximum TTL to apply to messages to be spooled."}, "maxMsgSpoolUsage": {"type": "string", "description": "The maximum amount of message spool that the given queue may use"}}}}}, {"properties": {"destinationType": {"type": "string", "const": "topic", "description": "If the type is topic, then the subscriber subscribes to the given topicSubscriptions. If no topicSubscriptions are provided, the client will subscribe to the topic as represented by the channel name."}, "topicSubscriptions": {"type": "array", "description": "The list of topics that the client subscribes to.", "items": {"type": "string"}}}}]}}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, \"latest\" MUST be assumed."}}, "examples": [{"bindingVersion": "0.3.0", "destinations": [{"destinationType": "queue", "queue": {"name": "sampleQueue", "topicSubscriptions": ["samples/*"], "accessType": "nonexclusive"}}, {"destinationType": "topic", "topicSubscriptions": ["samples/*"]}]}]}, "bindings-solace-0.2.0-operation": {"title": "Solace operation bindings object", "description": "This object contains information about the operation representation in Solace.", "type": "object", "additionalProperties": false, "properties": {"destinations": {"description": "The list of Solace destinations referenced in the operation.", "type": "array", "items": {"type": "object", "properties": {"deliveryMode": {"type": "string", "enum": ["direct", "persistent"]}}, "oneOf": [{"properties": {"destinationType": {"type": "string", "const": "queue", "description": "If the type is queue, then the subscriber can bind to the queue. The queue subscribes to the given topicSubscriptions. If no topicSubscriptions are provied, the queue will subscribe to the topic as represented by the channel name."}, "queue": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the queue"}, "topicSubscriptions": {"type": "array", "description": "The list of topics that the queue subscribes to.", "items": {"type": "string"}}, "accessType": {"type": "string", "enum": ["exclusive", "nonexclusive"]}}}}}, {"properties": {"destinationType": {"type": "string", "const": "topic", "description": "If the type is topic, then the subscriber subscribes to the given topicSubscriptions. If no topicSubscriptions are provided, the client will subscribe to the topic as represented by the channel name."}, "topicSubscriptions": {"type": "array", "description": "The list of topics that the client subscribes to.", "items": {"type": "string"}}}}]}}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding. If omitted, \"latest\" MUST be assumed."}}, "examples": [{"bindingVersion": "0.2.0", "destinations": [{"destinationType": "queue", "queue": {"name": "sampleQueue", "topicSubscriptions": ["samples/*"], "accessType": "nonexclusive"}}, {"destinationType": "topic", "topicSubscriptions": ["samples/*"]}]}]}, "components": {"type": "object", "description": "An object to hold a set of reusable objects for different aspects of the AsyncAPI specification. All objects defined within the components object will have no effect on the API unless they are explicitly referenced from properties outside the components object.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "#/definitions/specificationExtension"}}, "properties": {"schemas": {"type": "object", "description": "An object to hold reusable Schema Object. If this is a Schema Object, then the schemaFormat will be assumed to be 'application/vnd.aai.asyncapi+json;version=asyncapi' where the version is equal to the AsyncAPI Version String.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"$ref": "#/definitions/anySchema"}}}, "servers": {"type": "object", "description": "An object to hold reusable Server Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/server"}]}}}, "channels": {"type": "object", "description": "An object to hold reusable Channel Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/channel"}]}}}, "serverVariables": {"type": "object", "description": "An object to hold reusable Server Variable Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/serverVariable"}]}}}, "operations": {"type": "object", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operation"}]}}}, "messages": {"type": "object", "description": "An object to hold reusable Message Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/messageObject"}]}}}, "securitySchemes": {"type": "object", "description": "An object to hold reusable Security Scheme Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/SecurityScheme"}]}}}, "parameters": {"type": "object", "description": "An object to hold reusable Parameter Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/parameter"}]}}}, "correlationIds": {"type": "object", "description": "An object to hold reusable Correlation ID Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/correlationId"}]}}}, "operationTraits": {"type": "object", "description": "An object to hold reusable Operation Trait Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationTrait"}]}}}, "messageTraits": {"type": "object", "description": "An object to hold reusable Message Trait Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/messageTrait"}]}}}, "replies": {"type": "object", "description": "An object to hold reusable Operation Reply Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationReply"}]}}}, "replyAddresses": {"type": "object", "description": "An object to hold reusable Operation Reply Address Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationReplyAddress"}]}}}, "serverBindings": {"type": "object", "description": "An object to hold reusable Server Bindings Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/serverBindingsObject"}]}}}, "channelBindings": {"type": "object", "description": "An object to hold reusable Channel Bindings Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/channelBindingsObject"}]}}}, "operationBindings": {"type": "object", "description": "An object to hold reusable Operation Bindings Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/operationBindingsObject"}]}}}, "messageBindings": {"type": "object", "description": "An object to hold reusable Message Bindings Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/messageBindingsObject"}]}}}, "tags": {"type": "object", "description": "An object to hold reusable Tag Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/tag"}]}}}, "externalDocs": {"type": "object", "description": "An object to hold reusable External Documentation Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "#/definitions/Reference"}, {"$ref": "#/definitions/externalDocs"}]}}}}, "examples": [{"components": {"schemas": {"Category": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "Tag": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "AvroExample": {"schemaFormat": "application/vnd.apache.avro+json;version=1.9.0", "schema": {"$ref": "path/to/user-create.avsc#/UserCreate"}}}, "servers": {"development": {"host": "{stage}.in.mycompany.com:{port}", "description": "RabbitMQ broker", "protocol": "amqp", "protocolVersion": "0-9-1", "variables": {"stage": {"$ref": "#/components/serverVariables/stage"}, "port": {"$ref": "#/components/serverVariables/port"}}}}, "serverVariables": {"stage": {"default": "demo", "description": "This value is assigned by the service provider, in this example `mycompany.com`"}, "port": {"enum": ["5671", "5672"], "default": "5672"}}, "channels": {"user/signedup": {"subscribe": {"message": {"$ref": "#/components/messages/userSignUp"}}}}, "messages": {"userSignUp": {"summary": "Action to sign a user up.", "description": "Multiline description of what this action does.\nHere you have another line.\n", "tags": [{"name": "user"}, {"name": "signup"}], "headers": {"type": "object", "properties": {"applicationInstanceId": {"description": "Unique identifier for a given instance of the publishing application", "type": "string"}}}, "payload": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/userCreate"}, "signup": {"$ref": "#/components/schemas/signup"}}}}}, "parameters": {"userId": {"description": "Id of the user."}}, "correlationIds": {"default": {"description": "Default Correlation ID", "location": "$message.header#/correlationId"}}, "messageTraits": {"commonHeaders": {"headers": {"type": "object", "properties": {"my-app-header": {"type": "integer", "minimum": 0, "maximum": 100}}}}}}}]}}, "description": "!!Auto generated!! \n Do not manually edit. "}