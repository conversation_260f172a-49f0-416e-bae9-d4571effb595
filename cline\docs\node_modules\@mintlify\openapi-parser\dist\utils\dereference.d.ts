import type { AnyApiDefinitionFormat, DereferenceResult, Filesystem, ThrowOnErrorOption } from '../types/index.js';
export type DereferenceOptions = ThrowOnErrorOption;
/**
 * Validates an OpenAPI schema and resolves all references.
 */
export declare function dereference(value: AnyApiDefinitionFormat | Filesystem, options?: DereferenceOptions): Promise<DereferenceResult>;
//# sourceMappingURL=dereference.d.ts.map