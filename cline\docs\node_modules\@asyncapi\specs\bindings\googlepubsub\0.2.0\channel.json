{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/googlepubsub/0.2.0/channel.json", "title": "Cloud Pub/Sub Channel Schema", "description": "This object contains information about the channel representation for Google Cloud Pub/Sub.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding."}, "labels": {"type": "object"}, "messageRetentionDuration": {"type": "string"}, "messageStoragePolicy": {"type": "object", "additionalProperties": false, "properties": {"allowedPersistenceRegions": {"type": "array", "items": {"type": "string"}}}}, "schemaSettings": {"type": "object", "additionalItems": false, "properties": {"encoding": {"type": "string"}, "firstRevisionId": {"type": "string"}, "lastRevisionId": {"type": "string"}, "name": {"type": "string"}}, "required": ["encoding", "name"]}}, "required": ["schemaSettings"], "examples": [{"labels": {"label1": "value1", "label2": "value2"}, "messageRetentionDuration": "86400s", "messageStoragePolicy": {"allowedPersistenceRegions": ["us-central1", "us-east1"]}, "schemaSettings": {"encoding": "json", "name": "projects/your-project-id/schemas/your-schema"}}]}