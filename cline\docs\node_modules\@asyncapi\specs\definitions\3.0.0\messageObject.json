{"type": "object", "description": "Describes a message received on a given channel and operation.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"contentType": {"type": "string", "description": "The content type to use when encoding/decoding a message's payload. The value MUST be a specific media type (e.g. application/json). When omitted, the value MUST be the one specified on the defaultContentType field."}, "headers": {"$ref": "http://asyncapi.com/definitions/3.0.0/anySchema.json"}, "payload": {"$ref": "http://asyncapi.com/definitions/3.0.0/anySchema.json"}, "correlationId": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/correlationId.json"}]}, "tags": {"type": "array", "items": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/tag.json"}]}, "uniqueItems": true}, "summary": {"type": "string", "description": "A brief summary of the message."}, "name": {"type": "string", "description": "Name of the message."}, "title": {"type": "string", "description": "A human-friendly title for the message."}, "description": {"type": "string", "description": "A longer description of the message. CommonMark is allowed."}, "externalDocs": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/externalDocs.json"}]}, "deprecated": {"type": "boolean", "default": false}, "examples": {"type": "array", "description": "List of examples.", "items": {"$ref": "http://asyncapi.com/definitions/3.0.0/messageExampleObject.json"}}, "bindings": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/messageBindingsObject.json"}]}, "traits": {"type": "array", "description": "A list of traits to apply to the message object. Traits MUST be merged using traits merge mechanism. The resulting object MUST be a valid Message Object.", "items": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/messageTrait.json"}, {"type": "array", "items": [{"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/messageTrait.json"}]}, {"type": "object", "additionalItems": true}]}]}}}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/messageObject.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/messageObject.json"}