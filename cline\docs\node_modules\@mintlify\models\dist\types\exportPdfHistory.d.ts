export type ExportPdfHistory = {
    _id: string;
    subdomain: string;
    createdBy?: {
        id: string;
        email: string;
    };
    status: 'queued' | 'in_progress' | 'success' | 'failure';
    options: ExportOptions;
    createdAt: number;
    endedAt?: number;
    errorMessage?: string;
    summary?: string;
    logs?: string[];
};
export declare const exportFormats: readonly ["A4", "Letter", "Legal"];
export type ExportFormat = (typeof exportFormats)[number];
export interface ExportOptions {
    format: ExportFormat;
    scalePercentage: number;
    includeFooter: boolean;
}
