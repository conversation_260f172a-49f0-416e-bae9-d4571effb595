export { AsyncAPIDocument as AsyncAPIDocumentV2 } from './asyncapi';
export { Binding as BindingV2 } from './binding';
export { Bindings as BindingsV2 } from './bindings';
export { ChannelParameter as ChannelParameterV2 } from './channel-parameter';
export { ChannelParameters as ChannelParametersV2 } from './channel-parameters';
export { Channel as ChannelV2 } from './channel';
export { Channels as ChannelsV2 } from './channels';
export { Components as ComponentsV2 } from './components';
export { Contact as ContactV2 } from './contact';
export { CorrelationId as CorrelationIdV2 } from './correlation-id';
export { Extension as ExtensionV2 } from './extension';
export { Extensions as ExtensionsV2 } from './extensions';
export { ExternalDocumentation as ExternalDocumentationV2 } from './external-docs';
export { Info as InfoV2 } from './info';
export { License as LicenseV2 } from './license';
export { MessageExample as MessageExampleV2 } from './message-example';
export { MessageExamples as MessageExamplesV2 } from './message-examples';
export { MessageTrait as MessageTraitV2 } from './message-trait';
export { MessageTraits as MessageTraitsV2 } from './message-traits';
export { Message as MessageV2 } from './message';
export { Messages as MessagesV2 } from './messages';
export { OAuthFlow as OAuthFlowV2 } from './oauth-flow';
export { OAuthFlows as OAuthFlowsV2 } from './oauth-flows';
export { OperationTrait as OperationTraitV2 } from './operation-trait';
export { OperationTraits as OperationTraitsV2 } from './operation-traits';
export { Operation as OperationV2 } from './operation';
export { Operations as OperationsV2 } from './operations';
export { Schema as SchemaV2 } from './schema';
export { Schemas as SchemasV2 } from './schemas';
export { SecurityScheme as SecuritySchemeV2 } from './security-scheme';
export { SecuritySchemes as SecuritySchemesV2 } from './security-schemes';
export { ServerVariable as ServerVariableV2 } from './server-variable';
export { ServerVariables as ServerVariablesV2 } from './server-variables';
export { Server as ServerV2 } from './server';
export { Servers as ServersV2 } from './servers';
export { Tag as TagV2 } from './tag';
export { Tags as TagsV2 } from './tags';
