import { BaseModel } from '../base';
import { Collection } from '../collection';
import type { BindingsInterface } from '../bindings';
import type { ComponentsInterface } from '../components';
import type { ExtensionsInterface } from '../extensions';
import type { Constructor } from '../utils';
import type { ServersInterface } from '../servers';
import type { ChannelsInterface } from '../channels';
import type { MessagesInterface } from '../messages';
import type { SchemasInterface } from '../schemas';
import type { ChannelParametersInterface } from '../channel-parameters';
import type { ServerVariablesInterface } from '../server-variables';
import type { OperationTraitsInterface } from '../operation-traits';
import type { SecuritySchemesInterface } from '../security-schemes';
import type { MessageTraitsInterface } from '../message-traits';
import type { OperationsInterface } from '../operations';
import type { CorrelationIdsInterface } from '../correlation-ids';
import type { v2 } from '../../spec-types';
export declare class Components extends BaseModel<v2.ComponentsObject> implements ComponentsInterface {
    servers(): ServersInterface;
    channels(): ChannelsInterface;
    operations(): OperationsInterface;
    messages(): MessagesInterface;
    schemas(): SchemasInterface;
    channelParameters(): ChannelParametersInterface;
    serverVariables(): ServerVariablesInterface;
    operationTraits(): OperationTraitsInterface;
    messageTraits(): MessageTraitsInterface;
    correlationIds(): CorrelationIdsInterface;
    securitySchemes(): SecuritySchemesInterface;
    serverBindings(): Record<string, BindingsInterface>;
    channelBindings(): Record<string, BindingsInterface>;
    operationBindings(): Record<string, BindingsInterface>;
    messageBindings(): Record<string, BindingsInterface>;
    extensions(): ExtensionsInterface;
    isEmpty(): boolean;
    protected createCollection<M extends Collection<any>, T extends BaseModel>(itemsName: keyof v2.ComponentsObject, collectionModel: Constructor<M>, itemModel: Constructor<T>): M;
    protected createBindings(itemsName: 'serverBindings' | 'channelBindings' | 'operationBindings' | 'messageBindings'): Record<string, BindingsInterface>;
}
