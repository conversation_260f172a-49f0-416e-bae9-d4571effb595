import { visit } from 'unist-util-visit';
export const remarkSplitCodeGroup = () => (tree) => {
    const codeGroupCompoenents = ['CodeGroup', 'RequestExample', 'ResponseExample'];
    const groupsToProcess = [];
    visit(tree, 'mdxJsxFlowElement', (node, index, parent) => {
        if (node.name && codeGroupCompoenents.includes(node.name) && index && parent) {
            groupsToProcess.push({ node, index, parent });
        }
    });
    // Split the code group into multiple CodeBlock nodes
    // basically remove the CodeGroup component and replace it with the codeblocks
    for (const { node, index, parent } of groupsToProcess) {
        const numberOfCodeBlocks = node.children.length;
        if (numberOfCodeBlocks <= 1)
            continue;
        const newNodes = node.children;
        parent.children.splice(index, 1, ...newNodes);
    }
};
