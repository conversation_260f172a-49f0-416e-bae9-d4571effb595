import { DecoratedNavigationPage } from '@mintlify/models';
import { DecoratedGroupConfig, DecoratedNavigationConfig, DecoratedPageConfig } from '@mintlify/validation';
export declare function getFirstPageFromNavigation(node: DecoratedNavigationConfig | DecoratedGroupConfig | DecoratedPageConfig | undefined, userGroups?: Set<string>, shouldCheckNavAccess?: boolean): DecoratedNavigationPage | undefined;
