import { SpecificationExtensionsModel } from './mixins';
import type { v2 } from '../spec-types';
export declare class Schema extends SpecificationExtensionsModel<v2.AsyncAPISchemaObject, {
    parent?: Schema;
}> {
    uid(): any;
    $id(): string | undefined;
    multipleOf(): number | undefined;
    maximum(): number | undefined;
    exclusiveMaximum(): number | undefined;
    minimum(): number | undefined;
    exclusiveMinimum(): number | undefined;
    maxLength(): number | undefined;
    minLength(): number | undefined;
    pattern(): string | undefined;
    maxItems(): number | undefined;
    minItems(): number | undefined;
    uniqueItems(): boolean | undefined;
    maxProperties(): number | undefined;
    minProperties(): number | undefined;
    required(): string[] | undefined;
    enum(): import("json-schema").JSONSchema7Type[] | undefined;
    type(): import("json-schema").JSONSchema7TypeName | import("json-schema").JSONSchema7TypeName[] | undefined;
    allOf(): Schema[] | null;
    oneOf(): Schema[] | null;
    anyOf(): Schema[] | null;
    not(): Schema | null;
    items(): Schema | Schema[] | null;
    properties(): Record<string, Schema>;
    property(name: string): Schema | null;
    additionalProperties(): boolean | Schema | undefined;
    additionalItems(): Schema | undefined;
    patternProperties(): Record<string, Schema>;
    const(): import("json-schema").JSONSchema7Type | undefined;
    contains(): Schema | null;
    dependencies(): Record<string, string[] | Schema> | null;
    propertyNames(): Schema | null;
    if(): Schema | null;
    then(): Schema | null;
    else(): Schema | null;
    format(): string | undefined;
    contentEncoding(): string | undefined;
    contentMediaType(): string | undefined;
    definitions(): Record<string, Schema>;
    title(): string | undefined;
    default(): import("json-schema").JSONSchema7Type | undefined;
    deprecated(): boolean | undefined;
    discriminator(): string | undefined;
    readOnly(): boolean | undefined;
    writeOnly(): boolean | undefined;
    examples(): import("json-schema").JSONSchema7Type[] | undefined;
    isBooleanSchema(): boolean;
    description(): string | null;
    hasDescription(): boolean;
    externalDocs(): import("./external-docs").ExternalDocs | null;
    hasExternalDocs(): boolean;
    isCircular(): boolean;
    circularSchema(): Schema | undefined;
    hasCircularProps(): boolean;
    circularProps(): any;
    protected __get<K extends keyof v2.AsyncAPISchemaDefinition>(key: K): v2.AsyncAPISchemaDefinition[K] | undefined;
    protected __createChild(s: v2.AsyncAPISchemaObject): Schema;
}
