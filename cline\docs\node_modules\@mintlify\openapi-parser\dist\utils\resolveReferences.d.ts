import type { <PERSON>API } from '@mintlify/openapi-types';
import type { AnyObject, ErrorObject, Filesystem, FilesystemEntry, ThrowOnErrorOption } from '../types/index.js';
export type ResolveReferencesResult = {
    valid: boolean;
    errors: ErrorObject[];
    schema: OpenAPI.Document;
};
/**
 * Takes a specification and resolves all references.
 */
export declare function resolveReferences(input: AnyObject | Filesystem, options?: ThrowOnErrorOption, file?: FilesystemEntry, errors?: ErrorObject[]): ResolveReferencesResult;
//# sourceMappingURL=resolveReferences.d.ts.map