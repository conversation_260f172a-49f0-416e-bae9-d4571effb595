var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { truncateCircularReferences } from './truncateCircularReferences.js';
import { validate } from './validate.js';
function openApiCheck(document_1) {
    return __awaiter(this, arguments, void 0, function* (document, truncate = false) {
        try {
            const { schema } = yield validate(document);
            // truncation can be costly, so only do it when necessary
            return truncate ? truncateCircularReferences(schema, 2) : schema;
        }
        catch (_a) {
            return undefined;
        }
    });
}
export { openApiCheck };
