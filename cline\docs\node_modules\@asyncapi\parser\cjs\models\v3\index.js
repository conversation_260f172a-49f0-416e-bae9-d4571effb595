"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagsV3 = exports.TagV3 = exports.ServersV3 = exports.ServerV3 = exports.ServerVariablesV3 = exports.ServerVariableV3 = exports.SecuritySchemesV3 = exports.SecuritySchemeV3 = exports.SchemasV3 = exports.SchemaV3 = exports.OperationsV3 = exports.OperationV3 = exports.OperationReplyV3 = exports.OperationReplyAddressesV3 = exports.OperationReplyAddressV3 = exports.OperationRepliesV3 = exports.OperationTraitsV3 = exports.OperationTraitV3 = exports.OAuthFlowsV3 = exports.OAuthFlowV3 = exports.MessagesV3 = exports.MessageV3 = exports.MessageTraitsV3 = exports.MessageTraitV3 = exports.MessageExamplesV3 = exports.MessageExampleV3 = exports.LicenseV3 = exports.InfoV3 = exports.ExternalDocumentationV3 = exports.ExtensionsV3 = exports.ExtensionV3 = exports.CorrelationIdV3 = exports.ContactV3 = exports.ComponentsV3 = exports.ChannelsV3 = exports.ChannelV3 = exports.ChannelParametersV3 = exports.ChannelParameterV3 = exports.BindingsV3 = exports.BindingV3 = exports.AsyncAPIDocumentV3 = void 0;
var asyncapi_1 = require("./asyncapi");
Object.defineProperty(exports, "AsyncAPIDocumentV3", { enumerable: true, get: function () { return asyncapi_1.AsyncAPIDocument; } });
var binding_1 = require("./binding");
Object.defineProperty(exports, "BindingV3", { enumerable: true, get: function () { return binding_1.Binding; } });
var bindings_1 = require("./bindings");
Object.defineProperty(exports, "BindingsV3", { enumerable: true, get: function () { return bindings_1.Bindings; } });
var channel_parameter_1 = require("./channel-parameter");
Object.defineProperty(exports, "ChannelParameterV3", { enumerable: true, get: function () { return channel_parameter_1.ChannelParameter; } });
var channel_parameters_1 = require("./channel-parameters");
Object.defineProperty(exports, "ChannelParametersV3", { enumerable: true, get: function () { return channel_parameters_1.ChannelParameters; } });
var channel_1 = require("./channel");
Object.defineProperty(exports, "ChannelV3", { enumerable: true, get: function () { return channel_1.Channel; } });
var channels_1 = require("./channels");
Object.defineProperty(exports, "ChannelsV3", { enumerable: true, get: function () { return channels_1.Channels; } });
var components_1 = require("./components");
Object.defineProperty(exports, "ComponentsV3", { enumerable: true, get: function () { return components_1.Components; } });
var contact_1 = require("./contact");
Object.defineProperty(exports, "ContactV3", { enumerable: true, get: function () { return contact_1.Contact; } });
var correlation_id_1 = require("./correlation-id");
Object.defineProperty(exports, "CorrelationIdV3", { enumerable: true, get: function () { return correlation_id_1.CorrelationId; } });
var extension_1 = require("./extension");
Object.defineProperty(exports, "ExtensionV3", { enumerable: true, get: function () { return extension_1.Extension; } });
var extensions_1 = require("./extensions");
Object.defineProperty(exports, "ExtensionsV3", { enumerable: true, get: function () { return extensions_1.Extensions; } });
var external_docs_1 = require("./external-docs");
Object.defineProperty(exports, "ExternalDocumentationV3", { enumerable: true, get: function () { return external_docs_1.ExternalDocumentation; } });
var info_1 = require("./info");
Object.defineProperty(exports, "InfoV3", { enumerable: true, get: function () { return info_1.Info; } });
var license_1 = require("./license");
Object.defineProperty(exports, "LicenseV3", { enumerable: true, get: function () { return license_1.License; } });
var message_example_1 = require("./message-example");
Object.defineProperty(exports, "MessageExampleV3", { enumerable: true, get: function () { return message_example_1.MessageExample; } });
var message_examples_1 = require("./message-examples");
Object.defineProperty(exports, "MessageExamplesV3", { enumerable: true, get: function () { return message_examples_1.MessageExamples; } });
var message_trait_1 = require("./message-trait");
Object.defineProperty(exports, "MessageTraitV3", { enumerable: true, get: function () { return message_trait_1.MessageTrait; } });
var message_traits_1 = require("./message-traits");
Object.defineProperty(exports, "MessageTraitsV3", { enumerable: true, get: function () { return message_traits_1.MessageTraits; } });
var message_1 = require("./message");
Object.defineProperty(exports, "MessageV3", { enumerable: true, get: function () { return message_1.Message; } });
var messages_1 = require("./messages");
Object.defineProperty(exports, "MessagesV3", { enumerable: true, get: function () { return messages_1.Messages; } });
var oauth_flow_1 = require("./oauth-flow");
Object.defineProperty(exports, "OAuthFlowV3", { enumerable: true, get: function () { return oauth_flow_1.OAuthFlow; } });
var oauth_flows_1 = require("./oauth-flows");
Object.defineProperty(exports, "OAuthFlowsV3", { enumerable: true, get: function () { return oauth_flows_1.OAuthFlows; } });
var operation_trait_1 = require("./operation-trait");
Object.defineProperty(exports, "OperationTraitV3", { enumerable: true, get: function () { return operation_trait_1.OperationTrait; } });
var operation_traits_1 = require("./operation-traits");
Object.defineProperty(exports, "OperationTraitsV3", { enumerable: true, get: function () { return operation_traits_1.OperationTraits; } });
var operation_replies_1 = require("./operation-replies");
Object.defineProperty(exports, "OperationRepliesV3", { enumerable: true, get: function () { return operation_replies_1.OperationReplies; } });
var operation_reply_address_1 = require("./operation-reply-address");
Object.defineProperty(exports, "OperationReplyAddressV3", { enumerable: true, get: function () { return operation_reply_address_1.OperationReplyAddress; } });
var operation_reply_addresses_1 = require("./operation-reply-addresses");
Object.defineProperty(exports, "OperationReplyAddressesV3", { enumerable: true, get: function () { return operation_reply_addresses_1.OperationReplyAddresses; } });
var operation_reply_1 = require("./operation-reply");
Object.defineProperty(exports, "OperationReplyV3", { enumerable: true, get: function () { return operation_reply_1.OperationReply; } });
var operation_1 = require("./operation");
Object.defineProperty(exports, "OperationV3", { enumerable: true, get: function () { return operation_1.Operation; } });
var operations_1 = require("./operations");
Object.defineProperty(exports, "OperationsV3", { enumerable: true, get: function () { return operations_1.Operations; } });
var schema_1 = require("./schema");
Object.defineProperty(exports, "SchemaV3", { enumerable: true, get: function () { return schema_1.Schema; } });
var schemas_1 = require("./schemas");
Object.defineProperty(exports, "SchemasV3", { enumerable: true, get: function () { return schemas_1.Schemas; } });
var security_scheme_1 = require("./security-scheme");
Object.defineProperty(exports, "SecuritySchemeV3", { enumerable: true, get: function () { return security_scheme_1.SecurityScheme; } });
var security_schemes_1 = require("./security-schemes");
Object.defineProperty(exports, "SecuritySchemesV3", { enumerable: true, get: function () { return security_schemes_1.SecuritySchemes; } });
var server_variable_1 = require("./server-variable");
Object.defineProperty(exports, "ServerVariableV3", { enumerable: true, get: function () { return server_variable_1.ServerVariable; } });
var server_variables_1 = require("./server-variables");
Object.defineProperty(exports, "ServerVariablesV3", { enumerable: true, get: function () { return server_variables_1.ServerVariables; } });
var server_1 = require("./server");
Object.defineProperty(exports, "ServerV3", { enumerable: true, get: function () { return server_1.Server; } });
var servers_1 = require("./servers");
Object.defineProperty(exports, "ServersV3", { enumerable: true, get: function () { return servers_1.Servers; } });
var tag_1 = require("./tag");
Object.defineProperty(exports, "TagV3", { enumerable: true, get: function () { return tag_1.Tag; } });
var tags_1 = require("./tags");
Object.defineProperty(exports, "TagsV3", { enumerable: true, get: function () { return tags_1.Tags; } });
