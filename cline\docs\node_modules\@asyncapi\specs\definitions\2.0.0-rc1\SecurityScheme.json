{"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/userPassword.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/apiKey.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/X509.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/symmetricEncryption.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/asymmetricEncryption.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/HTTPSecurityScheme.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/oauth2Flows.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/openIdConnect.json"}], "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc1/SecurityScheme.json"}