import { BaseConverter } from './BaseConverter.js';
import { generateFirstIncrementalSchema } from './IncrementalEvaluator.js';
import { SchemaConverter } from './SchemaConverter.js';
import { InvalidSchemaError } from './errors.js';
import { copyKeyIfDefined, dereference } from './utils.js';
const PARAM_NAMES = ['path', 'header', 'query', 'cookie'];
export class ParametersConverter extends BaseConverter {
    constructor(method, pathParameters, operationParameters, path, safeParse = false) {
        super(safeParse);
        this.method = method;
        this.pathParameters = pathParameters;
        this.operationParameters = operationParameters;
        this.path = path;
        this.safeParse = safeParse;
        this.parameterSections = {
            path: {},
            query: {},
            header: {},
            cookie: {},
        };
    }
    convert() {
        var _a, _b;
        (_a = this.pathParameters) === null || _a === void 0 ? void 0 : _a.forEach((parameterObject, i) => {
            this.addParameter([...this.path, 'parameters', i.toString()], parameterObject);
        });
        (_b = this.operationParameters) === null || _b === void 0 ? void 0 : _b.forEach((parameterObject, i) => {
            this.addParameter([...this.path, this.method, 'parameters', i.toString()], parameterObject);
        });
        return this.parameterSections;
    }
    addParameter(path, parameter) {
        if (!PARAM_NAMES.includes(parameter.in)) {
            this.handleNewError(InvalidSchemaError, path, `invalid parameter location: '${parameter.in}'`);
            return;
        }
        const location = parameter.in;
        // parameter.schema may be undefined (if the schema is instead defined in parameter.content), but this is likely super rare
        const parameterSchema = parameter.schema;
        copyKeyIfDefined('description', parameter, parameterSchema);
        copyKeyIfDefined('deprecated', parameter, parameterSchema);
        const newParameter = SchemaConverter.convert({
            schema: parameter.schema,
            path: [...path, 'schema'],
            required: location === 'path' ? true : parameter.required,
        });
        this.parameterSections[location][parameter.name] = { schema: newParameter };
    }
    static convert(method, pathParameters, operationParameters, path, safeParse) {
        return new ParametersConverter(method, pathParameters, operationParameters, path, safeParse).convert();
    }
}
export class IncrementalParametersConverter extends BaseConverter {
    constructor(rawDocument, method, pathParameters, operationParameters, path, safeParse = false) {
        super(safeParse);
        this.rawDocument = rawDocument;
        this.method = method;
        this.pathParameters = pathParameters;
        this.operationParameters = operationParameters;
        this.path = path;
        this.safeParse = safeParse;
        this.parameterSections = {
            path: {},
            query: {},
            header: {},
            cookie: {},
        };
    }
    convert() {
        var _a, _b;
        (_a = this.pathParameters) === null || _a === void 0 ? void 0 : _a.forEach((parameterObject, i) => {
            this.addParameter([...this.path, 'parameters', i.toString()], parameterObject);
        });
        (_b = this.operationParameters) === null || _b === void 0 ? void 0 : _b.forEach((parameterObject, i) => {
            this.addParameter([...this.path, this.method, 'parameters', i.toString()], parameterObject);
        });
        return this.parameterSections;
    }
    addParameter(path, parameter) {
        var _a, _b, _c;
        if ('$ref' in parameter) {
            const dereferencedParam = dereference('parameters', parameter.$ref, (_a = this.rawDocument.components) === null || _a === void 0 ? void 0 : _a.parameters);
            if (!dereferencedParam || '$ref' in dereferencedParam) {
                return;
            }
            parameter = dereferencedParam;
        }
        if (!PARAM_NAMES.includes(parameter.in)) {
            this.handleNewError(InvalidSchemaError, path, `invalid parameter location: '${parameter.in}'`);
            return;
        }
        const location = parameter.in;
        // parameter.schema may be undefined (if the schema is instead defined in parameter.content), but this is likely super rare
        let schema = parameter.schema;
        if (schema && '$ref' in schema) {
            schema = dereference('schemas', schema.$ref, (_b = this.rawDocument.components) === null || _b === void 0 ? void 0 : _b.schemas);
        }
        if (!schema) {
            return;
        }
        // maintain immutability
        const schemaCopy = Object.assign({}, schema);
        copyKeyIfDefined('description', parameter, schemaCopy);
        copyKeyIfDefined('deprecated', parameter, schemaCopy);
        const incrementalSchema = generateFirstIncrementalSchema(schemaCopy, (_c = this.rawDocument.components) === null || _c === void 0 ? void 0 : _c.schemas, location === 'path' ? true : parameter.required);
        this.parameterSections[location][parameter.name] = Object.assign(Object.assign({ schema: incrementalSchema }, (parameter.style !== undefined ? { style: parameter.style } : {})), (parameter.explode !== undefined ? { explode: parameter.explode } : {}));
    }
    static convert(rawDocument, method, pathParameters, operationParameters, path, safeParse) {
        return new IncrementalParametersConverter(rawDocument, method, pathParameters, operationParameters, path, safeParse).convert();
    }
}
