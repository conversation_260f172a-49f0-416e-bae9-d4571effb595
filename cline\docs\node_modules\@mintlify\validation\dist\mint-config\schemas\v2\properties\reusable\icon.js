import { iconTypes, iconLibraries } from '@mintlify/models';
import { z } from 'zod';
const iconNameSchema = z
    .string({
    invalid_type_error: 'Anchor icon must be the name of a Font Awesome or Lucide icon. Browse their libraries to see all the available icons: https://fontawesome.com/icons, https://lucide.dev/icons',
})
    .refine((icon) => !icon.startsWith('fa-'), "Icon does not need to start with 'fa-'. Please delete 'fa-' and keep the rest of the icon name");
export const iconSchema = z
    .union([
    iconNameSchema,
    z.object({
        style: z.enum(iconTypes).optional(),
        name: iconNameSchema,
        library: z.enum(iconLibraries).optional(),
    }),
])
    .describe('The icon to be displayed in the section');
