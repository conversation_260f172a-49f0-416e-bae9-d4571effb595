/**
 * When adding a new Resource/Action here, remember to add it
 * to Stytch as well!
 */
export declare const ACTIONS: readonly ["create", "read", "update", "delete", "upsert"];
export declare const ORG_RESOURCES: readonly ["org", "org.apiKey", "org.billing"];
export declare const USER_RESOURCES: readonly ["user", "user.role"];
export declare const DEPLOYMENT_RESOURCES: readonly ["deployment", "deployment.customDomain", "deployment.basePath", "deployment.gitSource", "deployment.auth", "deployment.personalization"];
export declare const ALL_RESOURCES: readonly ["org", "org.apiKey", "org.billing", "user", "user.role", "deployment", "deployment.customDomain", "deployment.basePath", "deployment.gitSource", "deployment.auth", "deployment.personalization"];
export type OrgResource = (typeof ORG_RESOURCES)[number];
export type UserResource = (typeof USER_RESOURCES)[number];
export type DeploymentResource = (typeof DEPLOYMENT_RESOURCES)[number];
export type Resource = (typeof ALL_RESOURCES)[number];
export type Action = (typeof ACTIONS)[number];
export type Permission = `${Resource}.${Action}`;
export declare const validResourceSet: Set<string>;
export declare const validActionSet: Set<string>;
export declare function isValidPermission(permission: string): permission is Permission;
