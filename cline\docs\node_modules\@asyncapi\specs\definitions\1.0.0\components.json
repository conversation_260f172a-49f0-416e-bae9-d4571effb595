{"type": "object", "description": "An object to hold a set of reusable objects for different aspects of the AsyncAPI Specification.", "additionalProperties": false, "properties": {"schemas": {"$ref": "http://asyncapi.com/definitions/1.0.0/schemas.json"}, "messages": {"$ref": "http://asyncapi.com/definitions/1.0.0/messages.json"}, "securitySchemes": {"type": "object", "patternProperties": {"^[a-zA-Z0-9\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/1.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/1.0.0/SecurityScheme.json"}]}}}}, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.0.0/components.json"}