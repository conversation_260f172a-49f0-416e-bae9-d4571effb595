{"title": "Stream Object", "type": "object", "additionalProperties": false, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.2.0/vendorExtension.json"}}, "minProperties": 1, "properties": {"framing": {"title": "Stream Framing Object", "type": "object", "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.2.0/vendorExtension.json"}}, "minProperties": 1, "oneOf": [{"additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["chunked"]}, "delimiter": {"type": "string", "enum": ["\\r\\n", "\\n"], "default": "\\r\\n"}}}, {"additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["sse"]}, "delimiter": {"type": "string", "enum": ["\\n\\n"], "default": "\\n\\n"}}}]}, "read": {"title": "Stream Read Object", "type": "array", "uniqueItems": true, "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/1.2.0/message.json"}}, "write": {"title": "Stream Write Object", "type": "array", "uniqueItems": true, "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/1.2.0/message.json"}}}, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.2.0/stream.json"}