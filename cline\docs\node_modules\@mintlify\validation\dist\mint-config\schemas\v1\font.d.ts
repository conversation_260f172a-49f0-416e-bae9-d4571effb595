import { z } from 'zod';
export declare const fontDetailsSchema: z.ZodEffects<z.ZodObject<{
    family: z.ZodString;
    weight: z.ZodOptional<z.ZodNumber>;
    url: z.ZodOptional<z.ZodString>;
    format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
}, "strip", z.ZodType<PERSON>ny, {
    family: string;
    weight?: number | undefined;
    url?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}, {
    family: string;
    weight?: number | undefined;
    url?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}>, {
    family: string;
    weight?: number | undefined;
    url?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}, {
    family: string;
    weight?: number | undefined;
    url?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}>;
export declare const fontSchema: z.<PERSON>n<[z.ZodEffects<z.ZodObject<{
    family: z.ZodString;
    weight: z.ZodOptional<z.ZodNumber>;
    url: z.ZodOptional<z.ZodString>;
    format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
}, "strip", z.ZodTypeAny, {
    family: string;
    weight?: number | undefined;
    url?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}, {
    family: string;
    weight?: number | undefined;
    url?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}>, {
    family: string;
    weight?: number | undefined;
    url?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}, {
    family: string;
    weight?: number | undefined;
    url?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}>, z.ZodObject<{
    headings: z.ZodOptional<z.ZodEffects<z.ZodObject<{
        family: z.ZodString;
        weight: z.ZodOptional<z.ZodNumber>;
        url: z.ZodOptional<z.ZodString>;
        format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
    }, "strip", z.ZodTypeAny, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>>;
    body: z.ZodOptional<z.ZodEffects<z.ZodObject<{
        family: z.ZodString;
        weight: z.ZodOptional<z.ZodNumber>;
        url: z.ZodOptional<z.ZodString>;
        format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
    }, "strip", z.ZodTypeAny, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>>;
}, "strict", z.ZodTypeAny, {
    headings?: {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | undefined;
    body?: {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | undefined;
}, {
    headings?: {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | undefined;
    body?: {
        family: string;
        weight?: number | undefined;
        url?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | undefined;
}>]>;
