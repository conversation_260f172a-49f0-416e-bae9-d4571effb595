{"type": "object", "description": "An object to hold a set of reusable objects for different aspects of the AsyncAPI specification. All objects defined within the components object will have no effect on the API unless they are explicitly referenced from properties outside the components object.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"schemas": {"type": "object", "description": "An object to hold reusable Schema Object. If this is a Schema Object, then the schemaFormat will be assumed to be 'application/vnd.aai.asyncapi+json;version=asyncapi' where the version is equal to the AsyncAPI Version String.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/anySchema.json"}}}, "servers": {"type": "object", "description": "An object to hold reusable Server Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/server.json"}]}}}, "channels": {"type": "object", "description": "An object to hold reusable Channel Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/channel.json"}]}}}, "serverVariables": {"type": "object", "description": "An object to hold reusable Server Variable Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/serverVariable.json"}]}}}, "operations": {"type": "object", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/operation.json"}]}}}, "messages": {"type": "object", "description": "An object to hold reusable Message Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/messageObject.json"}]}}}, "securitySchemes": {"type": "object", "description": "An object to hold reusable Security Scheme Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/SecurityScheme.json"}]}}}, "parameters": {"type": "object", "description": "An object to hold reusable Parameter Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/parameter.json"}]}}}, "correlationIds": {"type": "object", "description": "An object to hold reusable Correlation ID Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/correlationId.json"}]}}}, "operationTraits": {"type": "object", "description": "An object to hold reusable Operation Trait Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/operationTrait.json"}]}}}, "messageTraits": {"type": "object", "description": "An object to hold reusable Message Trait Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/messageTrait.json"}]}}}, "replies": {"type": "object", "description": "An object to hold reusable Operation Reply Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/operationReply.json"}]}}}, "replyAddresses": {"type": "object", "description": "An object to hold reusable Operation Reply Address Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/operationReplyAddress.json"}]}}}, "serverBindings": {"type": "object", "description": "An object to hold reusable Server Bindings Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/serverBindingsObject.json"}]}}}, "channelBindings": {"type": "object", "description": "An object to hold reusable Channel Bindings Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/channelBindingsObject.json"}]}}}, "operationBindings": {"type": "object", "description": "An object to hold reusable Operation Bindings Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/operationBindingsObject.json"}]}}}, "messageBindings": {"type": "object", "description": "An object to hold reusable Message Bindings Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/messageBindingsObject.json"}]}}}, "tags": {"type": "object", "description": "An object to hold reusable Tag Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/tag.json"}]}}}, "externalDocs": {"type": "object", "description": "An object to hold reusable External Documentation Objects.", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/externalDocs.json"}]}}}}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/components.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/components.json"}