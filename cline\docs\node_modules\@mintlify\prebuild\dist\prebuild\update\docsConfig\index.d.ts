import { AsyncAPIFile } from '@mintlify/common';
import { OpenApiFile, DecoratedNavigationPage } from '@mintlify/models';
import { DocsConfig } from '@mintlify/validation';
export declare function updateDocsConfigFile(contentDirectoryPath: string, openApiFiles: OpenApiFile[], asyncApiFiles: AsyncAPIFile[], docsConfig?: DocsConfig, localSchema?: boolean): Promise<{
    docsConfig: DocsConfig;
    pagesAcc: Record<string, DecoratedNavigationPage>;
    newOpenApiFiles: OpenApiFile[];
    newAsyncApiFiles: AsyncAPIFile[];
}>;
export { generateOpenApiDivisions } from './generateOpenApiDivisions.js';
export { generateOpenApiFromDocsConfig } from './generateOpenApiFromDocsConfig.js';
export { generateAsyncApiDivisions } from './generateAsyncApiDivisions.js';
export { generateAsyncApiFromDocsConfig } from './generateAsyncApiFromDocsConfig.js';
