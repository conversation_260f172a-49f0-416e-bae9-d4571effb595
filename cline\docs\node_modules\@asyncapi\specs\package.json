{"name": "@asyncapi/specs", "version": "6.8.1", "description": "AsyncAPI schema versions", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "npm run build && vitest run && npm run validate:schemas", "build": "npm run bundle", "generate:assets": "npm run build", "prepublishOnly": "npm run build", "bundle": "cd tools/bundler && npm i && npm run bundle", "startNewVersion": "newVersion=$npm_config_new_version node scripts/add-new-version.js", "lint": "eslint --max-warnings 0 --config .eslintrc.yml .", "lint:fix": "eslint --max-warnings 0 --config .eslintrc.yml --fix .", "bump:version": "npm --no-git-tag-version --allow-same-version version $VERSION", "validate:schemas": "node scripts/validate-schemas.js"}, "repository": {"type": "git", "url": "git+https://github.com/asyncapi/spec-json-schemas.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "Apache-2.0", "bugs": {"url": "https://github.com/asyncapi/spec-json-schemas/issues"}, "homepage": "https://github.com/asyncapi/spec-json-schemas#readme", "devDependencies": {"ajv": "^8.12.0", "ajv-draft-04": "^1.0.0", "ajv-formats": "^3.0.1", "conventional-changelog-conventionalcommits": "^5.0.0", "eslint": "^8.56.0", "eslint-plugin-sonarjs": "^0.23.0", "nyc": "^15.1.0", "vite-require": "^0.2.3", "vitest": "^1.6.0"}, "dependencies": {"@types/json-schema": "^7.0.11"}}