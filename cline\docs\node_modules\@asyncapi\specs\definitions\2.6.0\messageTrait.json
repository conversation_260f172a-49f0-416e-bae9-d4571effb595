{"type": "object", "description": "Describes a trait that MAY be applied to a Message Object.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "properties": {"schemaFormat": {"type": "string", "description": "A string containing the name of the schema format/language used to define the message payload."}, "contentType": {"type": "string", "description": "The content type to use when encoding/decoding a message's payload."}, "headers": {"description": "Schema definition of the application headers.", "allOf": [{"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}, {"properties": {"type": {"const": "object"}}}]}, "messageId": {"type": "string", "description": "Unique string used to identify the message. The id MUST be unique among all messages described in the API."}, "correlationId": {"description": "Definition of the correlation ID used for message tracing or matching.", "oneOf": [{"$ref": "http://asyncapi.com/definitions/2.6.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/correlationId.json"}]}, "tags": {"type": "array", "description": "A list of tags for logical grouping and categorization of messages.", "items": {"$ref": "http://asyncapi.com/definitions/2.6.0/tag.json"}, "uniqueItems": true}, "summary": {"type": "string", "description": "A brief summary of the message."}, "name": {"type": "string", "description": "Name of the message."}, "title": {"type": "string", "description": "A human-friendly title for the message."}, "description": {"type": "string", "description": "A longer description of the message. CommonMark is allowed."}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/2.6.0/externalDocs.json"}, "deprecated": {"type": "boolean", "default": false}, "examples": {"type": "array", "description": "List of examples.", "items": {"type": "object", "additionalProperties": false, "anyOf": [{"required": ["payload"]}, {"required": ["headers"]}], "properties": {"name": {"type": "string", "description": "Machine readable name of the message example."}, "summary": {"type": "string", "description": "A brief summary of the message example."}, "headers": {"type": "object", "description": "Schema definition of the application headers."}, "payload": {"description": "Definition of the message payload. It can be of any type"}}}}, "bindings": {"$ref": "http://asyncapi.com/definitions/2.6.0/bindingsObject.json"}}, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/messageTrait.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/messageTrait.json"}