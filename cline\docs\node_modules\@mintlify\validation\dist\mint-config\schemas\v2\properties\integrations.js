import { z } from 'zod';
const amplitudeConfigSchema = z.object({
    apiKey: z.string(),
});
const clearbitConfigSchema = z.object({
    publicApiKey: z.string(),
});
const fathomConfigSchema = z.object({
    siteId: z.string(),
});
const googleAnalyticsConfigSchema = z.object({
    measurementId: z.string().startsWith('G', 'Must start with G'),
});
const googleTagManagerConfigSchema = z.object({
    tagId: z.string().startsWith('G', 'Must start with G'),
});
const hotjarConfigSchema = z.object({
    hjid: z.string(),
    hjsv: z.string(),
});
const koalaConfigSchema = z.object({
    publicApiKey: z.string().min(2),
});
const logrocketConfigSchema = z.object({
    appId: z.string(),
});
const mixpanelConfigSchema = z.object({
    projectToken: z.string(),
});
const pirschConfigSchema = z.object({
    id: z.string(),
});
const postHogConfigSchema = z.object({
    apiKey: z.string().startsWith('phc_', 'Must start with phc_'),
    apiHost: z.string().url('Must be a valid URL').optional(),
});
const plausibleConfigSchema = z.object({
    domain: z
        .string()
        .refine((domain) => !domain.startsWith('https://'), 'Must not start with https://'),
    server: z
        .string()
        .refine((server) => !server.startsWith('https://'), 'Must not start with https://')
        .optional(),
});
const heapConfigSchema = z.object({
    appId: z.string(),
});
const segmentConfigSchema = z.object({
    key: z.string(),
});
const intercomSchema = z.object({
    appId: z.string().min(6, 'Must be a valid Intercom app ID'),
});
const frontchatSchema = z.object({
    snippetId: z.string().min(6, 'Must be a valid Front chat snippet id'),
});
const osanoSchema = z.object({
    scriptSource: z
        .string()
        .startsWith('https://cmp.osano.com/', 'A valid Osano script source must start with https://cmp.osano.com/')
        .endsWith('/osano.js', 'A valid Osano script source must end with /osano.js'),
});
const telemetrySchema = z.object({
    enabled: z.boolean().optional(),
});
const cookieConsentSchema = z.object({
    key: z.string().optional(),
    value: z.string().optional(),
});
export const integrationsSchema = z
    .object({
    amplitude: amplitudeConfigSchema.optional(),
    clearbit: clearbitConfigSchema.optional(),
    fathom: fathomConfigSchema.optional(),
    frontchat: frontchatSchema.optional(),
    ga4: googleAnalyticsConfigSchema.optional(),
    gtm: googleTagManagerConfigSchema.optional(),
    heap: heapConfigSchema.optional(),
    hotjar: hotjarConfigSchema.optional(),
    intercom: intercomSchema.optional(),
    koala: koalaConfigSchema.optional(),
    logrocket: logrocketConfigSchema.optional(),
    mixpanel: mixpanelConfigSchema.optional(),
    osano: osanoSchema.optional(),
    pirsch: pirschConfigSchema.optional(),
    posthog: postHogConfigSchema.optional(),
    plausible: plausibleConfigSchema.optional(),
    segment: segmentConfigSchema.optional(),
    telemetry: telemetrySchema.optional(),
    cookies: cookieConsentSchema.optional(),
})
    .strict()
    .describe('Configurations for official integrations');
