import { generateExampleFromSchema, } from '@mintlify/validation';
import { visitParents } from 'unist-util-visit-parents';
import { isMdxJsxFlowElementHast } from '../../../lib/mdx-utils.js';
import { findParentSchema } from './findParentSchema.js';
import { insertSchema } from './insertSchema.js';
import { parseApiString, parseAuthMethod, parseField } from './parsers.js';
export const rehypeMdxExtractEndpoint = (metadata, config, mdxExtracts) => {
    return (tree) => {
        var _a;
        if (!(metadata &&
            typeof metadata === 'object' &&
            'api' in metadata &&
            typeof metadata.api === 'string')) {
            return;
        }
        let parseResult = undefined;
        try {
            parseResult = parseApiString(metadata.api, config);
        }
        catch (_b) {
            console.error(`error parsing api string: "${metadata.api}"`);
            return;
        }
        const { servers, path, method } = parseResult;
        const title = 'title' in metadata && typeof metadata.title === 'string' ? metadata.title : '';
        const authMethodString = 'authMethod' in metadata && typeof metadata.authMethod === 'string'
            ? metadata.authMethod
            : undefined;
        const security = parseAuthMethod(authMethodString, config);
        const endpoint = {
            title,
            path,
            servers,
            method,
            request: {
                security,
                parameters: {
                    query: {},
                    header: {},
                    cookie: {},
                    path: {},
                },
                body: {},
            },
            response: {},
            deprecated: false,
        };
        const requestContentType = 'contentType' in metadata && typeof metadata.contentType === 'string'
            ? metadata.contentType
            : 'application/json';
        const nodeToSchema = new Map();
        visitParents(tree, isMdxJsxFlowElementHast, (node, parents) => {
            if (node.name === 'Param' || node.name === 'ParamField' || node.name === 'ResponseField') {
                const parentSchema = findParentSchema(node.name, parents, nodeToSchema);
                // we only want ParamFields/ResponseFields that are nested in others of the same type, or at the top level
                if (parentSchema === undefined && parents.length > 1) {
                    console.error(`${node.name} tags must occur at the top level or inside another ${node.name} tag`);
                    return 'skip';
                }
                const parsedParamField = parseField(node);
                if (parsedParamField === undefined) {
                    console.error('param field conversion failed');
                    return 'skip';
                }
                const insertSuccessful = insertSchema({
                    locationAttr: parsedParamField.locationAttr,
                    schema: parsedParamField.schema,
                    deepestSchema: parsedParamField.deepestSchema,
                    endpoint,
                    node,
                    parentSchema,
                    nodeToSchema,
                    requestContentType,
                });
                if (!insertSuccessful) {
                    return 'skip';
                }
            }
        });
        const content = endpoint.request.body[requestContentType];
        if (content) {
            content.examples['example'] = { value: generateExampleFromSchema(content.schemaArray[0]) };
        }
        const response = (_a = endpoint.response['200']) === null || _a === void 0 ? void 0 : _a['application/json'];
        if (response) {
            response.examples['example'] = { value: generateExampleFromSchema(response.schemaArray[0]) };
        }
        mdxExtracts.endpoint = endpoint;
        return tree;
    };
};
