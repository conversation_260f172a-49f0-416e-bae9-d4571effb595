{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/mqtt/0.2.0/operation.json", "title": "MQTT operation bindings object", "description": "This object contains information about the operation representation in MQTT.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"qos": {"type": "integer", "enum": [0, 1, 2], "description": "Defines the Quality of Service (QoS) levels for the message flow between client and server. Its value MUST be either 0 (At most once delivery), 1 (At least once delivery), or 2 (Exactly once delivery)."}, "retain": {"type": "boolean", "description": "Whether the broker should retain the message or not."}, "messageExpiryInterval": {"oneOf": [{"type": "integer", "minimum": 0, "maximum": 4294967295}, {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}], "description": "Lifetime of the message in seconds"}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"qos": 2, "retain": true, "messageExpiryInterval": 60, "bindingVersion": "0.2.0"}]}