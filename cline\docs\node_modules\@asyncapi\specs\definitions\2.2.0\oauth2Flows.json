{"type": "object", "required": ["type", "flows"], "properties": {"type": {"type": "string", "enum": ["oauth2"]}, "description": {"type": "string"}, "flows": {"type": "object", "properties": {"implicit": {"allOf": [{"$ref": "http://asyncapi.com/definitions/2.2.0/oauth2Flow.json"}, {"required": ["authorizationUrl", "scopes"]}, {"not": {"required": ["tokenUrl"]}}]}, "password": {"allOf": [{"$ref": "http://asyncapi.com/definitions/2.2.0/oauth2Flow.json"}, {"required": ["tokenUrl", "scopes"]}, {"not": {"required": ["authorizationUrl"]}}]}, "clientCredentials": {"allOf": [{"$ref": "http://asyncapi.com/definitions/2.2.0/oauth2Flow.json"}, {"required": ["tokenUrl", "scopes"]}, {"not": {"required": ["authorizationUrl"]}}]}, "authorizationCode": {"allOf": [{"$ref": "http://asyncapi.com/definitions/2.2.0/oauth2Flow.json"}, {"required": ["authorizationUrl", "tokenUrl", "scopes"]}]}}, "additionalProperties": false}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.2.0/specificationExtension.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.2.0/oauth2Flows.json"}