import { z } from 'zod';
export declare const backgroundSchema: z.ZodObject<{
    image: z.ZodOptional<z.<PERSON><[z.ZodString, z.ZodObject<{
        light: z.ZodString;
        dark: z.ZodString;
    }, "strip", z.<PERSON>, {
        light: string;
        dark: string;
    }, {
        light: string;
        dark: string;
    }>]>>;
    decoration: z.<PERSON>ptional<z.ZodEnum<["gradient", "grid", "windows"]>>;
    color: z.ZodOptional<z.ZodObject<{
        light: z.ZodOptional<z.ZodString>;
        dark: z.ZodOptional<z.ZodString>;
    }, "strict", z.<PERSON>Type<PERSON>ny, {
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        light?: string | undefined;
        dark?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    image?: string | {
        light: string;
        dark: string;
    } | undefined;
    decoration?: "gradient" | "grid" | "windows" | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
}, {
    image?: string | {
        light: string;
        dark: string;
    } | undefined;
    decoration?: "gradient" | "grid" | "windows" | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
}>;
