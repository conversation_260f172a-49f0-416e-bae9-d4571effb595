import { OpenAPIV3 } from 'openapi-types';
const VALID_METHODS = Object.values(OpenAPIV3.HttpMethods).concat('webhook');
/**
 *
 * @param str either the openapi or api string defined in the frontmatter
 * @returns method: API method (GET, POST, PUT, DELETE, PATCH)
 *          endpoint: API endpoint
 *          filename: filename of the openapi file (if applicable)
 *          returns undefined for any of the values (fails silently)
 */
export const potentiallyParseOpenApiString = (str) => {
    const components = str.trim().split(/\s+/);
    let filename;
    let method;
    let endpoint;
    if (components.length > 3) {
        // improperly formatted openapi string
        return undefined;
    }
    else if (components[0] && components[1] && components[2]) {
        [filename, method, endpoint] = components;
    }
    else if (components[0] && components[1]) {
        [method, endpoint] = components;
    }
    else {
        // improperly formatted openapi string
        return undefined;
    }
    if (method) {
        method = method.toLowerCase();
        if (!VALID_METHODS.includes(method)) {
            // invalid http method
            return undefined;
        }
    }
    return {
        method: method,
        endpoint,
        filename,
    };
};
/**
 *
 * @param str either the openapi or api string defined in the frontmatter
 * @returns method: API method (GET, POST, PUT, DELETE, PATCH)
 *          endpoint: API endpoint
 *          filename: filename of the openapi file (if applicable)
 */
export const parseOpenApiString = (str) => {
    const components = str.trim().split(/\s+/);
    let filename;
    let method;
    let endpoint;
    if (components.length > 3) {
        throw new Error('improperly formatted openapi string');
    }
    else if (components[0] && components[1] && components[2]) {
        [filename, method, endpoint] = components;
    }
    else if (components[0] && components[1]) {
        [method, endpoint] = components;
    }
    else {
        throw new Error('improperly formatted openapi string');
    }
    method = method.toLowerCase();
    if (!VALID_METHODS.includes(method)) {
        throw new Error('invalid http method');
    }
    return {
        method: method,
        endpoint,
        filename,
    };
};
