{"type": "object", "additionalProperties": false, "description": "information about external documentation", "required": ["url"], "properties": {"description": {"type": "string"}, "url": {"type": "string", "format": "uri"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.4.0/specificationExtension.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.4.0/externalDocs.json"}