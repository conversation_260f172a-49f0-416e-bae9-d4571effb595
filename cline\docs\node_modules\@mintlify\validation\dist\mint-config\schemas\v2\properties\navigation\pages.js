import { z } from 'zod';
import { decoratedPageSchema, pageSchema } from '../reusable/page.js';
import { decoratedGroupSchema, groupSchema } from './groups.js';
export const pageOrGroupSchema = z.lazy(() => z.union([pageSchema, groupSchema]));
export const decoratedPageOrGroupSchema = z.lazy(() => z.union([decoratedPageSchema, decoratedGroupSchema]));
export const pagesSchema = z.array(pageOrGroupSchema).describe('An array of page paths or groups');
export const decoratedPagesSchema = z
    .array(decoratedPageOrGroupSchema)
    .describe('An array of page metadata or groups');
