{"type": "object", "additionalProperties": false, "description": "Allows referencing an external resource for extended documentation.", "required": ["url"], "properties": {"description": {"type": "string", "description": "A short description of the target documentation. CommonMark syntax can be used for rich text representation."}, "url": {"type": "string", "description": "The URL for the target documentation. This MUST be in the form of an absolute URL.", "format": "uri"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/externalDocs.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/externalDocs.json"}