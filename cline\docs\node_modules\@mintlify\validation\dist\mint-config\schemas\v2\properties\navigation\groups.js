import { z } from 'zod';
import { asyncApiSchema } from '../reusable/asyncapi.js';
import { hiddenSchema } from '../reusable/hidden.js';
import { iconSchema } from '../reusable/icon.js';
import { openApiSchema } from '../reusable/openapi.js';
import { decoratedPageSchema, pageSchema } from '../reusable/page.js';
import { decoratedPagesSchema, pagesSchema } from './pages.js';
export const baseGroupSchema = z.object({
    group: z.string().nonempty().describe('The name of the group'),
    icon: iconSchema.optional(),
    hidden: hiddenSchema.optional(),
    root: pageSchema.optional(),
    tag: z.string().optional().describe('Tag for the group'),
});
export const groupSchema = z
    .union([
    baseGroupSchema.extend({ openapi: openApiSchema }),
    baseGroupSchema.extend({ asyncapi: asyncApiSchema }),
    baseGroupSchema.extend({ pages: z.lazy(() => pagesSchema) }),
])
    .describe('Organizing by groups');
export const decoratedGroupSchema = z
    .object({
    group: z.string().nonempty().describe('The name of the group'),
    icon: iconSchema.optional(),
    hidden: hiddenSchema.optional(),
    root: decoratedPageSchema.optional(),
    pages: z.lazy(() => decoratedPagesSchema),
    tag: z.string().optional().describe('Tag for the group'),
})
    .describe('Organizing by groups');
export const groupsSchema = z
    .array(groupSchema)
    .min(1, 'At least one group must be specified')
    .describe('Organizing by groups');
export const decoratedGroupsSchema = z
    .array(decoratedGroupSchema)
    .min(1, 'At least one group must be specified')
    .describe('Organizing by groups');
