{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/kafka/0.5.0/channel.json", "title": "Channel Schema", "description": "This object contains information about the channel representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"topic": {"type": "string", "description": "Kafka topic name if different from channel name."}, "partitions": {"type": "integer", "minimum": 1, "description": "Number of partitions configured on this topic."}, "replicas": {"type": "integer", "minimum": 1, "description": "Number of replicas configured on this topic."}, "topicConfiguration": {"description": "Topic configuration properties that are relevant for the API.", "type": "object", "additionalProperties": true, "properties": {"cleanup.policy": {"description": "The [`cleanup.policy`](https://kafka.apache.org/documentation/#topicconfigs_cleanup.policy) configuration option.", "type": "array", "items": {"type": "string", "enum": ["compact", "delete"]}}, "retention.ms": {"description": "The [`retention.ms`](https://kafka.apache.org/documentation/#topicconfigs_retention.ms) configuration option.", "type": "integer", "minimum": -1}, "retention.bytes": {"description": "The [`retention.bytes`](https://kafka.apache.org/documentation/#topicconfigs_retention.bytes) configuration option.", "type": "integer", "minimum": -1}, "delete.retention.ms": {"description": "The [`delete.retention.ms`](https://kafka.apache.org/documentation/#topicconfigs_delete.retention.ms) configuration option.", "type": "integer", "minimum": 0}, "max.message.bytes": {"description": "The [`max.message.bytes`](https://kafka.apache.org/documentation/#topicconfigs_max.message.bytes) configuration option.", "type": "integer", "minimum": 0}, "confluent.key.schema.validation": {"description": "It shows whether the schema validation for the message key is enabled. Vendor specific config. For more details: (https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#confluent-key-schema-validation)", "type": "boolean"}, "confluent.key.subject.name.strategy": {"description": "The name of the schema lookup strategy for the message key. Vendor specific config. For more details: (https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#confluent-key-subject-name-strategy)", "type": "string"}, "confluent.value.schema.validation": {"description": "It shows whether the schema validation for the message value is enabled. Vendor specific config. For more details: (https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#confluent-value-schema-validation)", "type": "boolean"}, "confluent.value.subject.name.strategy": {"description": "The name of the schema lookup strategy for the message value. Vendor specific config. For more details: (https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#confluent-value-subject-name-strategy)", "type": "string"}}}, "bindingVersion": {"type": "string", "enum": ["0.5.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"topic": "my-specific-topic", "partitions": 20, "replicas": 3, "bindingVersion": "0.5.0"}]}