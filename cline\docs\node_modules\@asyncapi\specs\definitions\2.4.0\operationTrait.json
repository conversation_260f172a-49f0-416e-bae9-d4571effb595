{"type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.4.0/specificationExtension.json"}}, "properties": {"summary": {"type": "string"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/2.4.0/tag.json"}, "uniqueItems": true}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/2.4.0/externalDocs.json"}, "operationId": {"type": "string"}, "security": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/2.4.0/SecurityRequirement.json"}}, "bindings": {"$ref": "http://asyncapi.com/definitions/2.4.0/bindingsObject.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.4.0/operationTrait.json"}