import { IconType } from '../mintconfig/index.js';
import { ApiPlaygroundDisplayType } from './apiPlaygroundDisplayType.js';
export type PageMetaTags = {
    href?: string;
    title?: string;
    'og:title'?: string;
    sidebarTitle?: string;
    description?: string;
    api?: string;
    openapi?: string;
    asyncapi?: string;
    contentType?: string;
    authMethod?: string;
    auth?: string;
    version?: string;
    mode?: string;
    hideFooterPagination?: boolean;
    authors?: unknown;
    lastUpdatedDate?: string;
    createdDate?: string;
    'openapi-schema'?: string;
    icon?: string;
    iconType?: IconType;
    tag?: string;
    url?: string;
    hideApiMarker?: boolean;
    noindex?: boolean;
    isPublic?: boolean;
    public?: boolean;
    playground?: ApiPlaygroundDisplayType;
    keywords?: string[];
} & Record<string, unknown>;
