import { IncrementalDataSchemaArray } from './types/endpoint.js';
import { SimpleSchema, SchemaOrRef, Schema3_1, SumOfProducts, SchemaOrRefComposition } from './types/schema.js';
/**
 * Given an OpenAPI 3.1 SchemaObject or ReferenceObject containing any number of
 * refs or compositions, this function returns the schema in sum-of-products form.
 *
 * When given the following schema:
 *
 * ```yaml
 * title: 'A'
 * oneOf:
 *   - { title: 'B' }
 *   - { title: 'C' }
 * allOf:
 *   - { title: 'D' }
 *   - { title: 'E' }
 * ```
 *
 * this function returns the following sum of products:
 *
 * ```js
 * [
 *   [{ title: 'B' }, { title: 'D' }, { title: 'E' }, { title: 'A' }],
 *   [{ title: 'C' }, { title: 'D' }, { title: 'E' }, { title: 'A' }],
 * ]
 * ```
 *
 * @param schema The schema or ref to reduce
 * @param componentSchemas The value of `document.components.schemas`, to be used when dereferencing
 * @returns The schema in sum-of-products form
 */
export declare function reduceToSumOfProducts(schemaOrRef: SchemaOrRefComposition, componentSchemas: Record<string, Schema3_1> | undefined, opts?: {
    isRoot?: true;
    _depth?: number;
}): SumOfProducts;
/**
 * This function logically combines an array of simple schemas (schemas that contain no compositions)
 * in preparation for conversion to `IncrementalDataSchema`. This is akin to "multiplying" all of the schemas,
 * to continue our math analogy. The result is a single simple schema, which is easier to work with.
 *
 * How fields are combined depends on the field. For fields like `title` and `description` where
 * it doesn't make sense to combine, we just take the last. For `required` we combine arrays,
 * for `maximum` we take the minimum value, etc.
 *
 * @param schemas An array of simple schemas to combine
 * @param componentSchemas The value of `document.components.schemas`. In this function, it is only used to check if properties are readOnly/writeOnly
 * @param location Whether the schema is part of the request, response, or neither. Used for filtering readOnly/writeOnly properties
 * @returns A single simple schema that satisfies all the input schemas
 */
export declare function combineSimpleSchemas(schemas: SimpleSchema[], componentSchemas: Record<string, Schema3_1> | undefined, location?: 'request' | 'response'): SimpleSchema;
export declare function generateFirstIncrementalSchema(schema: SchemaOrRef | undefined, componentSchemas: Record<string, Schema3_1> | undefined, required?: boolean, location?: 'request' | 'response', contentType?: string): IncrementalDataSchemaArray;
export declare function generateNextIncrementalSchema(schema: SchemaOrRef, componentSchemas: Record<string, Schema3_1> | undefined, required?: boolean, location?: 'request' | 'response'): IncrementalDataSchemaArray;
