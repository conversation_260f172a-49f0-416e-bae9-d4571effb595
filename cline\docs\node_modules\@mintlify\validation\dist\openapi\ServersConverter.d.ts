import { OpenAPIV3_1 } from 'openapi-types';
import { BaseConverter } from './BaseConverter.js';
import { Server } from './types/endpoint.js';
export declare class ServersConverter extends BaseConverter {
    readonly servers: OpenAPIV3_1.ServerObject[] | undefined;
    private constructor();
    private convert;
    private convertVariables;
    static convert(servers: OpenAPIV3_1.ServerObject[] | undefined): Server[] | undefined;
}
