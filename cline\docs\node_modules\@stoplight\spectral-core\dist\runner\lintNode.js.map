{"version": 3, "file": "lintNode.js", "sourceRoot": "", "sources": ["../../src/runner/lintNode.ts"], "names": [], "mappings": ";;;AAAA,kEAA+G;AAC/G,mCAAsC;AACtC,2CAA4C;AAE5C,0CAAuC;AAGvC,mCAA+D;AAGxD,MAAM,QAAQ,GAAG,CAAC,OAA+B,EAAE,IAAgB,EAAE,IAAU,EAAQ,EAAE;;IAC9F,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAExG,MAAM,SAAS,GAA4C;QACzD,QAAQ,EAAE,OAAO,CAAC,iBAAiB,CAAC,QAAQ;QAC5C,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;QAC5C,IAAI;QACJ,IAAI,EAAE,SAAS;KAChB,CAAC;IAEF,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;QAC5B,MAAM,OAAO,GAAG,IAAA,sBAAc,EAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,SAAS,CAAC,IAAI,GAAG,CAAC,GAAG,SAAS,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;aACjD;iBAAM;gBACL,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC;aAC5B;YAED,IAAI,aAAa,CAAC;YAClB,IAAI;gBACF,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,MAAA,IAAI,CAAC,eAAe,mCAAI,IAAI,EAAE,SAAS,CAAC,CAAC;aACtF;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,IAAI,2BAAc,CACtB,aAAa,IAAI,CAAC,QAAQ,CAAC,IAAI,uBAAuB,IAAA,gBAAO,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAC1F;oBACE,KAAK,EAAE,CAAC;iBACT,CACF,CAAC;aACH;YAED,IAAI,aAAa,KAAK,KAAK,CAAC;gBAAE,SAAS;YAEvC,IAAI,MAAM,IAAI,aAAa,EAAE;gBAC3B,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;gBACpC,OAAO,CAAC,QAAQ,CAAC,IAAI,CACnB,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC3B,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CACjF,CACF,CAAC;aACH;iBAAM;gBACL,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;aACzD;SACF;KACF;AACH,CAAC,CAAC;AA9CW,QAAA,QAAQ,YA8CnB;AAEF,SAAS,oBAAoB,CAC3B,OAA+B,EAC/B,SAAkD,EAClD,OAA0B;;IAE1B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC;IAC7C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,MAAM,eAAe,GAAG,CAAC,MAAA,MAAM,CAAC,IAAI,mCAAI,UAAU,CAAC,CAAC,GAAG,CAAC,wCAAqB,CAAC,CAAC;QAC/E,MAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3G,MAAM,IAAI,GAAG,MAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,IAAI,mCAAI,IAAA,qCAAkB,EAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC7G,MAAM,MAAM,GAAG,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,QAAQ,CAAC,MAAM,CAAC;QAE/C,MAAM,QAAQ,GAAG,MAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,QAAQ,mCAAI,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC;QAChF,MAAM,KAAK,GAAG,MAAA,QAAQ,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,mCAAI,mBAAQ,CAAC,aAAa,CAAC;QACjF,MAAM,KAAK,GAAY,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,YAAG,EAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEpF,MAAM,IAAI,GAAgB;YACxB,QAAQ,EACN,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,mBAAmB,MAAK,KAAK,CAAC,IAAI,cAAc,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;gBACvG,CAAC,CAAC,IAAA,4BAAS,EAAC,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,6BAAU,CAAC,GAAG,CAAC;gBACtF,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;oBACjB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;oBACvB,CAAC,CAAC,EAAE;YACR,KAAK,EAAE,MAAM,CAAC,OAAO;YACrB,IAAI,EAAE,IAAA,4BAAS,EAAC,IAAI,EAAE,6BAAU,CAAC,cAAc,CAAC;YAChD,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK;SACN,CAAC;QAEF,MAAM,aAAa,GAAG,IAAA,eAAO,EAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;QAE3B,MAAM,QAAQ,GAAG,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;QAEhH,IAAI,QAAQ,KAAK,CAAC,CAAC;YAAE,SAAS;QAE9B,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,MAAA,IAAI,CAAC,WAAW,mCAAI,aAAa,CAAC,CAAC,CAAC,IAAA,eAAO,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;YACzG,IAAI;YACJ,QAAQ;YACR,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACxC,KAAK;YACL,gBAAgB,EAAE,MAAA,IAAI,CAAC,gBAAgB,mCAAI,SAAS;SACrD,CAAC,CAAC;KACJ;AACH,CAAC"}