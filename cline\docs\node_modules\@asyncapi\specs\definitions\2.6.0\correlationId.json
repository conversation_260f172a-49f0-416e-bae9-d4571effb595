{"type": "object", "description": "An object that specifies an identifier at design time that can used for message tracing and correlation.", "required": ["location"], "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "properties": {"description": {"type": "string", "description": "A optional description of the correlation ID. GitHub Flavored Markdown is allowed."}, "location": {"type": "string", "description": "A runtime expression that specifies the location of the correlation ID", "pattern": "^\\$message\\.(header|payload)#(\\/(([^\\/~])|(~[01]))*)*"}}, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/correlationId.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/correlationId.json"}