export declare const generateMessage: (path: string[], ...messages: string[]) => string;
/**
 * Thrown when a value is incorrect or missing, according to the OpenAPI spec.
 */
export declare class InvalidSchemaError extends Error {
    constructor(path: string[], ...messages: string[]);
}
/**
 * Thrown when a schema is valid according to OpenAPI, but is unsatisfiable
 * (e.g. a value that must be both a number and a string).
 */
export declare class ImpossibleSchemaError extends Error {
    constructor(path: string[], ...messages: string[]);
}
/**
 * Thrown when there is an issue that was caused in another part of the conversion
 * (e.g. a schema in reduced form that is missing `oneOf`).
 */
export declare class ConversionError extends Error {
    constructor(path: string[], ...messages: string[]);
}
