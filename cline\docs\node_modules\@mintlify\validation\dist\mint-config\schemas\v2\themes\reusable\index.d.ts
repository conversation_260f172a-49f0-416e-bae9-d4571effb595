export declare const standardConfigSchema: {
    $schema: import("zod").ZodDefault<import("zod").ZodOptional<import("zod").ZodString>>;
    name: import("zod").ZodString;
    description: import("zod").ZodOptional<import("zod").ZodString>;
    colors: import("zod").ZodObject<{
        primary: import("zod").ZodString;
        light: import("zod").ZodOptional<import("zod").ZodString>;
        dark: import("zod").ZodOptional<import("zod").ZodString>;
    }, "strict", import("zod").ZodTypeAny, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    }>;
    logo: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodString, import("zod").ZodObject<{
        light: import("zod").ZodString;
        dark: import("zod").ZodString;
        href: import("zod").ZodOptional<import("zod").ZodString>;
    }, "strip", import("zod").ZodTypeAny, {
        light: string;
        dark: string;
        href?: string | undefined;
    }, {
        light: string;
        dark: string;
        href?: string | undefined;
    }>]>>;
    favicon: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodString, import("zod").ZodObject<{
        light: import("zod").ZodString;
        dark: import("zod").ZodString;
    }, "strip", import("zod").ZodTypeAny, {
        light: string;
        dark: string;
    }, {
        light: string;
        dark: string;
    }>]>>;
    api: import("zod").ZodOptional<import("zod").ZodObject<{
        openapi: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodArray<import("zod").ZodEffects<import("zod").ZodString, string, string>, "many">, import("zod").ZodObject<{
            source: import("zod").ZodEffects<import("zod").ZodString, string, string>;
            directory: import("zod").ZodOptional<import("zod").ZodString>;
        }, "strict", import("zod").ZodTypeAny, {
            source: string;
            directory?: string | undefined;
        }, {
            source: string;
            directory?: string | undefined;
        }>]>>;
        asyncapi: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodArray<import("zod").ZodEffects<import("zod").ZodString, string, string>, "many">, import("zod").ZodObject<{
            source: import("zod").ZodEffects<import("zod").ZodString, string, string>;
            directory: import("zod").ZodOptional<import("zod").ZodString>;
        }, "strict", import("zod").ZodTypeAny, {
            source: string;
            directory?: string | undefined;
        }, {
            source: string;
            directory?: string | undefined;
        }>]>>;
        params: import("zod").ZodOptional<import("zod").ZodObject<{
            expanded: import("zod").ZodOptional<import("zod").ZodEnum<["all", "closed"]>>;
        }, "strip", import("zod").ZodTypeAny, {
            expanded?: "all" | "closed" | undefined;
        }, {
            expanded?: "all" | "closed" | undefined;
        }>>;
        playground: import("zod").ZodOptional<import("zod").ZodObject<{
            display: import("zod").ZodOptional<import("zod").ZodEnum<["interactive", "simple", "none"]>>;
            proxy: import("zod").ZodOptional<import("zod").ZodBoolean>;
        }, "strip", import("zod").ZodTypeAny, {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        }, {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        }>>;
        examples: import("zod").ZodOptional<import("zod").ZodObject<{
            defaults: import("zod").ZodOptional<import("zod").ZodEnum<["required", "all"]>>;
            languages: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodString, "many">>;
        }, "strip", import("zod").ZodTypeAny, {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        }, {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        }>>;
        mdx: import("zod").ZodOptional<import("zod").ZodObject<{
            auth: import("zod").ZodOptional<import("zod").ZodObject<{
                method: import("zod").ZodOptional<import("zod").ZodEnum<["bearer", "basic", "key", "cobo"]>>;
                name: import("zod").ZodOptional<import("zod").ZodString>;
            }, "strip", import("zod").ZodTypeAny, {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            }, {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            }>>;
            server: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodString, import("zod").ZodArray<import("zod").ZodString, "many">]>>;
        }, "strip", import("zod").ZodTypeAny, {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        }, {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        }>>;
    }, "strip", import("zod").ZodTypeAny, {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    }, {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    }>>;
    appearance: import("zod").ZodOptional<import("zod").ZodObject<{
        default: import("zod").ZodOptional<import("zod").ZodEnum<["system", "light", "dark"]>>;
        strict: import("zod").ZodOptional<import("zod").ZodBoolean>;
    }, "strip", import("zod").ZodTypeAny, {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    }, {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    }>>;
    background: import("zod").ZodOptional<import("zod").ZodObject<{
        image: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodString, import("zod").ZodObject<{
            light: import("zod").ZodString;
            dark: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            light: string;
            dark: string;
        }, {
            light: string;
            dark: string;
        }>]>>;
        decoration: import("zod").ZodOptional<import("zod").ZodEnum<["gradient", "grid", "windows"]>>;
        color: import("zod").ZodOptional<import("zod").ZodObject<{
            light: import("zod").ZodOptional<import("zod").ZodString>;
            dark: import("zod").ZodOptional<import("zod").ZodString>;
        }, "strict", import("zod").ZodTypeAny, {
            light?: string | undefined;
            dark?: string | undefined;
        }, {
            light?: string | undefined;
            dark?: string | undefined;
        }>>;
    }, "strip", import("zod").ZodTypeAny, {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    }, {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    }>>;
    navbar: import("zod").ZodOptional<import("zod").ZodObject<{
        links: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
            label: import("zod").ZodString;
            icon: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodObject<{
                style: import("zod").ZodOptional<import("zod").ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: import("zod").ZodEffects<import("zod").ZodString, string, string>;
                library: import("zod").ZodOptional<import("zod").ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", import("zod").ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            href: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }, {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }>, "many">>;
        primary: import("zod").ZodOptional<import("zod").ZodDiscriminatedUnion<"type", [import("zod").ZodObject<{
            type: import("zod").ZodLiteral<"button">;
            label: import("zod").ZodString;
            href: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            type: "button";
            href: string;
            label: string;
        }, {
            type: "button";
            href: string;
            label: string;
        }>, import("zod").ZodObject<{
            type: import("zod").ZodLiteral<"github">;
            href: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            type: "github";
            href: string;
        }, {
            type: "github";
            href: string;
        }>]>>;
    }, "strip", import("zod").ZodTypeAny, {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    }, {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    }>>;
    navigation: import("zod").ZodUnion<[import("zod").ZodObject<{
        global: import("zod").ZodOptional<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").GlobalNavigation, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").GlobalNavigation>>;
        languages: import("zod").ZodArray<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").LanguageNavigation<"default">, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").LanguageNavigation<"default">>, "many">;
    }, "strip", import("zod").ZodTypeAny, {
        languages: import("../../properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        languages: import("../../properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, import("zod").ZodObject<{
        global: import("zod").ZodOptional<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").GlobalNavigation, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").GlobalNavigation>>;
        versions: import("zod").ZodArray<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").VersionNavigation<"default">, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").VersionNavigation<"default">>, "many">;
    }, "strip", import("zod").ZodTypeAny, {
        versions: import("../../properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        versions: import("../../properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, import("zod").ZodObject<{
        global: import("zod").ZodOptional<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").GlobalNavigation, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").GlobalNavigation>>;
        tabs: import("zod").ZodArray<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").TabNavigation<"default">, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").TabNavigation<"default">>, "many">;
    }, "strip", import("zod").ZodTypeAny, {
        tabs: import("../../properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        tabs: import("../../properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, import("zod").ZodObject<{
        global: import("zod").ZodOptional<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").GlobalNavigation, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").GlobalNavigation>>;
        dropdowns: import("zod").ZodArray<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").DropdownNavigation<"default">, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").DropdownNavigation<"default">>, "many">;
    }, "strip", import("zod").ZodTypeAny, {
        dropdowns: import("../../properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        dropdowns: import("../../properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, import("zod").ZodObject<{
        global: import("zod").ZodOptional<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").GlobalNavigation, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").GlobalNavigation>>;
        anchors: import("zod").ZodArray<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").AnchorNavigation<"default">, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").AnchorNavigation<"default">>, "many">;
    }, "strip", import("zod").ZodTypeAny, {
        anchors: import("../../properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        anchors: import("../../properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, import("zod").ZodObject<{
        global: import("zod").ZodOptional<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").GlobalNavigation, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").GlobalNavigation>>;
        groups: import("zod").ZodArray<import("zod").ZodUnion<[import("zod").ZodObject<{
            icon: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodObject<{
                style: import("zod").ZodOptional<import("zod").ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: import("zod").ZodEffects<import("zod").ZodString, string, string>;
                library: import("zod").ZodOptional<import("zod").ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", import("zod").ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            group: import("zod").ZodString;
            tag: import("zod").ZodOptional<import("zod").ZodString>;
            hidden: import("zod").ZodOptional<import("zod").ZodBoolean>;
            root: import("zod").ZodOptional<import("zod").ZodEffects<import("zod").ZodString, string, string>>;
            openapi: import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodArray<import("zod").ZodEffects<import("zod").ZodString, string, string>, "many">, import("zod").ZodObject<{
                source: import("zod").ZodEffects<import("zod").ZodString, string, string>;
                directory: import("zod").ZodOptional<import("zod").ZodString>;
            }, "strict", import("zod").ZodTypeAny, {
                source: string;
                directory?: string | undefined;
            }, {
                source: string;
                directory?: string | undefined;
            }>]>;
        }, "strip", import("zod").ZodTypeAny, {
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }, {
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }>, import("zod").ZodObject<{
            icon: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodObject<{
                style: import("zod").ZodOptional<import("zod").ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: import("zod").ZodEffects<import("zod").ZodString, string, string>;
                library: import("zod").ZodOptional<import("zod").ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", import("zod").ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            group: import("zod").ZodString;
            tag: import("zod").ZodOptional<import("zod").ZodString>;
            hidden: import("zod").ZodOptional<import("zod").ZodBoolean>;
            root: import("zod").ZodOptional<import("zod").ZodEffects<import("zod").ZodString, string, string>>;
            asyncapi: import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodArray<import("zod").ZodEffects<import("zod").ZodString, string, string>, "many">, import("zod").ZodObject<{
                source: import("zod").ZodEffects<import("zod").ZodString, string, string>;
                directory: import("zod").ZodOptional<import("zod").ZodString>;
            }, "strict", import("zod").ZodTypeAny, {
                source: string;
                directory?: string | undefined;
            }, {
                source: string;
                directory?: string | undefined;
            }>]>;
        }, "strip", import("zod").ZodTypeAny, {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }, {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }>, import("zod").ZodObject<{
            icon: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodObject<{
                style: import("zod").ZodOptional<import("zod").ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: import("zod").ZodEffects<import("zod").ZodString, string, string>;
                library: import("zod").ZodOptional<import("zod").ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", import("zod").ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            group: import("zod").ZodString;
            tag: import("zod").ZodOptional<import("zod").ZodString>;
            hidden: import("zod").ZodOptional<import("zod").ZodBoolean>;
            root: import("zod").ZodOptional<import("zod").ZodEffects<import("zod").ZodString, string, string>>;
            pages: import("zod").ZodLazy<import("zod").ZodArray<import("zod").ZodType<any, import("zod").ZodTypeDef, any>, "many">>;
        }, "strip", import("zod").ZodTypeAny, {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }, {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        }>]>, "many">;
    }, "strip", import("zod").ZodTypeAny, {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>, import("zod").ZodObject<{
        global: import("zod").ZodOptional<import("zod").ZodType<import("../../properties/navigation/divisionNav.js").GlobalNavigation, import("zod").ZodTypeDef, import("../../properties/navigation/divisionNav.js").GlobalNavigation>>;
        pages: import("zod").ZodArray<import("zod").ZodType<any, import("zod").ZodTypeDef, any>, "many">;
    }, "strip", import("zod").ZodTypeAny, {
        pages: any[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }, {
        pages: any[];
        global?: import("../../properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }>]>;
    footer: import("zod").ZodOptional<import("zod").ZodObject<{
        socials: import("zod").ZodOptional<import("zod").ZodRecord<import("zod").ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>, import("zod").ZodString>>;
        links: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
            header: import("zod").ZodOptional<import("zod").ZodString>;
            items: import("zod").ZodArray<import("zod").ZodObject<{
                label: import("zod").ZodString;
                href: import("zod").ZodString;
            }, "strip", import("zod").ZodTypeAny, {
                href: string;
                label: string;
            }, {
                href: string;
                label: string;
            }>, "many">;
        }, "strip", import("zod").ZodTypeAny, {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }, {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }>, "many">>;
    }, "strip", import("zod").ZodTypeAny, {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    }, {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    }>>;
    search: import("zod").ZodOptional<import("zod").ZodObject<{
        prompt: import("zod").ZodOptional<import("zod").ZodString>;
    }, "strip", import("zod").ZodTypeAny, {
        prompt?: string | undefined;
    }, {
        prompt?: string | undefined;
    }>>;
    seo: import("zod").ZodOptional<import("zod").ZodObject<{
        metatags: import("zod").ZodOptional<import("zod").ZodOptional<import("zod").ZodRecord<import("zod").ZodString, import("zod").ZodString>>>;
        indexing: import("zod").ZodOptional<import("zod").ZodEnum<["navigable", "all"]>>;
    }, "strip", import("zod").ZodTypeAny, {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    }, {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    }>>;
    fonts: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodObject<{
        family: import("zod").ZodString;
        weight: import("zod").ZodOptional<import("zod").ZodNumber>;
        source: import("zod").ZodOptional<import("zod").ZodString>;
        format: import("zod").ZodOptional<import("zod").ZodEnum<["woff", "woff2"]>>;
    }, "strip", import("zod").ZodTypeAny, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, import("zod").ZodObject<{
        heading: import("zod").ZodOptional<import("zod").ZodEffects<import("zod").ZodObject<{
            family: import("zod").ZodString;
            weight: import("zod").ZodOptional<import("zod").ZodNumber>;
            source: import("zod").ZodOptional<import("zod").ZodString>;
            format: import("zod").ZodOptional<import("zod").ZodEnum<["woff", "woff2"]>>;
        }, "strip", import("zod").ZodTypeAny, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>>;
        body: import("zod").ZodOptional<import("zod").ZodEffects<import("zod").ZodObject<{
            family: import("zod").ZodString;
            weight: import("zod").ZodOptional<import("zod").ZodNumber>;
            source: import("zod").ZodOptional<import("zod").ZodString>;
            format: import("zod").ZodOptional<import("zod").ZodEnum<["woff", "woff2"]>>;
        }, "strip", import("zod").ZodTypeAny, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>>;
    }, "strict", import("zod").ZodTypeAny, {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    }, {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    }>]>>;
    icons: import("zod").ZodOptional<import("zod").ZodObject<{
        library: import("zod").ZodEnum<["fontawesome", "lucide"]>;
    }, "strip", import("zod").ZodTypeAny, {
        library: "fontawesome" | "lucide";
    }, {
        library: "fontawesome" | "lucide";
    }>>;
    styling: import("zod").ZodOptional<import("zod").ZodObject<{
        eyebrows: import("zod").ZodOptional<import("zod").ZodEnum<["section", "breadcrumbs"]>>;
        codeblocks: import("zod").ZodOptional<import("zod").ZodEnum<["system", "dark"]>>;
    }, "strip", import("zod").ZodTypeAny, {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    }, {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    }>>;
    redirects: import("zod").ZodOptional<import("zod").ZodEffects<import("zod").ZodArray<import("zod").ZodObject<{
        source: import("zod").ZodString;
        destination: import("zod").ZodString;
        permanent: import("zod").ZodOptional<import("zod").ZodBoolean>;
    }, "strip", import("zod").ZodTypeAny, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }>, "many">, {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[], {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[]>>;
    integrations: import("zod").ZodOptional<import("zod").ZodObject<{
        amplitude: import("zod").ZodOptional<import("zod").ZodObject<{
            apiKey: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            apiKey: string;
        }, {
            apiKey: string;
        }>>;
        clearbit: import("zod").ZodOptional<import("zod").ZodObject<{
            publicApiKey: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            publicApiKey: string;
        }, {
            publicApiKey: string;
        }>>;
        fathom: import("zod").ZodOptional<import("zod").ZodObject<{
            siteId: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            siteId: string;
        }, {
            siteId: string;
        }>>;
        frontchat: import("zod").ZodOptional<import("zod").ZodObject<{
            snippetId: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            snippetId: string;
        }, {
            snippetId: string;
        }>>;
        ga4: import("zod").ZodOptional<import("zod").ZodObject<{
            measurementId: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            measurementId: string;
        }, {
            measurementId: string;
        }>>;
        gtm: import("zod").ZodOptional<import("zod").ZodObject<{
            tagId: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            tagId: string;
        }, {
            tagId: string;
        }>>;
        heap: import("zod").ZodOptional<import("zod").ZodObject<{
            appId: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            appId: string;
        }, {
            appId: string;
        }>>;
        hotjar: import("zod").ZodOptional<import("zod").ZodObject<{
            hjid: import("zod").ZodString;
            hjsv: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            hjid: string;
            hjsv: string;
        }, {
            hjid: string;
            hjsv: string;
        }>>;
        intercom: import("zod").ZodOptional<import("zod").ZodObject<{
            appId: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            appId: string;
        }, {
            appId: string;
        }>>;
        koala: import("zod").ZodOptional<import("zod").ZodObject<{
            publicApiKey: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            publicApiKey: string;
        }, {
            publicApiKey: string;
        }>>;
        logrocket: import("zod").ZodOptional<import("zod").ZodObject<{
            appId: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            appId: string;
        }, {
            appId: string;
        }>>;
        mixpanel: import("zod").ZodOptional<import("zod").ZodObject<{
            projectToken: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            projectToken: string;
        }, {
            projectToken: string;
        }>>;
        osano: import("zod").ZodOptional<import("zod").ZodObject<{
            scriptSource: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            scriptSource: string;
        }, {
            scriptSource: string;
        }>>;
        pirsch: import("zod").ZodOptional<import("zod").ZodObject<{
            id: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            id: string;
        }, {
            id: string;
        }>>;
        posthog: import("zod").ZodOptional<import("zod").ZodObject<{
            apiKey: import("zod").ZodString;
            apiHost: import("zod").ZodOptional<import("zod").ZodString>;
        }, "strip", import("zod").ZodTypeAny, {
            apiKey: string;
            apiHost?: string | undefined;
        }, {
            apiKey: string;
            apiHost?: string | undefined;
        }>>;
        plausible: import("zod").ZodOptional<import("zod").ZodObject<{
            domain: import("zod").ZodEffects<import("zod").ZodString, string, string>;
            server: import("zod").ZodOptional<import("zod").ZodEffects<import("zod").ZodString, string, string>>;
        }, "strip", import("zod").ZodTypeAny, {
            domain: string;
            server?: string | undefined;
        }, {
            domain: string;
            server?: string | undefined;
        }>>;
        segment: import("zod").ZodOptional<import("zod").ZodObject<{
            key: import("zod").ZodString;
        }, "strip", import("zod").ZodTypeAny, {
            key: string;
        }, {
            key: string;
        }>>;
        telemetry: import("zod").ZodOptional<import("zod").ZodObject<{
            enabled: import("zod").ZodOptional<import("zod").ZodBoolean>;
        }, "strip", import("zod").ZodTypeAny, {
            enabled?: boolean | undefined;
        }, {
            enabled?: boolean | undefined;
        }>>;
        cookies: import("zod").ZodOptional<import("zod").ZodObject<{
            key: import("zod").ZodOptional<import("zod").ZodString>;
            value: import("zod").ZodOptional<import("zod").ZodString>;
        }, "strip", import("zod").ZodTypeAny, {
            key?: string | undefined;
            value?: string | undefined;
        }, {
            key?: string | undefined;
            value?: string | undefined;
        }>>;
    }, "strict", import("zod").ZodTypeAny, {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    }, {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    }>>;
    banner: import("zod").ZodOptional<import("zod").ZodObject<{
        content: import("zod").ZodString;
        dismissible: import("zod").ZodOptional<import("zod").ZodBoolean>;
    }, "strip", import("zod").ZodTypeAny, {
        content: string;
        dismissible?: boolean | undefined;
    }, {
        content: string;
        dismissible?: boolean | undefined;
    }>>;
    errors: import("zod").ZodOptional<import("zod").ZodObject<{
        '404': import("zod").ZodObject<{
            redirect: import("zod").ZodDefault<import("zod").ZodOptional<import("zod").ZodBoolean>>;
        }, "strip", import("zod").ZodTypeAny, {
            redirect: boolean;
        }, {
            redirect?: boolean | undefined;
        }>;
    }, "strip", import("zod").ZodTypeAny, {
        '404': {
            redirect: boolean;
        };
    }, {
        '404': {
            redirect?: boolean | undefined;
        };
    }>>;
    contextual: import("zod").ZodOptional<import("zod").ZodObject<{
        options: import("zod").ZodEffects<import("zod").ZodArray<import("zod").ZodUnion<[import("zod").ZodEnum<["copy", "view", "chatgpt", "claude", "perplexity"]>, import("zod").ZodObject<{
            title: import("zod").ZodString;
            description: import("zod").ZodString;
            icon: import("zod").ZodOptional<import("zod").ZodUnion<[import("zod").ZodEffects<import("zod").ZodString, string, string>, import("zod").ZodObject<{
                style: import("zod").ZodOptional<import("zod").ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
                name: import("zod").ZodEffects<import("zod").ZodString, string, string>;
                library: import("zod").ZodOptional<import("zod").ZodEnum<["fontawesome", "lucide"]>>;
            }, "strip", import("zod").ZodTypeAny, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }, {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            }>]>>;
            href: import("zod").ZodUnion<[import("zod").ZodString, import("zod").ZodObject<{
                base: import("zod").ZodString;
                query: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                    key: import("zod").ZodString;
                    value: import("zod").ZodString;
                }, "strip", import("zod").ZodTypeAny, {
                    value: string;
                    key: string;
                }, {
                    value: string;
                    key: string;
                }>, "many">>;
            }, "strip", import("zod").ZodTypeAny, {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }, {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }>]>;
        }, "strip", import("zod").ZodTypeAny, {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }, {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }>]>, "many">, ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[], ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[]>;
    }, "strip", import("zod").ZodTypeAny, {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    }, {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    }>>;
    thumbnails: import("zod").ZodOptional<import("zod").ZodObject<{
        appearance: import("zod").ZodOptional<import("zod").ZodEnum<[import("../../properties/thumbnails.js").ThumbnailAppearance.Light, import("../../properties/thumbnails.js").ThumbnailAppearance.Dark]>>;
        background: import("zod").ZodOptional<import("zod").ZodString>;
    }, "strip", import("zod").ZodTypeAny, {
        appearance?: import("../../properties/thumbnails.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    }, {
        appearance?: import("../../properties/thumbnails.js").ThumbnailAppearance | undefined;
        background?: string | undefined;
    }>>;
};
