import type { Root as HastRoot, Element } from 'hast';
import { visit, EXIT } from 'unist-util-visit';

import { framework } from '../utils/detectFramework.js';

export function retrieveRootNavElement(rootNode: HastRoot): Element | undefined {
  let rootTagName = 'aside';
  switch (framework.vendor) {
    case 'docusaurus':
      rootTagName = 'nav';
      break;
    case 'gitbook':
      rootTagName = 'aside';
      break;
    case 'readme':
      rootTagName = 'nav';
      break;
  }

  let rootSelector = 'page-no-toc:hidden';
  switch (framework.vendor) {
    case 'docusaurus':
      rootSelector = 'menu';
      break;
    case 'gitbook':
      rootSelector = 'page-no-toc:lg:hidden';
      break;
    case 'readme':
      rootSelector = 'rm-Sidebar';
      break;
  }

  let element: Element | undefined = undefined;
  visit(rootNode, 'element', function (node) {
    if (
      node.tagName === rootTagName &&
      node.properties.className &&
      Array.isArray(node.properties.className) &&
      node.properties.className.includes(rootSelector)
    ) {
      element = node;
      return EXIT;
    }
  });

  return element;
}
