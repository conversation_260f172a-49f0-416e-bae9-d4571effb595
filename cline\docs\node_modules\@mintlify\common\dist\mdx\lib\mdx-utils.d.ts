import { MdxJsxAttribute, MdxJsxExpressionAttribute, MdxJsxFlowElementHast } from 'mdast-util-mdx-jsx';
import { Node } from 'unist';
export declare const isMdxJsxFlowElementHast: (node: Node) => node is MdxJsxFlowElementHast;
export declare const isMdxJsxAttribute: (attribute: MdxJsxAttribute | MdxJsxExpressionAttribute) => attribute is MdxJsxAttribute;
export declare const paramFieldLocations: readonly ["body", "query", "auth", "cookie", "path", "header"];
export type ParamFieldLocation = (typeof paramFieldLocations)[number];
export type FieldLocation = ParamFieldLocation | 'response';
export declare const isParamFieldLocation: (str: string | undefined) => str is ParamFieldLocation;
