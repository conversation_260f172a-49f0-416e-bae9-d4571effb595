var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import _ from 'lodash';
import { updateNavigationToDocsConfig } from './updateNavigationToDocsConfig.js';
function updateEyebrows(config) {
    var _a;
    if (((_a = config.eyebrow) === null || _a === void 0 ? void 0 : _a.display) == undefined) {
        return undefined;
    }
    return config.eyebrow.display === 'breadcrumbs' ? 'breadcrumbs' : 'section';
}
function updateCodeblocks(config) {
    var _a;
    if (((_a = config.codeBlock) === null || _a === void 0 ? void 0 : _a.mode) == undefined) {
        return undefined;
    }
    return config.codeBlock.mode === 'dark' ? 'dark' : 'system';
}
function updateApiPlayground(config) {
    var _a, _b, _c, _d;
    const oldMode = (_b = (_a = config.api) === null || _a === void 0 ? void 0 : _a.playground) === null || _b === void 0 ? void 0 : _b.mode;
    const disableProxy = (_d = (_c = config.api) === null || _c === void 0 ? void 0 : _c.playground) === null || _d === void 0 ? void 0 : _d.disableProxy;
    if (!oldMode && disableProxy === undefined)
        return undefined;
    const display = (() => {
        switch (oldMode) {
            case 'show':
                return 'interactive';
            case 'hide':
                return 'none';
            case undefined:
                return undefined;
            default:
                return oldMode;
        }
    })();
    const proxy = disableProxy == undefined ? undefined : !disableProxy;
    return { display, proxy };
}
function updateTopbarPrimary(config) {
    const ctaButton = config.topbarCtaButton;
    if (!ctaButton)
        return undefined;
    if (ctaButton.type === undefined || ctaButton.type === 'link') {
        return {
            type: 'button',
            label: ctaButton.name,
            href: ctaButton.url,
        };
    }
    return {
        type: 'github',
        href: ctaButton.url,
    };
}
function updateFont(config) {
    const font = config.font;
    const result = {};
    if (!font)
        return undefined;
    if ('family' in font) {
        return transformFontDetails(font);
    }
    function transformFontDetails(details) {
        const { url } = details, rest = __rest(details, ["url"]);
        return Object.assign(Object.assign({}, rest), (url ? { source: url } : {}));
    }
    if (font.headings) {
        result.heading = transformFontDetails(font.headings);
    }
    if (font.body) {
        result.body = transformFontDetails(font.body);
    }
    return result;
}
function updateFooterSocials(config) {
    var _a, _b, _c;
    if (!((_a = config.footer) === null || _a === void 0 ? void 0 : _a.socials) && !config.footerSocials)
        return undefined;
    const footerSocials = (_c = (_b = config.footer) === null || _b === void 0 ? void 0 : _b.socials) !== null && _c !== void 0 ? _c : config.footerSocials;
    if (Array.isArray(footerSocials)) {
        return footerSocials.reduce((acc, social) => {
            acc[social.type] = social.url;
            return acc;
        }, {});
    }
    return footerSocials;
}
/**
 * default = mint
 * venus = palm
 * quill = willow
 * prism = mint
 * prism = layout:sidenav = maple
 * layout:solidSidenav = willow
 */
function upgradeOptionalConfig(config, opt) {
    let theme;
    if (opt === null || opt === void 0 ? void 0 : opt.shouldUpgradeTheme) {
        if (config.theme) {
            switch (config.theme) {
                case 'venus':
                    theme = 'palm';
                    break;
                case 'quill':
                    theme = 'willow';
                    break;
                default:
                    theme = 'mint';
            }
        }
        if (config.layout === 'sidenav') {
            theme = 'maple';
        }
        else if (config.layout === 'solidSidenav') {
            theme = 'willow';
        }
    }
    return {
        theme: theme !== null && theme !== void 0 ? theme : 'mint',
    };
}
export function upgradeToDocsConfig(config, opt) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2;
    const fonts = updateFont(config);
    const playground = updateApiPlayground(config);
    const eyebrows = updateEyebrows(config);
    const codeblocks = updateCodeblocks(config);
    const optionalConfig = upgradeOptionalConfig(config, opt);
    const v2Config = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({ $schema: 'https://mintlify.com/docs.json' }, optionalConfig), { name: config.name }), (config.colors
        ? {
            colors: {
                primary: config.colors.primary,
                light: config.colors.light,
                dark: config.colors.dark,
            },
        }
        : {
            colors: {
                primary: '#16A34A',
            },
        })), { favicon: config.favicon, navigation: updateNavigationToDocsConfig(config) }), (eyebrows || codeblocks
        ? {
            styling: {
                eyebrows,
                codeblocks,
            },
        }
        : {})), { logo: config.logo }), (playground ||
        ((_c = (_b = (_a = config.api) === null || _a === void 0 ? void 0 : _a.request) === null || _b === void 0 ? void 0 : _b.example) === null || _c === void 0 ? void 0 : _c.languages) ||
        ((_d = config.api) === null || _d === void 0 ? void 0 : _d.baseUrl) ||
        ((_f = (_e = config.api) === null || _e === void 0 ? void 0 : _e.auth) === null || _f === void 0 ? void 0 : _f.method) ||
        config.openapi
        ? {
            api: Object.assign(Object.assign(Object.assign(Object.assign({}, (config.openapi ? { openapi: config.openapi } : {})), (playground
                ? {
                    playground: Object.assign(Object.assign({}, (playground.display ? { display: playground.display } : {})), (playground.proxy !== undefined ? { proxy: playground.proxy } : {})),
                }
                : {})), (((_j = (_h = (_g = config.api) === null || _g === void 0 ? void 0 : _g.request) === null || _h === void 0 ? void 0 : _h.example) === null || _j === void 0 ? void 0 : _j.languages)
                ? {
                    examples: {
                        languages: config.api.request.example.languages,
                    },
                }
                : {})), (((_k = config.api) === null || _k === void 0 ? void 0 : _k.baseUrl) || ((_m = (_l = config.api) === null || _l === void 0 ? void 0 : _l.auth) === null || _m === void 0 ? void 0 : _m.method) || ((_p = (_o = config.api) === null || _o === void 0 ? void 0 : _o.auth) === null || _p === void 0 ? void 0 : _p.name)
                ? {
                    mdx: Object.assign({ server: config.api.baseUrl }, (((_q = config.api.auth) === null || _q === void 0 ? void 0 : _q.method) || ((_r = config.api.auth) === null || _r === void 0 ? void 0 : _r.name)
                        ? {
                            auth: {
                                method: config.api.auth.method,
                                name: config.api.auth.name,
                            },
                        }
                        : {})),
                }
                : {})),
        }
        : {})), (((_s = config.modeToggle) === null || _s === void 0 ? void 0 : _s.default) || ((_t = config.modeToggle) === null || _t === void 0 ? void 0 : _t.isHidden) != undefined
        ? {
            appearance: {
                default: config.modeToggle.default,
                strict: config.modeToggle.isHidden,
            },
        }
        : {})), (config.background || config.backgroundImage || (config.colors && config.colors.background)
        ? {
            background: Object.assign(Object.assign(Object.assign({}, (config.backgroundImage ? { image: config.backgroundImage } : {})), { decoration: (_u = config.background) === null || _u === void 0 ? void 0 : _u.style }), (config.colors && config.colors.background
                ? {
                    color: {
                        light: config.colors.background.light,
                        dark: config.colors.background.dark,
                    },
                }
                : {})),
        }
        : {})), (config.topbarLinks || config.topbarCtaButton
        ? {
            navbar: Object.assign(Object.assign({}, (config.topbarLinks
                ? {
                    links: config.topbarLinks.map((item) => {
                        if (item.type === 'link' || item.type === undefined) {
                            return {
                                label: item.name,
                                href: item.url,
                            };
                        }
                        else {
                            return {
                                label: _.capitalize(item.type),
                                href: item.url,
                            };
                        }
                    }),
                }
                : {})), { primary: updateTopbarPrimary(config) }),
        }
        : {})), (((_v = config.search) === null || _v === void 0 ? void 0 : _v.prompt)
        ? {
            search: {
                prompt: config.search.prompt,
            },
        }
        : {})), (config.metadata || ((_w = config.seo) === null || _w === void 0 ? void 0 : _w.indexHiddenPages) !== undefined
        ? {
            seo: Object.assign(Object.assign({}, (config.metadata ? { metatags: config.metadata } : {})), { indexing: ((_x = config.seo) === null || _x === void 0 ? void 0 : _x.indexHiddenPages) ? 'all' : 'navigable' }),
        }
        : {})), (config.footer || config.footerSocials
        ? {
            footer: Object.assign(Object.assign({}, (((_y = config.footer) === null || _y === void 0 ? void 0 : _y.socials) || config.footerSocials
                ? { socials: updateFooterSocials(config) }
                : {})), (((_z = config.footer) === null || _z === void 0 ? void 0 : _z.links)
                ? {
                    links: config.footer.links.map((group) => {
                        return {
                            header: group.title,
                            items: group.links.map((link) => {
                                return {
                                    label: link.label,
                                    href: link.url,
                                };
                            }),
                        };
                    }),
                }
                : {})),
        }
        : {})), (config.integrations || config.analytics
        ? {
            integrations: Object.assign(Object.assign(Object.assign(Object.assign({}, config.analytics), (((_0 = config.integrations) === null || _0 === void 0 ? void 0 : _0.intercom)
                ? {
                    intercom: {
                        appId: config.integrations.intercom,
                    },
                }
                : {})), (((_1 = config.integrations) === null || _1 === void 0 ? void 0 : _1.frontchat)
                ? {
                    frontchat: {
                        snippetId: config.integrations.frontchat,
                    },
                }
                : {})), (((_2 = config.integrations) === null || _2 === void 0 ? void 0 : _2.osano)
                ? {
                    osano: {
                        scriptSource: config.integrations.osano,
                    },
                }
                : {})),
        }
        : {})), (fonts ? { fonts } : {})), (config.redirects ? { redirects: config.redirects } : {}));
    return v2Config;
}
