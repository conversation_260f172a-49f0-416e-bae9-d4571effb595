import { z } from 'zod';
const intercomSchema = z.string().min(6, 'Must be a valid Intercom app ID');
const frontchatSchema = z.string().min(6, 'Must be a valid Front chat snippet id');
const osanoSchema = z
    .string()
    .startsWith('https://cmp.osano.com/', 'A valid Osano script source must start with https://cmp.osano.com/')
    .endsWith('/osano.js', 'A valid Osano script source must end with /osano.js');
export const integrationsSchema = z.object({
    intercom: intercomSchema.optional(),
    frontchat: frontchatSchema.optional(),
    osano: osanoSchema.optional(),
});
