{"version": 3, "file": "replacer.js", "sourceRoot": "", "sources": ["../../src/utils/replacer.ts"], "names": [], "mappings": ";;;;AACA,2EAAgC;AAIhC,MAAa,QAAQ;IAInB,YAAY,KAAa;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAEpF,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACtB,CAAC;IAEM,WAAW,CAAC,IAAY,EAAE,MAAsB;QACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,KAAa,EAAE,MAAS;QACnC,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE;YAE7D,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YAE5C,IAAI,cAAc,EAAE;gBAClB,OAAO,MAAM,CACX,IAAA,qBAAK,EAAC,UAAU,EAAE;oBAChB,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE;wBAC3D,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC5B,OAAO,GAAG,CAAC;oBACb,CAAC,EAAE,EAAE,CAAC;oBACN,GAAG,MAAM;iBACV,CAAC,CACH,CAAC;aACH;YAED,IAAI,CAAC,CAAC,UAAU,IAAI,MAAM,CAAC,EAAE;gBAC3B,OAAO,EAAE,CAAC;aACX;YAGD,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAvCD,4BAuCC"}