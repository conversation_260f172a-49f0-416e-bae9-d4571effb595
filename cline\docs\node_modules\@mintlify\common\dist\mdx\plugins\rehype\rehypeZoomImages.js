import { u } from 'unist-builder';
import { visit } from 'unist-util-visit';
export const rehypeZoomImages = () => (tree) => {
    visit(tree, 'mdxJsxFlowElement', (node, index, parent) => {
        if (node.name === 'img' || node.name === 'picture' || node.name === 'figure') {
            const noZoom = node.attributes.find((attr) => attr.type === 'mdxJsxAttribute' && attr.name === 'noZoom');
            if (!noZoom && parent && index != null) {
                parent.children.splice(index, 1, u('element', {
                    tagName: 'ZoomImage',
                    properties: {},
                }, [node]));
            }
        }
    });
};
