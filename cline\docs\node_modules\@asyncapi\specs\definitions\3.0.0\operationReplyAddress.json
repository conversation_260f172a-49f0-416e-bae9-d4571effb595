{"type": "object", "description": "An object that specifies where an operation has to send the reply", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "required": ["location"], "properties": {"location": {"type": "string", "description": "A runtime expression that specifies the location of the reply address.", "pattern": "^\\$message\\.(header|payload)#(\\/(([^\\/~])|(~[01]))*)*"}, "description": {"type": "string", "description": "An optional description of the address. CommonMark is allowed."}}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/operationReplyAddress.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/operationReplyAddress.json"}