import { getArrayExpressionStringProperties } from '../../utils.js';
export function remarkExtractChangelogFilters(mdxExtracts) {
    return (tree) => {
        const tagCounts = new Map();
        for (let nodeIndex = 0; nodeIndex < tree.children.length; nodeIndex++) {
            const node = tree.children[nodeIndex];
            if (!node || node.type !== 'mdxJsxFlowElement' || node.name !== 'Update') {
                continue;
            }
            const tagsAttribute = node.attributes.find((attr) => 'name' in attr && attr.name === 'tags');
            if (!tagsAttribute || !tagsAttribute.value || typeof tagsAttribute.value !== 'object') {
                continue;
            }
            let tags = [];
            try {
                tags = JSON.parse(tagsAttribute.value.value);
            }
            catch (_a) {
                tags = getArrayExpressionStringProperties(tagsAttribute);
            }
            if (!Array.isArray(tags)) {
                continue;
            }
            tags.forEach((tag) => {
                if (!!tag.trim()) {
                    tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
                }
            });
        }
        const filters = Array.from(tagCounts.entries())
            .map(([tag, count]) => ({
            tag,
            count,
        }))
            .sort((a, b) => b.count - a.count);
        if (mdxExtracts && filters.length) {
            mdxExtracts.changelogFilters = filters;
        }
    };
}
