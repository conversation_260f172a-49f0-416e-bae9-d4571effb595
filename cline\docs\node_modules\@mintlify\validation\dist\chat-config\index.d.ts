import { z } from 'zod';
import { FooterSchema } from './footer.js';
import { heroSchema, HeroSchema } from './hero.js';
export declare const chatConfigSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    favicon: z.ZodOptional<z.ZodU<PERSON>n<[z.ZodString, z.ZodObject<{
        light: z.ZodString;
        dark: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        light: string;
        dark: string;
    }, {
        light: string;
        dark: string;
    }>]>>;
    logo: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
        light: z.ZodString;
        dark: z.ZodString;
        href: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodType<PERSON>ny, {
        light: string;
        dark: string;
        href?: string | undefined;
    }, {
        light: string;
        dark: string;
        href?: string | undefined;
    }>]>>;
    hero: z.<PERSON>ptional<z.ZodObject<{
        prompt: z.Zod<PERSON>ptional<z.ZodString>;
        examples: z.ZodOptional<z.ZodU<PERSON><[z.ZodArray<z.ZodString, "many">, z.ZodArray<z.ZodObject<{
            icon: z.ZodOptional<z.ZodString>;
            title: z.ZodString;
            prompt: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            prompt: string;
            title: string;
            icon?: string | undefined;
        }, {
            prompt: string;
            title: string;
            icon?: string | undefined;
        }>, "many">]>>;
    }, "strip", z.ZodTypeAny, {
        prompt?: string | undefined;
        examples?: string[] | {
            prompt: string;
            title: string;
            icon?: string | undefined;
        }[] | undefined;
    }, {
        prompt?: string | undefined;
        examples?: string[] | {
            prompt: string;
            title: string;
            icon?: string | undefined;
        }[] | undefined;
    }>>;
    colors: z.ZodEffects<z.ZodOptional<z.ZodObject<{
        primary: z.ZodString;
        light: z.ZodOptional<z.ZodString>;
        dark: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    }>>, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined>;
    fonts: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodObject<{
        family: z.ZodString;
        weight: z.ZodOptional<z.ZodNumber>;
        source: z.ZodOptional<z.ZodString>;
        format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
    }, "strip", z.ZodTypeAny, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, z.ZodObject<{
        heading: z.ZodOptional<z.ZodEffects<z.ZodObject<{
            family: z.ZodString;
            weight: z.ZodOptional<z.ZodNumber>;
            source: z.ZodOptional<z.ZodString>;
            format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
        }, "strip", z.ZodTypeAny, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>>;
        body: z.ZodOptional<z.ZodEffects<z.ZodObject<{
            family: z.ZodString;
            weight: z.ZodOptional<z.ZodNumber>;
            source: z.ZodOptional<z.ZodString>;
            format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
        }, "strip", z.ZodTypeAny, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }, {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        }>>;
    }, "strict", z.ZodTypeAny, {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    }, {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    }>]>>;
    appearance: z.ZodOptional<z.ZodObject<{
        default: z.ZodOptional<z.ZodEnum<["system", "light", "dark"]>>;
        strict: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    }, {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    }>>;
    footer: z.ZodOptional<z.ZodObject<{
        href: z.ZodString;
        message: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        message: string;
        href: string;
    }, {
        message: string;
        href: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    name?: string | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    hero?: {
        prompt?: string | undefined;
        examples?: string[] | {
            prompt: string;
            title: string;
            icon?: string | undefined;
        }[] | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    footer?: {
        message: string;
        href: string;
    } | undefined;
}, {
    name?: string | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    hero?: {
        prompt?: string | undefined;
        examples?: string[] | {
            prompt: string;
            title: string;
            icon?: string | undefined;
        }[] | undefined;
    } | undefined;
    colors?: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    footer?: {
        message: string;
        href: string;
    } | undefined;
}>;
export type ChatProjectConfigSchema = z.infer<typeof chatConfigSchema>;
export { heroSchema, type HeroSchema, type FooterSchema };
