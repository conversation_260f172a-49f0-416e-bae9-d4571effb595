import { z } from 'zod';
import { VersionNavigation } from './divisionNav.js';
export declare const baseVersionSchema: z.ZodObject<{
    version: z.ZodString;
    default: z.ZodOptional<z.ZodBoolean>;
    hidden: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    version: string;
    default?: boolean | undefined;
    hidden?: boolean | undefined;
}, {
    version: string;
    default?: boolean | undefined;
    hidden?: boolean | undefined;
}>;
export type BaseVersionSchema = z.infer<typeof baseVersionSchema>;
export declare const nonRecursiveVersionSchema: z.ZodObject<{
    version: z.ZodString;
    default: z.ZodOptional<z.ZodBoolean>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    href: z.ZodString;
}, "strip", z.ZodTypeAny, {
    version: string;
    href: string;
    default?: boolean | undefined;
    hidden?: boolean | undefined;
}, {
    version: string;
    href: string;
    default?: boolean | undefined;
    hidden?: boolean | undefined;
}>;
export declare const versionSchema: z.ZodType<VersionNavigation<'default'>>;
export declare const decoratedVersionSchema: z.ZodType<VersionNavigation<'decorated'>>;
export declare const versionsSchema: z.ZodArray<z.ZodType<VersionNavigation<"default">, z.ZodTypeDef, VersionNavigation<"default">>, "many">;
export declare const decoratedVersionsSchema: z.ZodArray<z.ZodType<VersionNavigation<"decorated">, z.ZodTypeDef, VersionNavigation<"decorated">>, "many">;
export type VersionsConfig = z.infer<typeof versionsSchema>;
export type VersionConfig = z.infer<typeof versionSchema>;
export type DecoratedVersionConfig = z.infer<typeof decoratedVersionSchema>;
export type DecoratedVersionsConfig = z.infer<typeof decoratedVersionsSchema>;
