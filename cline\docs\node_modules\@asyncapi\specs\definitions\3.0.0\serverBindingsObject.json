{"type": "object", "description": "Map describing protocol-specific definitions for a server.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"http": {}, "ws": {}, "amqp": {}, "amqp1": {}, "mqtt": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/mqtt/0.2.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/mqtt/0.2.0/server.json"}}]}, "kafka": {"properties": {"bindingVersion": {"enum": ["0.5.0", "0.4.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/kafka/0.5.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.5.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/kafka/0.5.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/kafka/0.4.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/kafka/0.3.0/server.json"}}]}, "anypointmq": {}, "nats": {}, "jms": {"properties": {"bindingVersion": {"enum": ["0.0.1"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/jms/0.0.1/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.0.1"}}}, "then": {"$ref": "http://asyncapi.com/bindings/jms/0.0.1/server.json"}}]}, "sns": {}, "sqs": {}, "stomp": {}, "redis": {}, "ibmmq": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/ibmmq/0.1.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/ibmmq/0.1.0/server.json"}}]}, "solace": {"properties": {"bindingVersion": {"enum": ["0.4.0", "0.3.0", "0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/solace/0.4.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/solace/0.4.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/solace/0.3.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/solace/0.2.0/server.json"}}]}, "googlepubsub": {}, "pulsar": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/pulsar/0.1.0/server.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/pulsar/0.1.0/server.json"}}]}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/serverBindingsObject.json"}