import { z } from 'zod';
export const stylingSchema = z
    .object({
    eyebrows: z
        .enum(['section', 'breadcrumbs'])
        .optional()
        .describe('The eyebrows style of the content. Defaults to `section`.'),
    codeblocks: z
        .enum(['system', 'dark'])
        .optional()
        .describe('The codeblock theme. Defaults to `system`.'),
})
    .describe('Styling configurations');
