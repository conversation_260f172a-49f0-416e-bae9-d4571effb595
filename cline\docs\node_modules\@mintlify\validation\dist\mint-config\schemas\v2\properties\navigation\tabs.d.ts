import { z } from 'zod';
import { TabNavigation } from './divisionNav.js';
export declare const baseTabSchema: z.ZodObject<{
    tab: z.ZodString;
    icon: z.ZodOptional<z.<PERSON><PERSON>n<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.<PERSON>ptional<z.<PERSON>od<PERSON><["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.<PERSON>odEffects<z.ZodString, string, string>;
        library: z.<PERSON>od<PERSON>ptional<z.Zod<PERSON>num<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    tab: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
}, {
    tab: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
}>;
export type BaseTabSchema = z.infer<typeof baseTabSchema>;
export declare const nonRecursiveTabSchema: z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    tab: z.ZodString;
    href: z.ZodString;
}, "strip", z.ZodTypeAny, {
    href: string;
    tab: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
}, {
    href: string;
    tab: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    hidden?: boolean | undefined;
}>;
export declare const tabSchema: z.ZodType<TabNavigation<'default'>>;
export declare const decoratedTabSchema: z.ZodType<TabNavigation<'decorated'>>;
export declare const tabsSchema: z.ZodArray<z.ZodType<TabNavigation<"default">, z.ZodTypeDef, TabNavigation<"default">>, "many">;
export declare const decoratedTabsSchema: z.ZodArray<z.ZodType<TabNavigation<"decorated">, z.ZodTypeDef, TabNavigation<"decorated">>, "many">;
export type TabsConfig = z.infer<typeof tabsSchema>;
export type TabConfig = z.infer<typeof tabSchema>;
export type DecoratedTabsConfig = z.infer<typeof decoratedTabsSchema>;
export type DecoratedTabConfig = z.infer<typeof decoratedTabSchema>;
