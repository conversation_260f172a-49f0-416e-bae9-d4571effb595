export type TotalMetric = {
    title: string;
    value: number;
    delta: number;
};
export type PrimaryChart = {
    date: string;
    Visitors: number;
    Views: number;
    Actions: number;
}[];
export type PopularPages = {
    name: string;
    value: number;
}[];
export type Referrals = {
    name: string;
    value: number;
}[];
export type Feedback = {
    title: string;
    thumbsUpCount: number;
    thumbsDownCount: number;
    delta: number;
}[];
export type DashboardAnalytics = {
    total: {
        Visitors: TotalMetric;
        Views: TotalMetric;
        Actions: TotalMetric;
    };
    primaryChart: PrimaryChart;
    pages: PopularPages;
    referrals: Referrals;
    feedback: Feedback;
};
