{"version": 3, "file": "scalarInference.js", "sourceRoot": "", "sources": ["../../src/scalarInference.ts"], "names": [], "mappings": ";;AAEA,SAAgB,gBAAgB,CAAC,KAAa;IAC1C,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAClD,OAAO,IAAI,CAAC;KACf;SACI,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1D,OAAO,KAAK,CAAC;KAChB;IACD,MAAM,oBAAoB,KAAK,GAAG,CAAA;AACtC,CAAC;AARD,4CAQC;AAED,SAAS,oBAAoB,CAAC,KAAa;IAEvC,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE;QAClC,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;KACzC;IAED,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,SAAgB,gBAAgB,CAAC,KAAa;IAC1C,MAAM,MAAM,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAA;IAE1C,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QACtB,MAAM,oBAAoB,KAAK,GAAG,CAAA;KACrC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AARD,4CAQC;AAED,SAAgB,mBAAmB,CAAC,KAAa;IAC7C,MAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAA;IAEtC,IAAI,MAAM,GAAG,MAAM,CAAC,gBAAgB,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACvE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;KACxB;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AARD,kDAQC;AAED,SAAgB,cAAc,CAAC,KAAa;IAExC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAClD,OAAO,GAAG,CAAC;KACd;IAED,MAAM,QAAQ,GAAG,gCAAgC,CAAA;IACjD,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAClC,IAAI,KAAK,EAAE;QACP,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;KACpD;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;IAEhC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QAChB,OAAO,MAAM,CAAC;KACjB;IAED,MAAM,kBAAkB,KAAK,GAAG,CAAA;AACpC,CAAC;AAnBD,wCAmBC;AAED,IAAY,UAEX;AAFD,WAAY,UAAU;IAClB,2CAAI,CAAA;IAAE,2CAAI,CAAA;IAAE,yCAAG,CAAA;IAAE,6CAAK,CAAA;IAAE,+CAAM,CAAA;AAClC,CAAC,EAFW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAErB;AAKD,SAAgB,mBAAmB,CAAC,IAAgB;IAChD,IAAI,IAAI,KAAK,SAAS,EAAE;QACpB,OAAO,UAAU,CAAC,IAAI,CAAC;KAC1B;IAED,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE;QAChE,OAAO,UAAU,CAAC,MAAM,CAAA;KAC3B;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IAEzB,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACvD,OAAO,UAAU,CAAC,IAAI,CAAC;KAC1B;IAED,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;QACvC,OAAO,UAAU,CAAC,IAAI,CAAC;KAC1B;IAED,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACzE,OAAO,UAAU,CAAC,IAAI,CAAC;KAC1B;IAED,MAAM,MAAM,GAAG,eAAe,CAAA;IAC9B,MAAM,KAAK,GAAG,YAAY,CAAA;IAC1B,MAAM,MAAM,GAAG,kBAAkB,CAAA;IAEjC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC/D,OAAO,UAAU,CAAC,GAAG,CAAC;KACzB;IAED,MAAM,KAAK,GAAG,uDAAuD,CAAA;IACrE,MAAM,QAAQ,GAAG,4BAA4B,CAAA;IAC7C,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3F,OAAO,UAAU,CAAC,KAAK,CAAC;KAC3B;IAED,OAAO,UAAU,CAAC,MAAM,CAAC;AAC7B,CAAC;AAtCD,kDAsCC"}