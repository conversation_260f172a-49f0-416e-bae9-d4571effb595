import type { Analytics } from './analytics.js';
import type { <PERSON><PERSON>, TopAnchor } from './anchor.js';
import type { ApiConfig } from './apiConfig.js';
import type { Background } from './background.js';
import type { CodeBlockType } from './codeBlock.js';
import type { Colors } from './colors.js';
import type { CtaButton } from './ctaButton.js';
import type { Eyebrow } from './eyebrow.js';
import type { FontConfigType } from './font.js';
import type { Footer, FooterSocials } from './footer.js';
import type { Layout } from './layout.js';
import type { Logo } from './logo.js';
import type { Integrations } from './mintConfigIntegrations.js';
import type { Navigation } from './navigation.js';
import type { Rounded } from './rounded.js';
import type { SEO } from './seo.js';
import type { Sidebar } from './sidebar.js';
import type { PrimaryTab, Tab } from './tab.js';
import type { ThemeType } from './theme.js';
import type { Topbar } from './topbar.js';
import type { VersionType } from './version.js';
export type ModeToggleType = {
    default?: 'light' | 'dark';
    isHidden?: boolean;
};
export type MetadataType = {
    [key: string]: string;
};
export type FeedbackType = {
    thumbsRating?: boolean;
    suggestEdit?: boolean;
    raiseIssue?: boolean;
};
export type SearchType = {
    location?: 'side' | 'top';
    prompt?: string;
};
export type RedirectType = {
    source: string;
    destination: string;
    permanent?: boolean;
};
export type MintConfigType = {
    $schema: string;
    mintlify?: string;
    name: string;
    logo?: Logo;
    favicon: string;
    theme?: ThemeType;
    layout?: Layout;
    openapi?: string | string[];
    api?: ApiConfig;
    modeToggle?: ModeToggleType;
    versions?: VersionType[];
    metadata?: MetadataType;
    codeBlock?: CodeBlockType;
    colors: Colors;
    eyebrow?: Eyebrow;
    sidebar?: Sidebar;
    topbar?: Topbar;
    rounded?: Rounded;
    topbarCtaButton?: CtaButton;
    topbarLinks?: CtaButton[];
    navigation: Navigation;
    primaryTab?: PrimaryTab;
    topAnchor?: TopAnchor;
    anchors?: Anchor[];
    tabs?: Tab[];
    footer?: Footer;
    background?: Background;
    backgroundImage?: string;
    font?: FontConfigType;
    feedback?: FeedbackType;
    analytics?: Analytics;
    integrations?: Integrations;
    isWhiteLabeled?: boolean;
    search?: SearchType;
    redirects?: RedirectType[];
    seo?: SEO;
    footerSocials?: FooterSocials;
};
