{"type": "object", "additionalProperties": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/parameter.json"}]}, "description": "JSON objects describing re-usable channel parameters.", "example": {"$ref": "http://asyncapi.com/examples/3.0.0/parameters.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/parameters.json"}