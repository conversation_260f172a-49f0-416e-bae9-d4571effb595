import { z } from 'zod';
export declare const primarySchema: z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
    type: z.ZodLiteral<"button">;
    label: z.ZodString;
    href: z.ZodString;
}, "strip", z.<PERSON>ype<PERSON>ny, {
    type: "button";
    href: string;
    label: string;
}, {
    type: "button";
    href: string;
    label: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"github">;
    href: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "github";
    href: string;
}, {
    type: "github";
    href: string;
}>]>;
export declare const navbarSchema: z.ZodObject<{
    links: z.ZodOptional<z.ZodArray<z.ZodObject<{
        label: z.ZodString;
        icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
            style: z.<PERSON>ptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
            name: z.ZodEffects<z.ZodString, string, string>;
            library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }>]>>;
        href: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        href: string;
        label: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    }, {
        href: string;
        label: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    }>, "many">>;
    primary: z.ZodOptional<z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
        type: z.ZodLiteral<"button">;
        label: z.ZodString;
        href: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        type: "button";
        href: string;
        label: string;
    }, {
        type: "button";
        href: string;
        label: string;
    }>, z.ZodObject<{
        type: z.ZodLiteral<"github">;
        href: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        type: "github";
        href: string;
    }, {
        type: "github";
        href: string;
    }>]>>;
}, "strip", z.ZodTypeAny, {
    links?: {
        href: string;
        label: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    }[] | undefined;
    primary?: {
        type: "button";
        href: string;
        label: string;
    } | {
        type: "github";
        href: string;
    } | undefined;
}, {
    links?: {
        href: string;
        label: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    }[] | undefined;
    primary?: {
        type: "button";
        href: string;
        label: string;
    } | {
        type: "github";
        href: string;
    } | undefined;
}>;
