export type ApiConfig = {
    baseUrl?: string | string[];
    auth?: AuthType;
    playground?: PlaygroundSettings;
    request?: RequestSettings;
    maintainOrder?: boolean;
    paramFields?: ApiParamFields;
};
export type AuthType = {
    method?: 'bearer' | 'basic' | 'key' | 'cobo';
    name?: string;
    inputPrefix?: string;
};
export type PlaygroundSettings = {
    mode: 'show' | 'simple' | 'hide';
    disableProxy?: boolean;
};
export type RequestSettings = {
    example?: {
        showOptionalParams?: boolean;
        languages?: RequestExampleLanguages;
    };
};
export declare const apiParamFieldOptions: readonly ["all", "topLevel", "topLevelOneOfs", "none"];
export type ApiParamFieldOption = (typeof apiParamFieldOptions)[number];
export type ApiParamFields = {
    expanded?: ApiParamFieldOption;
};
export declare const requestExampleLanguages: readonly ["bash", "python", "javascript", "php", "go", "java"];
export type RequestExampleLanguages = ((typeof requestExampleLanguages)[number] | string)[];
