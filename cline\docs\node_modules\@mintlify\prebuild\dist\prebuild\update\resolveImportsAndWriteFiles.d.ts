import type { FileType, FileWithImports } from '@mintlify/common';
import { AsyncAPIFile } from '@mintlify/common';
import type { OpenApiFile, DecoratedNavigationPage } from '@mintlify/models';
type ResolveImportsAndWriteFilesArgs = {
    openApiFiles: OpenApiFile[];
    asyncApiFiles: AsyncAPIFile[];
    pagesAcc: Record<string, DecoratedNavigationPage>;
    snippetsV2: FileType[];
    filesWithImports: FileWithImports[];
};
export declare const resolveImportsAndWriteFiles: ({ openApiFiles, asyncApiFiles, pagesAcc, snippetsV2, filesWithImports, }: ResolveImportsAndWriteFilesArgs) => Promise<void>;
export {};
