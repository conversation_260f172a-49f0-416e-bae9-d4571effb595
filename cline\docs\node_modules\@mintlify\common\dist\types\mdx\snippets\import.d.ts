import { Root } from 'mdast';
export type MdxImportNode = {
    source: string;
    specifiers: ImportSpecifier[];
};
export type ImportSpecifier = {
    name: string;
    type: MdxImportSpecifier;
    renamedName?: string;
};
export declare enum MdxImportSpecifier {
    ImportDefaultSpecifier = "ImportDefaultSpecifier",// import DefaultImport from 'source'
    ImportSpecifier = "ImportSpecifier",// import { ImportItem } from 'source'
    ImportNamespaceSpecifier = "ImportNamespaceSpecifier",// import * as A from 'source
    RenamedImportSpecifier = "RenamedImportSpecifier"
}
type Source = string;
export type ImportMap = Record<Source, ImportSpecifier[]>;
export type FindAndRemoveImportsResponse = {
    importMap: ImportMap;
    tree: Root;
};
export type FileWithImports = FindAndRemoveImportsResponse & {
    filename: string;
};
export {};
