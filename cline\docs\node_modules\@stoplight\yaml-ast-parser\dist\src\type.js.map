{"version": 3, "file": "type.js", "sourceRoot": "", "sources": ["../../src/type.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;AAEb,6CAA8C;AAE9C,IAAI,wBAAwB,GAAG;IAC7B,MAAM;IACN,SAAS;IACT,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,cAAc;IACd,cAAc;CACf,CAAC;AAEF,IAAI,eAAe,GAAG;IACpB,QAAQ;IACR,UAAU;IACV,SAAS;CACV,CAAC;AAEF,SAAS,mBAAmB,CAAC,GAAG;IAC9B,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAI,IAAI,KAAK,GAAG,EAAE;QAChB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK;YACtC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK;gBAChC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAa,IAAI;IAaf,YAAY,GAAG,EAAE,OAAO;QACtB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAExB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI;YACzC,IAAI,CAAC,CAAC,KAAK,wBAAwB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACjD,MAAM,IAAI,aAAa,CAAC,kBAAkB,GAAG,IAAI,GAAG,6BAA6B,GAAG,GAAG,GAAG,cAAc,CAAC,CAAC;aAC3G;QACH,CAAC,CAAC,CAAC;QAGH,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,cAAc,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,UAAU,IAAI,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QAChD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;QAC9C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;QAC9C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QACpD,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,CAAC;QAEzE,IAAI,CAAC,CAAC,KAAK,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,IAAI,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,GAAG,sBAAsB,GAAG,GAAG,GAAG,cAAc,CAAC,CAAC;SACvG;IACH,CAAC;CACF;AArCD,oBAqCC"}