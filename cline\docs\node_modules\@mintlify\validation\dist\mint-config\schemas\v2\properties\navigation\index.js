import { z } from 'zod';
import { anchorsSchema, decoratedAnchorsSchema, } from './anchors.js';
import { decoratedDropdownsSchema, dropdownsSchema, } from './dropdown.js';
import { globalSchema } from './global.js';
import { decoratedGroupsSchema, groupsSchema, } from './groups.js';
import { decoratedLanguagesSchema, languagesSchema, } from './languages.js';
import { decoratedPagesSchema, pagesSchema } from './pages.js';
import { decoratedTabsSchema, tabsSchema } from './tabs.js';
import { decoratedVersionsSchema, versionsSchema, } from './version.js';
const baseNavigationSchema = z.object({ global: globalSchema.optional() });
export const navigationSchema = z
    .union([
    baseNavigationSchema.extend({ languages: languagesSchema }),
    baseNavigationSchema.extend({ versions: versionsSchema }),
    baseNavigationSchema.extend({ tabs: tabsSchema }),
    baseNavigationSchema.extend({ dropdowns: dropdownsSchema }),
    baseNavigationSchema.extend({ anchors: anchorsSchema }),
    baseNavigationSchema.extend({ groups: groupsSchema }),
    baseNavigationSchema.extend({ pages: pagesSchema }),
])
    .describe('The navigation structure of the content');
export const decoratedNavigationSchema = z
    .union([
    z.object({ languages: decoratedLanguagesSchema }),
    z.object({ versions: decoratedVersionsSchema }),
    z.object({ tabs: decoratedTabsSchema }),
    z.object({ dropdowns: decoratedDropdownsSchema }),
    z.object({ anchors: decoratedAnchorsSchema }),
    z.object({ groups: decoratedGroupsSchema }),
    z.object({ pages: decoratedPagesSchema }),
])
    .and(z.object({ global: globalSchema.optional() }))
    .describe('The navigation structure of the content');
