import { isExport, isMdxJsEsm } from '../utils.js';
/**
 * An export looks like this in AST form:
 * {
 *   type: 'mdxjsEsm',
 *   data: {
 *     estree: {
 *       type: 'Program',
 *       sourceType: 'module'
 *       body: [
 *         type: 'ExportNamedDeclaration',
 *         ...
 *       ]
 *     }
 *   }
 * }
 * @param content mdx ast node
 * @returns whether the node includes an export or not
 */
export const nodeIncludesExport = (node) => {
    var _a, _b;
    if (!isMdxJsEsm(node) || !((_b = (_a = node.data) === null || _a === void 0 ? void 0 : _a.estree) === null || _b === void 0 ? void 0 : _b.body) || node.data.estree.sourceType !== 'module') {
        return false;
    }
    for (const bodyChild of node.data.estree.body) {
        if (isExport(bodyChild.type)) {
            return true;
        }
    }
    return false;
};
