export type AmplitudeConfig = {
    apiKey: string;
};
export type ClearbitConfig = {
    publicApiKey: string;
};
export type FathomConfig = {
    siteId: string;
};
export type GoogleAnalyticsConfig = {
    measurementId: string;
};
export type GoogleTagManagerConfig = {
    tagId: string;
};
export type HeapConfig = {
    appId: string;
};
export type HotjarConfig = {
    hjid: string;
    hjsv: string;
};
export type KoalaConfig = {
    publicApiKey: string;
};
export type LogrocketConfig = {
    appId: string;
};
export type MixpanelConfig = {
    projectToken: string;
};
export type PirschConfig = {
    id: string;
};
export type PostHogConfig = {
    apiKey: string;
    apiHost?: string;
};
export type PlausibleConfig = {
    domain: string;
    server?: string;
};
export type SegmentConfig = {
    key: string;
};
export type Analytics = {
    amplitude?: AmplitudeConfig;
    clearbit?: ClearbitConfig;
    fathom?: FathomConfig;
    ga4?: GoogleAnalyticsConfig;
    gtm?: GoogleTagManagerConfig;
    heap?: HeapConfig;
    hotjar?: HotjarConfig;
    koala?: KoalaConfig;
    logrocket?: LogrocketConfig;
    mixpanel?: MixpanelConfig;
    pirsch?: PirschConfig;
    posthog?: PostHogConfig;
    plausible?: PlausibleConfig;
    segment?: SegmentConfig;
};
