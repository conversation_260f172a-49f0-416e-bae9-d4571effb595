{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/sns/0.2.0/channel.json", "title": "Channel Schema", "description": "This object contains information about the channel representation in SNS.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"name": {"type": "string", "description": "The name of the topic. Can be different from the channel name to allow flexibility around AWS resource naming limitations."}, "ordering": {"$ref": "http://asyncapi.com/bindings/sns/0.2.0/channel.json#/definitions/ordering"}, "policy": {"$ref": "http://asyncapi.com/bindings/sns/0.2.0/channel.json#/definitions/policy"}, "tags": {"type": "object", "description": "Key-value pairs that represent AWS tags on the topic."}, "bindingVersion": {"type": "string", "description": "The version of this binding.", "default": "latest"}}, "required": ["name"], "definitions": {"ordering": {"type": "object", "description": "By default, we assume an unordered SNS topic. This field allows configuration of a FIFO SNS Topic.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"type": {"type": "string", "description": "Defines the type of SNS Topic.", "enum": ["standard", "FIFO"]}, "contentBasedDeduplication": {"type": "boolean", "description": "True to turn on de-duplication of messages for a channel."}}, "required": ["type"]}, "policy": {"type": "object", "description": "The security policy for the SNS Topic.", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"statements": {"type": "array", "description": "An array of statement objects, each of which controls a permission for this topic", "items": {"$ref": "http://asyncapi.com/bindings/sns/0.2.0/channel.json#/definitions/statement"}}}, "required": ["statements"]}, "statement": {"type": "object", "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"effect": {"type": "string", "enum": ["Allow", "<PERSON><PERSON>"]}, "principal": {"description": "The AWS account(s) or resource ARN(s) that this statement applies to.", "oneOf": [{"type": "string"}, {"type": "object", "properties": {"AWS": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["AWS"], "additionalProperties": false}, {"type": "object", "properties": {"Service": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["Service"], "additionalProperties": false}]}, "action": {"description": "The SNS permission(s) being allowed or denied e.g. sns:Publish", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "resource": {"description": "The resource(s) that this policy applies to.", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "condition": {"description": "Specific circumstances under which the policy grants permission", "type": "object", "patternProperties": {".*": {"type": "object", "patternProperties": {".*": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}}}}}, "required": ["effect", "principal", "action"]}}, "examples": [{"name": "my-sns-topic", "policy": {"statements": [{"effect": "Allow", "principal": "*", "action": "SNS:Publish"}]}}]}