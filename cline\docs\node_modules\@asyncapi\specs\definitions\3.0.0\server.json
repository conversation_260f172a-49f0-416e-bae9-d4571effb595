{"type": "object", "description": "An object representing a message broker, a server or any other kind of computer program capable of sending and/or receiving data.", "required": ["host", "protocol"], "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"host": {"type": "string", "description": "The server host name. It MAY include the port. This field supports Server Variables. Variable substitutions will be made when a variable is named in {braces}."}, "pathname": {"type": "string", "description": "The path to a resource in the host. This field supports Server Variables. Variable substitutions will be made when a variable is named in {braces}."}, "title": {"type": "string", "description": "A human-friendly title for the server."}, "summary": {"type": "string", "description": "A brief summary of the server."}, "description": {"type": "string", "description": "A longer description of the server. CommonMark is allowed."}, "protocol": {"type": "string", "description": "The protocol this server supports for connection."}, "protocolVersion": {"type": "string", "description": "An optional string describing the server. CommonMark syntax MAY be used for rich text representation."}, "variables": {"$ref": "http://asyncapi.com/definitions/3.0.0/serverVariables.json"}, "security": {"$ref": "http://asyncapi.com/definitions/3.0.0/securityRequirements.json"}, "tags": {"type": "array", "items": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/tag.json"}]}, "uniqueItems": true}, "externalDocs": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/externalDocs.json"}]}, "bindings": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/serverBindingsObject.json"}]}}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/server.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/server.json"}