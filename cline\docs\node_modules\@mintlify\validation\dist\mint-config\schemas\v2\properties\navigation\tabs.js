import { z } from 'zod';
import { asyncApiSchema } from '../reusable/asyncapi.js';
import { hiddenSchema } from '../reusable/hidden.js';
import { hrefSchema } from '../reusable/href.js';
import { iconSchema } from '../reusable/icon.js';
import { openApiSchema } from '../reusable/openapi.js';
import { anchorsSchema, decoratedAnchorsSchema } from './anchors.js';
import { decoratedDropdownsSchema, dropdownsSchema } from './dropdown.js';
import { globalSchema } from './global.js';
import { decoratedGroupsSchema, groupsSchema } from './groups.js';
import { decoratedLanguagesSchema, languagesSchema } from './languages.js';
import { decoratedPagesSchema, pagesSchema } from './pages.js';
import { decoratedVersionsSchema, versionsSchema } from './version.js';
export const baseTabSchema = z.object({
    tab: z.string().nonempty().describe('The name of the tab'),
    icon: iconSchema.optional(),
    hidden: hiddenSchema.optional(),
});
export const nonRecursiveTabSchema = baseTabSchema.extend({ href: hrefSchema });
export const tabSchema = z.union([
    baseTabSchema.extend({
        href: hrefSchema,
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseTabSchema.extend({
        languages: z.lazy(() => languagesSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseTabSchema.extend({
        versions: z.lazy(() => versionsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseTabSchema.extend({
        dropdowns: z.lazy(() => dropdownsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseTabSchema.extend({
        anchors: z.lazy(() => anchorsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseTabSchema.extend({
        groups: z.lazy(() => groupsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseTabSchema.extend({
        pages: z.lazy(() => pagesSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseTabSchema.extend({
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
]);
export const decoratedTabSchema = baseTabSchema.and(z.union([
    z.object({ href: hrefSchema }),
    z.lazy(() => z.object({ languages: decoratedLanguagesSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ versions: decoratedVersionsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ dropdowns: decoratedDropdownsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ anchors: decoratedAnchorsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ groups: decoratedGroupsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ pages: decoratedPagesSchema, global: globalSchema.optional() })),
]));
export const tabsSchema = z
    .array(tabSchema)
    .min(1, 'At least one tab must be specified')
    .describe('Organizing by tabs');
export const decoratedTabsSchema = z
    .array(decoratedTabSchema)
    .min(1, 'At least one tab must be specified')
    .describe('Organizing by tabs');
