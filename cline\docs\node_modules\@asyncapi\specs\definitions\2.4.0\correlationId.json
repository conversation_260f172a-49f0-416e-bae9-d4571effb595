{"type": "object", "required": ["location"], "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.4.0/specificationExtension.json"}}, "properties": {"description": {"type": "string", "description": "A optional description of the correlation ID. GitHub Flavored Markdown is allowed."}, "location": {"type": "string", "description": "A runtime expression that specifies the location of the correlation ID", "pattern": "^\\$message\\.(header|payload)#(\\/(([^\\/~])|(~[01]))*)*"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.4.0/correlationId.json"}