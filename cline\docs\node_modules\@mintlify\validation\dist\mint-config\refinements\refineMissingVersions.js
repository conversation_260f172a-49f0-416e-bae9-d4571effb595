import { z } from 'zod';
import { flattenNavigationVersions } from '../flattenNavigationVersions.js';
export function refineMissingVersions({ navigation, versions = [], }, ctx) {
    const versionsFromNavigation = flattenNavigationVersions(navigation);
    const versionNames = versions
        .map((v) => (typeof v === 'string' ? v : v.url ? undefined : v.name))
        .filter(Boolean);
    versionsFromNavigation.forEach((v) => {
        if (!versionNames.includes(v)) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: `Version ${v} is not included in the versions array, but is used in the navigation. Please add ${v} to the versions array.`,
            });
        }
    });
}
