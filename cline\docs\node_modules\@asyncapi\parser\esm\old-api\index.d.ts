export { convertToOldAPI, convertToNewAPI } from './converter';
export { AsyncAPIDocument as OldAsyncAPIDocument } from './asyncapi';
export { Base as OldBase } from './base';
export { ChannelParameter as OldChannelParameter } from './channel-parameter';
export { Channel as OldChannel } from './channel';
export { Components as OldComponents } from './components';
export { Contact as OldContact } from './contact';
export { CorrelationId as OldCorrelationId } from './correlation-id';
export { ExternalDocs as OldExternalDocs } from './external-docs';
export { License as OldLicense } from './license';
export { MessageTrait as OldMessageTrait } from './message-trait';
export { Message as OldMessage } from './message';
export { OAuthFlow as OldOAuthFlow } from './oauth-flow';
export { OperationTrait as OldOperationTrait } from './operation-trait';
export { Operation as OldOperation } from './operation';
export { Schema as OldSchema } from './schema';
export { SecurityRequirement as OldSecurityRequirement } from './security-requirement';
export { SecurityScheme as OldSecurityScheme } from './security-scheme';
export { ServerVariable as OldServerVariable } from './server-variable';
export { Server as OldServer } from './server';
export { Tag as OldTag } from './tag';
