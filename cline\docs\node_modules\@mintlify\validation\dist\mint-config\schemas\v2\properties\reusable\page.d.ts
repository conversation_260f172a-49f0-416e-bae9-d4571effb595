import { z } from 'zod';
export declare const pageSchema: z.ZodEffects<z.ZodString, string, string>;
export declare const decoratedPageSchema: z.ZodObject<{
    href: z.ZodString;
    title: z.ZodString;
    sidebarTitle: z.<PERSON>odOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodString>;
    api: z.ZodOptional<z.ZodString>;
    openapi: z.ZodOptional<z.ZodString>;
    asyncapi: z.ZodOptional<z.ZodString>;
    contentType: z.ZodOptional<z.ZodString>;
    authMethod: z.ZodOptional<z.ZodString>;
    auth: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodString>;
    hideFooterPagination: z.ZodOptional<z.ZodBoolean>;
    authors: <PERSON>.<PERSON>ption<PERSON><z.ZodUnknown>;
    lastUpdatedDate: z.ZodOptional<z.ZodString>;
    createdDate: z.<PERSON>ption<PERSON><z.ZodString>;
    'openapi-schema': z.<PERSON>odOptional<z.ZodString>;
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    tag: z.ZodOptional<z.ZodString>;
    url: z.ZodOptional<z.ZodString>;
    hideApiMarker: z.ZodOptional<z.ZodBoolean>;
    noindex: z.ZodOptional<z.ZodBoolean>;
    isPublic: z.ZodOptional<z.ZodBoolean>;
    public: z.ZodOptional<z.ZodBoolean>;
    deprecated: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    href: string;
    title: string;
    sidebarTitle?: string | undefined;
    description?: string | undefined;
    api?: string | undefined;
    openapi?: string | undefined;
    asyncapi?: string | undefined;
    contentType?: string | undefined;
    authMethod?: string | undefined;
    auth?: string | undefined;
    version?: string | undefined;
    mode?: string | undefined;
    hideFooterPagination?: boolean | undefined;
    authors?: unknown;
    lastUpdatedDate?: string | undefined;
    createdDate?: string | undefined;
    'openapi-schema'?: string | undefined;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    url?: string | undefined;
    hideApiMarker?: boolean | undefined;
    noindex?: boolean | undefined;
    isPublic?: boolean | undefined;
    public?: boolean | undefined;
    deprecated?: boolean | undefined;
}, {
    href: string;
    title: string;
    sidebarTitle?: string | undefined;
    description?: string | undefined;
    api?: string | undefined;
    openapi?: string | undefined;
    asyncapi?: string | undefined;
    contentType?: string | undefined;
    authMethod?: string | undefined;
    auth?: string | undefined;
    version?: string | undefined;
    mode?: string | undefined;
    hideFooterPagination?: boolean | undefined;
    authors?: unknown;
    lastUpdatedDate?: string | undefined;
    createdDate?: string | undefined;
    'openapi-schema'?: string | undefined;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    tag?: string | undefined;
    url?: string | undefined;
    hideApiMarker?: boolean | undefined;
    noindex?: boolean | undefined;
    isPublic?: boolean | undefined;
    public?: boolean | undefined;
    deprecated?: boolean | undefined;
}>;
export type DecoratedPageConfig = z.infer<typeof decoratedPageSchema>;
