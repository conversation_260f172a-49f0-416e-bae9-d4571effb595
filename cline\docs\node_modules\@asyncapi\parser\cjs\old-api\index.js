"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OldTag = exports.OldServer = exports.OldServerVariable = exports.OldSecurityScheme = exports.OldSecurityRequirement = exports.OldSchema = exports.OldOperation = exports.OldOperationTrait = exports.OldOAuthFlow = exports.OldMessage = exports.OldMessageTrait = exports.OldLicense = exports.OldExternalDocs = exports.OldCorrelationId = exports.OldContact = exports.OldComponents = exports.OldChannel = exports.OldChannelParameter = exports.OldBase = exports.OldAsyncAPIDocument = exports.convertToNewAPI = exports.convertToOldAPI = void 0;
var converter_1 = require("./converter");
Object.defineProperty(exports, "convertToOldAPI", { enumerable: true, get: function () { return converter_1.convertToOldAPI; } });
Object.defineProperty(exports, "convertToNewAPI", { enumerable: true, get: function () { return converter_1.convertToNewAPI; } });
var asyncapi_1 = require("./asyncapi");
Object.defineProperty(exports, "OldAsyncAPIDocument", { enumerable: true, get: function () { return asyncapi_1.AsyncAPIDocument; } });
var base_1 = require("./base");
Object.defineProperty(exports, "OldBase", { enumerable: true, get: function () { return base_1.Base; } });
var channel_parameter_1 = require("./channel-parameter");
Object.defineProperty(exports, "OldChannelParameter", { enumerable: true, get: function () { return channel_parameter_1.ChannelParameter; } });
var channel_1 = require("./channel");
Object.defineProperty(exports, "OldChannel", { enumerable: true, get: function () { return channel_1.Channel; } });
var components_1 = require("./components");
Object.defineProperty(exports, "OldComponents", { enumerable: true, get: function () { return components_1.Components; } });
var contact_1 = require("./contact");
Object.defineProperty(exports, "OldContact", { enumerable: true, get: function () { return contact_1.Contact; } });
var correlation_id_1 = require("./correlation-id");
Object.defineProperty(exports, "OldCorrelationId", { enumerable: true, get: function () { return correlation_id_1.CorrelationId; } });
var external_docs_1 = require("./external-docs");
Object.defineProperty(exports, "OldExternalDocs", { enumerable: true, get: function () { return external_docs_1.ExternalDocs; } });
var license_1 = require("./license");
Object.defineProperty(exports, "OldLicense", { enumerable: true, get: function () { return license_1.License; } });
var message_trait_1 = require("./message-trait");
Object.defineProperty(exports, "OldMessageTrait", { enumerable: true, get: function () { return message_trait_1.MessageTrait; } });
var message_1 = require("./message");
Object.defineProperty(exports, "OldMessage", { enumerable: true, get: function () { return message_1.Message; } });
var oauth_flow_1 = require("./oauth-flow");
Object.defineProperty(exports, "OldOAuthFlow", { enumerable: true, get: function () { return oauth_flow_1.OAuthFlow; } });
var operation_trait_1 = require("./operation-trait");
Object.defineProperty(exports, "OldOperationTrait", { enumerable: true, get: function () { return operation_trait_1.OperationTrait; } });
var operation_1 = require("./operation");
Object.defineProperty(exports, "OldOperation", { enumerable: true, get: function () { return operation_1.Operation; } });
var schema_1 = require("./schema");
Object.defineProperty(exports, "OldSchema", { enumerable: true, get: function () { return schema_1.Schema; } });
var security_requirement_1 = require("./security-requirement");
Object.defineProperty(exports, "OldSecurityRequirement", { enumerable: true, get: function () { return security_requirement_1.SecurityRequirement; } });
var security_scheme_1 = require("./security-scheme");
Object.defineProperty(exports, "OldSecurityScheme", { enumerable: true, get: function () { return security_scheme_1.SecurityScheme; } });
var server_variable_1 = require("./server-variable");
Object.defineProperty(exports, "OldServerVariable", { enumerable: true, get: function () { return server_variable_1.ServerVariable; } });
var server_1 = require("./server");
Object.defineProperty(exports, "OldServer", { enumerable: true, get: function () { return server_1.Server; } });
var tag_1 = require("./tag");
Object.defineProperty(exports, "OldTag", { enumerable: true, get: function () { return tag_1.Tag; } });
