[{"type": "oauth2", "flows": {"implicit": {"authorizationUrl": "https://example.com/api/oauth/dialog", "scopes": {"write:pets": "modify pets in your account", "read:pets": "read your pets"}}, "authorizationCode": {"authorizationUrl": "https://example.com/api/oauth/dialog", "tokenUrl": "https://example.com/api/oauth/token", "scopes": {"write:pets": "modify pets in your account", "read:pets": "read your pets"}}}}]