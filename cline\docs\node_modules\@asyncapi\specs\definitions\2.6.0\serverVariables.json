{"type": "object", "description": "A map between a variable name and its value. The value is used for substitution in the server's URL template.", "additionalProperties": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.6.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/serverVariable.json"}]}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/serverVariables.json"}