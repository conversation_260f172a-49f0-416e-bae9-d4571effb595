import { DataSchema, Endpoint } from '@mintlify/validation';
import { MdxJsxFlowElementHast } from 'mdast-util-mdx-jsx';
import type { Node } from 'unist';
import { LocationAttr } from './parsers.js';
type InsertSchemaParams = {
    locationAttr: LocationAttr;
    node: MdxJsxFlowElementHast;
    schema: DataSchema;
    deepestSchema: DataSchema;
    parentSchema: DataSchema | undefined;
    nodeToSchema: Map<Node, DataSchema>;
    endpoint: Endpoint;
    requestContentType: string;
};
export declare const insertSchema: ({ locationAttr, node, schema, deepestSchema, parentSchema, nodeToSchema, endpoint, requestContentType, }: InsertSchemaParams) => boolean;
export {};
