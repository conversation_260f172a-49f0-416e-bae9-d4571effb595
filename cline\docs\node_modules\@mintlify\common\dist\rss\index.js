import slugify from '@sindresorhus/slugify';
import { stringifyTree } from '../mdx/index.js';
import { getArrayExpressionStringProperties, getObjectExpressionStringProperty, } from '../mdx/utils.js';
export const UPDATE_MAX = 15;
export const isFrontmatter = (node) => {
    return (node === null || node === void 0 ? void 0 : node.type) === 'yaml';
};
export const isUpdate = (node) => {
    return (node === null || node === void 0 ? void 0 : node.type) === 'mdxJsxFlowElement' && node.name === 'Update';
};
export const isHeading = (node) => {
    if (!node) {
        return false;
    }
    return node.type === 'heading';
};
export const isNormalMarkdown = (node) => {
    if (!node) {
        return false;
    }
    return (node.type !== 'mdxJsxFlowElement' &&
        node.type !== 'mdxJsxTextElement' &&
        node.type !== 'html' &&
        node.type !== 'code' &&
        node.type !== 'inlineCode');
};
export const containsUpdates = (tree) => {
    return tree.children.some((child) => isUpdate(child));
};
export const getTags = (node) => {
    let tags = [];
    const tagsAttribute = node.attributes.find((attr) => 'name' in attr && attr.name === 'tags');
    if (!tagsAttribute || !tagsAttribute.value || typeof tagsAttribute.value !== 'object') {
        return tags;
    }
    try {
        tags = JSON.parse(tagsAttribute.value.value);
    }
    catch (_a) {
        tags = getArrayExpressionStringProperties(tagsAttribute);
    }
    if (tags.length > 0) {
        return tags;
    }
    return undefined;
};
export const getRssPropsData = (updateComponent) => {
    const attributes = updateComponent.attributes;
    const rssData = attributes.find((attribute) => attribute.type === 'mdxJsxAttribute' && attribute.name === 'rss');
    const title = getObjectExpressionStringProperty('title', rssData);
    const description = getObjectExpressionStringProperty('description', rssData);
    return { rssTitle: title, rssDescription: description };
};
export const getUpdateTitle = (updateComponent) => {
    var _a;
    const attributes = updateComponent.attributes;
    const label = (_a = attributes.find((attribute) => attribute.type === 'mdxJsxAttribute' && attribute.name === 'label')) === null || _a === void 0 ? void 0 : _a.value;
    if (label) {
        return label.toString();
    }
    return undefined;
};
export const getUpdateDescription = (updateComponent) => {
    var _a;
    const attributes = updateComponent.attributes;
    const descriptionAttribute = (_a = attributes.find((attribute) => attribute.type === 'mdxJsxAttribute' && attribute.name === 'description')) === null || _a === void 0 ? void 0 : _a.value;
    if (descriptionAttribute) {
        return descriptionAttribute.toString();
    }
    return undefined;
};
export const compareUpdates = ({ newTree, previousTree, }) => {
    const newUpdateComponents = newTree.children.filter((child) => isUpdate(child));
    const previousUpdateComponents = previousTree.children
        .filter((child) => isUpdate(child))
        .map(getUpdateTitle);
    const previousUpdateComponentsSet = new Set(previousUpdateComponents);
    const newUpdates = newUpdateComponents.filter((component) => {
        const title = getUpdateTitle(component);
        return !previousUpdateComponentsSet.has(title);
    });
    return newUpdates;
};
export const matchRSSTitle = (node, title) => {
    const { rssTitle } = getRssPropsData(node);
    const label = getUpdateTitle(node);
    const nodeTitle = label || rssTitle;
    return nodeTitle === title;
};
export const splitChildrenAtHeadings = (children) => {
    return children.reduce((acc, child) => {
        if (isHeading(child)) {
            acc.push([child]);
        }
        else {
            if (isNormalMarkdown(child)) {
                if (acc.length === 0) {
                    acc.push([child]);
                }
                else {
                    const lastGroup = acc[acc.length - 1];
                    if (lastGroup) {
                        lastGroup.push(child);
                    }
                }
            }
        }
        return acc;
    }, []);
};
export const getMarkdownHeadingProps = (heading) => {
    const headingContent = heading.children[0];
    let title = undefined;
    let anchor = undefined;
    if ((headingContent === null || headingContent === void 0 ? void 0 : headingContent.type) === 'text') {
        title = headingContent.value;
    }
    if (title) {
        anchor = slugify(title);
    }
    return { title, anchor };
};
export const updateGroupToRSSItemV2 = ({ group, date, }) => {
    const dateToUse = date || new Date().toISOString();
    const heading = group[0];
    if (!heading || !isHeading(heading)) {
        return undefined;
    }
    const { title, anchor } = getMarkdownHeadingProps(heading);
    const content = group.slice(1);
    const contentString = stringifyTree({
        type: 'root',
        children: content,
    });
    if (!title || !anchor) {
        return undefined;
    }
    return { title, date: dateToUse, anchor, content: contentString };
};
export const getNewContent = (newUpdateComponents) => {
    const newUpdates = [];
    for (const component of newUpdateComponents) {
        const children = component.children;
        const updatesByHeading = splitChildrenAtHeadings(children);
        for (const group of updatesByHeading) {
            const newUpdate = updateGroupToRSSItemV2({ group });
            if (newUpdate) {
                newUpdates.push(newUpdate);
            }
        }
    }
    return newUpdates;
};
export const getNewMarkdownUpdates = ({ newTree, previousTree, previousUpdates, }) => {
    const firstUpdateInNewTree = newTree.children.find(isUpdate);
    const firstUpdateInPreviousTree = previousTree.children.find(isUpdate);
    if (!isUpdate(firstUpdateInNewTree) || !isUpdate(firstUpdateInPreviousTree)) {
        // no last updates found to compare
        return [];
    }
    const firstUpdateTitleInNewTree = getUpdateTitle(firstUpdateInNewTree);
    const firstUpdateTitleInPreviousTree = getUpdateTitle(firstUpdateInPreviousTree);
    if (firstUpdateTitleInNewTree !== firstUpdateTitleInPreviousTree) {
        // last update component has changed
        return [];
    }
    const newUpdates = splitChildrenAtHeadings(firstUpdateInNewTree.children);
    const actuallyNewUpdates = [];
    for (const group of newUpdates) {
        const newUpdate = updateGroupToRSSItemV2({ group });
        if (newUpdate && !previousUpdates.find((update) => update.title === newUpdate.title)) {
            actuallyNewUpdates.push(newUpdate);
        }
    }
    return actuallyNewUpdates;
};
