import { z } from 'zod';
import { pageSchema } from '../reusable/page.js';
export declare const pageOrGroupSchema: z.ZodType;
export declare const decoratedPageOrGroupSchema: z.ZodType;
export declare const pagesSchema: z.<PERSON><z.ZodType<any, z.ZodTypeDef, any>, "many">;
export declare const decoratedPagesSchema: z.<PERSON><z.ZodType<any, z.ZodTypeDef, any>, "many">;
export type PageConfig = z.infer<typeof pageSchema>;
export type PagesConfig = z.infer<typeof pagesSchema>;
export type DecoratedPagesConfig = z.infer<typeof decoratedPagesSchema>;
export type PageOrGroupConfig = z.infer<typeof pageOrGroupSchema>;
export type DecoratedPageOrGroupConfig = z.infer<typeof decoratedPageOrGroupSchema>;
