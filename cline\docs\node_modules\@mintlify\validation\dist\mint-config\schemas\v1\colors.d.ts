import { z } from 'zod';
export declare const colorsSchema: z.ZodObject<{
    primary: z.ZodString;
    light: z.Zod<PERSON>ptional<z.ZodString>;
    dark: z.ZodOptional<z.ZodString>;
    background: z.ZodOptional<z.ZodObject<{
        light: z.ZodOptional<z.ZodString>;
        dark: z.ZodOptional<z.ZodString>;
    }, "strip", z.<PERSON>, {
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        light?: string | undefined;
        dark?: string | undefined;
    }>>;
    anchors: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
        from: z.ZodString;
        via: z.ZodOptional<z.ZodString>;
        to: z.ZodString;
    }, "strict", z.<PERSON><PERSON>, {
        from: string;
        to: string;
        via?: string | undefined;
    }, {
        from: string;
        to: string;
        via?: string | undefined;
    }>]>>;
    ultraLight: z.<PERSON>ptional<z.<PERSON><PERSON><PERSON>ny>;
    ultraDark: z.Zod<PERSON>ptional<z.<PERSON><PERSON>>;
}, "strict", z.<PERSON><PERSON><PERSON>, {
    primary: string;
    light?: string | undefined;
    dark?: string | undefined;
    background?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    anchors?: string | {
        from: string;
        to: string;
        via?: string | undefined;
    } | undefined;
    ultraLight?: any;
    ultraDark?: any;
}, {
    primary: string;
    light?: string | undefined;
    dark?: string | undefined;
    background?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    anchors?: string | {
        from: string;
        to: string;
        via?: string | undefined;
    } | undefined;
    ultraLight?: any;
    ultraDark?: any;
}>;
