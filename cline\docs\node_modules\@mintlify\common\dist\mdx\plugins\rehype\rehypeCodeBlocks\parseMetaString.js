import { MetaOptions } from './metaOptions.js';
export const META_PARSER_CONFIG = {
    filename: {
        keys: ['title'],
        parser: (metaOptions) => {
            const title = metaOptions.getString('title');
            return title || metaOptions.getRemainingText() || '';
        },
        defaultValue: '',
    },
    icon: {
        keys: ['icon'],
        parser: (metaOptions) => metaOptions.getString('icon'),
    },
    lang: {
        keys: ['lang'],
        parser: (metaOptions) => metaOptions.getString('lang'),
    },
    lines: {
        keys: ['lines'],
        parser: (metaOptions) => metaOptions.getBoolean('lines'),
    },
    wrap: {
        keys: ['wrap'],
        parser: (metaOptions) => metaOptions.getBoolean('wrap'),
    },
    expandable: {
        keys: ['expandable', '[expandable]'],
        parser: (metaOptions) => metaOptions.getBoolean('[expandable]') || metaOptions.getBoolean('expandable'),
    },
    highlight: {
        keys: ['highlight'],
        parser: (metaOptions) => {
            const highlightWithString = metaOptions.getStringsWithInteger('highlight');
            const highlightWithInteger = metaOptions.getRangesWithInteger('highlight');
            const highlightWithStringWithoutKey = metaOptions.getRangesWithInteger('');
            const result = [
                ...highlightWithString,
                ...highlightWithInteger,
                ...highlightWithStringWithoutKey,
            ];
            return result.length ? Array.from(new Set(result)) : undefined;
        },
    },
    focus: {
        keys: ['focus'],
        parser: (metaOptions) => {
            const focusWithString = metaOptions.getStringsWithInteger('focus');
            const focusWithInteger = metaOptions.getRangesWithInteger('focus');
            const result = [...focusWithString, ...focusWithInteger];
            return result.length ? Array.from(new Set(result)) : undefined;
        },
    },
};
export function parseMetaString(input) {
    const reservedKeys = Object.values(META_PARSER_CONFIG).flatMap((config) => config.keys);
    const metaOptions = new MetaOptions(input, reservedKeys);
    const result = {};
    for (const [key, config] of Object.entries(META_PARSER_CONFIG)) {
        const parsedValue = config === null || config === void 0 ? void 0 : config.parser(metaOptions);
        if (parsedValue !== undefined) {
            result[key] = parsedValue;
        }
        else if ((config === null || config === void 0 ? void 0 : config.defaultValue) !== undefined) {
            result[key] = config.defaultValue;
        }
    }
    return result;
}
