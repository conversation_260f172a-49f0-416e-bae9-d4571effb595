import { z } from 'zod';
export declare const integrationsSchema: z.ZodObject<{
    intercom: z.ZodOptional<z.ZodString>;
    frontchat: z.ZodOptional<z.ZodString>;
    osano: z.ZodOptional<z.ZodString>;
}, "strip", z.<PERSON>ypeAny, {
    intercom?: string | undefined;
    frontchat?: string | undefined;
    osano?: string | undefined;
}, {
    intercom?: string | undefined;
    frontchat?: string | undefined;
    osano?: string | undefined;
}>;
