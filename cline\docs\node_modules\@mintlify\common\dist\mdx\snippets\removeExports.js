var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { remark } from 'remark';
import { remove } from 'unist-util-remove';
import { coreRemarkMdxPlugins } from '../remark.js';
import { nodeIncludesExport } from './nodeIncludesExport.js';
export const removeExports = (content) => __awaiter(void 0, void 0, void 0, function* () {
    const convertedContent = yield remark()
        .use(coreRemarkMdxPlugins)
        .use(() => (tree) => {
        remove(tree, (node) => {
            if (!nodeIncludesExport(node))
                return false;
            node.value = '';
            return true;
        });
    })
        .process(content);
    return String(convertedContent);
});
