export declare function resolve(...parameters: any[]): string;
export declare function normalize(path: any): string;
export declare function isAbsolute(path: any): boolean;
export declare function join(...paths: string[]): string;
export declare function relative(from: any, to: any): string;
export declare const sep = "/";
export declare const delimiter = ":";
export declare function dirname(path: any): string;
export declare function basename(path: any, ext: any): string;
export declare function extname(path: any): string;
declare const _default: {
    extname: typeof extname;
    basename: typeof basename;
    dirname: typeof dirname;
    sep: string;
    delimiter: string;
    relative: typeof relative;
    join: typeof join;
    isAbsolute: typeof isAbsolute;
    normalize: typeof normalize;
    resolve: typeof resolve;
};
export default _default;
//# sourceMappingURL=path.d.ts.map