// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface CheckpointsServiceDefinition {
  // Checkpoints service methods
}

export interface CheckpointRestoreRequest {
  checkpointId: string;
  restoreType: CheckpointRestoreType;
}

export enum CheckpointRestoreType {
  WORKSPACE_ONLY = "WORKSPACE_ONLY",
  TASK_AND_WORKSPACE = "TASK_AND_WORKSPACE"
}

export interface Checkpoint {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  taskId: string;
}

export interface CheckpointListResponse {
  checkpoints: Checkpoint[];
}
