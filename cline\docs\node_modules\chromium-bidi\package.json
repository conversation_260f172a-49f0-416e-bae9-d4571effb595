{"name": "chromium-bidi", "version": "0.6.3", "description": "An implementation of the WebDriver BiDi protocol for Chromium implemented as a JavaScript layer translating between BiDi and CDP, running inside a Chrome tab.", "scripts": {"build": "wireit", "clean": "node tools/clean.mjs", "e2e:headful": "HEADLESS=false npm run e2e --", "e2e:headless": "npm run e2e:new-headless --", "e2e:new-headless": "HEADLESS=new npm run e2e --", "e2e:old-headless": "HEADLESS=old npm run e2e --", "e2e": "wireit", "flake8": "flake8 examples/ tests/", "format": "npm run pre-commit --", "format:eslint": "eslint --ext js --ext mjs --ext ts --fix .", "format:prettier": "prettier --write .", "pre-commit": "pre-commit run --hook-stage manual --all-files", "prepare": "wireit", "rollup": "wireit", "server": "wireit", "test": "wireit", "tsc": "wireit", "unit": "wireit", "wpt": "wireit", "wpt:all": "wireit", "yapf": "yapf -i --parallel --recursive --exclude=wpt examples/ tests/"}, "wireit": {"build": {"dependencies": ["rollup", "tsc"]}, "e2e": {"command": "tools/run-e2e.mjs", "files": ["tools/run-e2e.mjs ", "pytest.ini", "tests/**/*.py"], "dependencies": ["build"]}, "prepare": {"dependencies": ["build"]}, "rollup": {"command": "rollup -c", "dependencies": ["tsc"], "files": ["lib/cjs/bidiMapper/index.js", "rollup.config.mjs"], "output": ["lib/iife/mapperTab.js"]}, "server": {"command": "tools/run-bidi-server.mjs", "files": ["tools/run-bidi-server.mjs"], "service": {"readyWhen": {"lineMatches": "(BiDi server|ChromeDriver) was started successfully\\."}}, "dependencies": ["rollup"]}, "test": {"dependencies": ["unit", "e2e", "wpt"]}, "tsc": {"command": "tsc --build src/tsconfig.json --pretty", "clean": "if-file-deleted", "files": ["**/tsconfig*.json", "src/**/*.ts"], "output": ["lib/cjs/**"]}, "unit": {"command": "mocha", "dependencies": ["tsc"]}, "wpt": {"command": "tools/run-wpt.mjs", "files": ["tools/run-wpt.mjs", "wpt/tools/webdriver/**/*.py", "wpt/webdriver/tests/**/*.py", "wpt-metadata/**/*.ini"], "dependencies": ["rollup"]}, "wpt:all": {"command": "tools/run-wpt-all.mjs", "files": ["tools/run-wpt.mjs", "tools/run-wpt-all.mjs", "wpt/tools/webdriver/**/*.py", "wpt/webdriver/tests/**/*.py", "wpt-metadata/**/*.ini"], "dependencies": ["rollup"]}}, "files": ["lib", "!lib/**/*.spec.*", "!*.tsbuil<PERSON><PERSON>", ".browser"], "repository": {"type": "git", "url": "https://github.com/GoogleChromeLabs/chromium-bidi.git"}, "author": "The Chromium Authors", "license": "Apache-2.0", "peerDependencies": {"devtools-protocol": "*"}, "devDependencies": {"@actions/core": "1.10.1", "@puppeteer/browsers": "2.2.3", "@rollup/plugin-commonjs": "26.0.1", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/wasm-node": "4.18.0", "@types/chai": "4.3.11", "@types/chai-as-promised": "7.1.8", "@types/debug": "4.1.12", "@types/mocha": "10.0.7", "@types/node": "20.14.9", "@types/sinon": "17.0.3", "@types/websocket": "1.0.10", "@types/ws": "8.5.10", "@types/yargs": "17.0.32", "@typescript-eslint/eslint-plugin": "7.15.0", "@typescript-eslint/parser": "7.15.0", "chai": "4.4.1", "chai-as-promised": "7.1.2", "debug": "4.3.5", "devtools-protocol": "0.0.1330662", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-mocha": "10.4.3", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-promise": "6.4.0", "gts": "5.3.1", "mocha": "10.5.2", "pkg-dir": "8.0.0", "prettier": "3.3.2", "rimraf": "5.0.7", "rollup": "4.18.0", "rollup-plugin-license": "3.5.1", "selenium-webdriver": "4.22.0", "sinon": "18.0.0", "source-map-support": "0.5.21", "tslib": "2.6.3", "typescript": "5.5.3", "webdriverio": "8.39.0", "websocket": "1.0.35", "wireit": "0.14.4", "ws": "8.17.1", "yargs": "17.7.2"}, "dependencies": {"mitt": "3.0.1", "urlpattern-polyfill": "10.0.0", "zod": "3.23.8"}}