{"version": 3, "file": "schema.js", "sourceRoot": "", "sources": ["../../src/schema.ts"], "names": [], "mappings": "AAEA,YAAY,CAAC;;AAIb,mCAA2C;AAC3C,6CAA8C;AAC9C,iCAA4B;AAG5B,SAAS,WAAW,CAAC,MAAc,EAAE,IAAI,EAAE,MAAM;IAC/C,IAAI,OAAO,GAAG,EAAE,CAAC;IAEjB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,cAAc;QAC7C,MAAM,GAAG,WAAW,CAAC,cAAc,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,WAAW;QACxC,MAAM,CAAC,OAAO,CAAC,UAAU,YAAY,EAAE,aAAa;YAClD,IAAI,YAAY,CAAC,GAAG,KAAK,WAAW,CAAC,GAAG,EAAE;gBACxC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC7B;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,KAAK;QACxC,OAAO,CAAC,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;AACL,CAAC;AAGD,SAAS,UAAU;IACjB,IAAI,MAAM,GAAG,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC;IAE/B,SAAS,WAAW,CAAC,IAAI;QACvB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,KAAK,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;QACrE,SAAS,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;KACvC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAQD,MAAa,MAAM;IASjB,YAAY,UAA2B;QACrC,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,IAAI,EAAE,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAC;QAE1C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI;YAClC,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC/C,MAAM,IAAI,aAAa,CAAC,iHAAiH,CAAC,CAAC;aAC5I;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,eAAe,GAAS,UAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACzF,CAAC;;AAvBH,wBA4DC;AAnCQ,cAAO,GAAC,IAAI,CAAC;AACb,aAAM,GAAC,SAAS,YAAY;IAC/B,IAAI,OAAO,EAAE,KAAK,CAAC;IAEnB,QAAQ,SAAS,CAAC,MAAM,EAAE;QACxB,KAAK,CAAC;YACJ,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YACzB,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACrB,MAAM;QAER,KAAK,CAAC;YACJ,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACvB,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACrB,MAAM;QAER;YACE,MAAM,IAAI,aAAa,CAAC,sDAAsD,CAAC,CAAC;KACnF;IAED,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAClC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAE9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,MAAM,IAAI,OAAO,MAAM,YAAY,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1E,MAAM,IAAI,aAAa,CAAC,2FAA2F,CAAC,CAAC;KACtH;IAED,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,OAAO,IAAI,YAAY,WAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAClE,MAAM,IAAI,aAAa,CAAC,oFAAoF,CAAC,CAAC;KAC/G;IAED,OAAO,IAAI,MAAM,CAAC;QAChB,OAAO,EAAE,OAAO;QAChB,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;AACL,CAAC,CAAA"}