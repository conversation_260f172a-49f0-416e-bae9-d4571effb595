export type Colors = {
    primary: string;
    light?: string;
    dark?: string;
    background?: {
        light?: string;
        dark?: string;
    };
    anchors?: AnchorColor;
    /**
     * `any` type is coming from the zod schema declaration of Colors:
     *
     * > "Prevent strict() from throwing an error when the user defines
     * > a deprecated ultraLight / ultraDark color."
     */
    ultraLight?: any;
    ultraDark?: any;
};
export type AnchorColor = string | Gradient;
export type Gradient = {
    from: string;
    via?: string;
    to: string;
};
