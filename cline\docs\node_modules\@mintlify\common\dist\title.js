export function convertStrToTitle(str) {
    const spacedString = str.replace(/[-_\/\.]/g, ' ');
    const words = spacedString.split(/(?<=[a-z])(?=[A-Z])|(?<=[A-Z])(?=[A-Z][a-z])|\s+/);
    const titleCasedWords = words.map((word) => {
        if (word.length > 1 && word === word.toUpperCase()) {
            return word;
        }
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    });
    return titleCasedWords.join(' ').trim();
}
