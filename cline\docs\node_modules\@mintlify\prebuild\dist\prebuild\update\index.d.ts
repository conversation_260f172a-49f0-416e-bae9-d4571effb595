import { AsyncAPIFile } from '@mintlify/common';
import type { OpenApiFile } from '@mintlify/models';
import { Root } from 'mdast';
type UpdateArgs = {
    contentDirectoryPath: string;
    staticFilenames: string[];
    openApiFiles: OpenApiFile[];
    asyncApiFiles: AsyncAPIFile[];
    contentFilenames: string[];
    snippets: string[];
    snippetV2Filenames: string[];
    docsConfigPath?: string | null;
    localSchema?: boolean;
    groups?: string[];
};
export declare const update: ({ contentDirectoryPath, staticFilenames, openApiFiles, asyncApiFiles, contentFilenames, snippets, snippetV2Filenames, docsConfigPath, localSchema, groups, }: UpdateArgs) => Promise<{
    name: string;
    $schema: string;
    theme: "mint";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        "404": {
            redirect: boolean;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("@mintlify/validation").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    $schema: string;
    theme: "maple";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        "404": {
            redirect: boolean;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("@mintlify/validation").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    $schema: string;
    theme: "palm";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        "404": {
            redirect: boolean;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("@mintlify/validation").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    $schema: string;
    theme: "willow";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        "404": {
            redirect: boolean;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("@mintlify/validation").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    $schema: string;
    theme: "linden";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        "404": {
            redirect: boolean;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("@mintlify/validation").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    $schema: string;
    theme: "almond";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        "404": {
            redirect: boolean;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("@mintlify/validation").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
} | {
    name: string;
    $schema: string;
    theme: "aspen";
    colors: {
        primary: string;
        light?: string | undefined;
        dark?: string | undefined;
    };
    navigation: ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    }) & ({
        languages: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").LanguageNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        versions: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").VersionNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        tabs: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").TabNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        dropdowns: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").DropdownNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        anchors: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").AnchorNavigation<"default">[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        groups: ({
            openapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            group: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            asyncapi: (string | string[] | {
                source: string;
                directory?: string | undefined;
            }) & (string | string[] | {
                source: string;
                directory?: string | undefined;
            } | undefined);
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        } | {
            group: string;
            pages: any[];
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
            tag?: string | undefined;
            hidden?: boolean | undefined;
            root?: string | undefined;
        })[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | {
        pages: any[];
        global?: import("@mintlify/validation/dist/mint-config/schemas/v2/properties/navigation/divisionNav.js").GlobalNavigation | undefined;
    } | undefined);
    description?: string | undefined;
    logo?: string | {
        light: string;
        dark: string;
        href?: string | undefined;
    } | undefined;
    favicon?: string | {
        light: string;
        dark: string;
    } | undefined;
    api?: {
        openapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        asyncapi?: string | string[] | {
            source: string;
            directory?: string | undefined;
        } | undefined;
        params?: {
            expanded?: "all" | "closed" | undefined;
        } | undefined;
        playground?: {
            display?: "simple" | "none" | "interactive" | undefined;
            proxy?: boolean | undefined;
        } | undefined;
        examples?: {
            defaults?: "all" | "required" | undefined;
            languages?: string[] | undefined;
        } | undefined;
        mdx?: {
            auth?: {
                method?: "key" | "bearer" | "basic" | "cobo" | undefined;
                name?: string | undefined;
            } | undefined;
            server?: string | string[] | undefined;
        } | undefined;
    } | undefined;
    appearance?: {
        default?: "light" | "dark" | "system" | undefined;
        strict?: boolean | undefined;
    } | undefined;
    background?: {
        image?: string | {
            light: string;
            dark: string;
        } | undefined;
        decoration?: "gradient" | "grid" | "windows" | undefined;
        color?: {
            light?: string | undefined;
            dark?: string | undefined;
        } | undefined;
    } | undefined;
    navbar?: {
        links?: {
            href: string;
            label: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        }[] | undefined;
        primary?: {
            type: "button";
            href: string;
            label: string;
        } | {
            type: "github";
            href: string;
        } | undefined;
    } | undefined;
    footer?: {
        socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
        links?: {
            items: {
                href: string;
                label: string;
            }[];
            header?: string | undefined;
        }[] | undefined;
    } | undefined;
    search?: {
        prompt?: string | undefined;
    } | undefined;
    seo?: {
        metatags?: Record<string, string> | undefined;
        indexing?: "all" | "navigable" | undefined;
    } | undefined;
    fonts?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | {
        heading?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
        body?: {
            family: string;
            weight?: number | undefined;
            source?: string | undefined;
            format?: "woff" | "woff2" | undefined;
        } | undefined;
    } | undefined;
    icons?: {
        library: "fontawesome" | "lucide";
    } | undefined;
    styling?: {
        eyebrows?: "section" | "breadcrumbs" | undefined;
        codeblocks?: "dark" | "system" | undefined;
    } | undefined;
    redirects?: {
        source: string;
        destination: string;
        permanent?: boolean | undefined;
    }[] | undefined;
    integrations?: {
        amplitude?: {
            apiKey: string;
        } | undefined;
        clearbit?: {
            publicApiKey: string;
        } | undefined;
        fathom?: {
            siteId: string;
        } | undefined;
        frontchat?: {
            snippetId: string;
        } | undefined;
        ga4?: {
            measurementId: string;
        } | undefined;
        gtm?: {
            tagId: string;
        } | undefined;
        heap?: {
            appId: string;
        } | undefined;
        hotjar?: {
            hjid: string;
            hjsv: string;
        } | undefined;
        intercom?: {
            appId: string;
        } | undefined;
        koala?: {
            publicApiKey: string;
        } | undefined;
        logrocket?: {
            appId: string;
        } | undefined;
        mixpanel?: {
            projectToken: string;
        } | undefined;
        osano?: {
            scriptSource: string;
        } | undefined;
        pirsch?: {
            id: string;
        } | undefined;
        posthog?: {
            apiKey: string;
            apiHost?: string | undefined;
        } | undefined;
        plausible?: {
            domain: string;
            server?: string | undefined;
        } | undefined;
        segment?: {
            key: string;
        } | undefined;
        telemetry?: {
            enabled?: boolean | undefined;
        } | undefined;
        cookies?: {
            key?: string | undefined;
            value?: string | undefined;
        } | undefined;
    } | undefined;
    banner?: {
        content: string;
        dismissible?: boolean | undefined;
    } | undefined;
    errors?: {
        "404": {
            redirect: boolean;
        };
    } | undefined;
    contextual?: {
        options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
            href: (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            }) & (string | {
                base: string;
                query?: {
                    value: string;
                    key: string;
                }[] | undefined;
            } | undefined);
            title: string;
            description: string;
            icon?: string | {
                name: string;
                style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
                library?: "fontawesome" | "lucide" | undefined;
            } | undefined;
        })[];
    } | undefined;
    thumbnails?: {
        appearance?: import("@mintlify/validation").ThumbnailAppearance | undefined;
        background?: string | undefined;
    } | undefined;
}>;
export declare const writeMdxFilesWithNoImports: (mdxFilesWithNoImports: {
    targetPath: string;
    tree: Root;
}[]) => Promise<void>[];
export * from './mintConfig/index.js';
export * from './docsConfig/index.js';
export * from './ConfigUpdater.js';
