{"type": "object", "additionalProperties": false, "required": ["name"], "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/2.4.0/externalDocs.json"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.4.0/specificationExtension.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.4.0/tag.json"}