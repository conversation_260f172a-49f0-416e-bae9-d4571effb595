import remarkStringify from 'remark-stringify';
import { unified } from 'unified';
import { visit } from 'unist-util-visit';
import { coreRemarkMdxPlugins } from '../../remark.js';
export const remarkMdxExtractPanel = (mdxExtracts) => {
    return (tree) => {
        let esmContent = '';
        let panelContent;
        const stringifyNode = (node) => {
            try {
                return unified().use(coreRemarkMdxPlugins).use(remarkStringify).stringify(node);
            }
            catch (error) {
                console.error('Error converting MDX content to markdown:', error);
                return '';
            }
        };
        visit(tree, 'mdxjsEsm', (node) => {
            esmContent += stringifyNode({
                type: 'root',
                children: [node],
            });
        });
        visit(tree, 'mdxJsxFlowElement', (node, i, parent) => {
            if (node.name === 'Panel') {
                panelContent = stringifyNode({
                    type: 'root',
                    children: node.children,
                });
                if (parent && i != null) {
                    parent.children.splice(i, 1);
                }
            }
        });
        if (esmContent || panelContent) {
            const content = [esmContent, panelContent].filter((content) => !!content).join('\n');
            if (!!panelContent && mdxExtracts) {
                mdxExtracts.panel = { content };
            }
        }
        return tree;
    };
};
