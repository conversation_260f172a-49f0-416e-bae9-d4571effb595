export type RSSItem = {
    title: string;
    date: string;
    anchor: string;
    description?: string;
    categories?: string[];
};
export type SavedRssFile = {
    rssPath: string;
    updates: RSSItem[];
    orgName: string;
    orgDescription?: string;
    title?: string;
    description?: string;
};
export type RSSItemV2 = RSSItem & {
    content?: string;
};
export type SavedRssFileV2 = SavedRssFile & {
    updates: RSSItemV2[];
};
