import { MetaOptions } from './metaOptions.js';
export type Meta = {
    filename?: string;
    icon?: string;
    lang?: string;
    lines?: boolean;
    wrap?: boolean;
    expandable?: boolean;
    highlight?: number[];
    focus?: number[];
};
export type MetaParserConfig = {
    [K in keyof Meta]: {
        keys: string[];
        parser: (metaOptions: MetaOptions) => Meta[K];
        defaultValue?: Meta[K];
    };
};
export declare const META_PARSER_CONFIG: MetaParserConfig;
export declare function parseMetaString(input: string): Meta;
