import { z } from 'zod';
export declare const footerLinkSchema: z.ZodObject<{
    label: z.ZodString;
    href: z.ZodString;
}, "strip", z.ZodTypeAny, {
    href: string;
    label: string;
}, {
    href: string;
    label: string;
}>;
export declare const footerLinksColumnSchema: z.ZodObject<{
    header: z.ZodOptional<z.ZodString>;
    items: z.Zod<PERSON>y<z.ZodObject<{
        label: z.ZodString;
        href: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        href: string;
        label: string;
    }, {
        href: string;
        label: string;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    items: {
        href: string;
        label: string;
    }[];
    header?: string | undefined;
}, {
    items: {
        href: string;
        label: string;
    }[];
    header?: string | undefined;
}>;
export declare const footerSocialsSchema: z.<PERSON><z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>, z.ZodString>;
export declare const footerSchema: z.ZodObject<{
    socials: z.ZodOptional<z.ZodRecord<z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>, z.ZodString>>;
    links: z.ZodOptional<z.ZodArray<z.ZodObject<{
        header: z.ZodOptional<z.ZodString>;
        items: z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            href: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            href: string;
            label: string;
        }, {
            href: string;
            label: string;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        items: {
            href: string;
            label: string;
        }[];
        header?: string | undefined;
    }, {
        items: {
            href: string;
            label: string;
        }[];
        header?: string | undefined;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
    links?: {
        items: {
            href: string;
            label: string;
        }[];
        header?: string | undefined;
    }[] | undefined;
}, {
    socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | undefined;
    links?: {
        items: {
            href: string;
            label: string;
        }[];
        header?: string | undefined;
    }[] | undefined;
}>;
