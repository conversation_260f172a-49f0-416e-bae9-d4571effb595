import type { Element } from 'hast';
import type { <PERSON><PERSON><PERSON><PERSON>, HastNodeIndex, HastNodeParent } from '../types/hast.js';
export declare function gitBookScrapeCardGroup(node: HastNode, _: HastNodeIndex, parent: HastNodeParent): Element | undefined;
export declare function readmeScrapeCardGroup(node: HastNode, _: HastNodeIndex, parent: HastNodeParent): Element | undefined;
export declare function docusaurusScrapeCardGroup(node: HastN<PERSON>, _: HastNodeIndex, parent: HastNodeParent): Element | undefined;
