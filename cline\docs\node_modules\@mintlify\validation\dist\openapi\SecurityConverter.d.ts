import { OpenAPIV3_1 } from 'openapi-types';
import { BaseConverter } from './BaseConverter.js';
import { SecurityOption } from './types/endpoint.js';
export declare class SecurityConverter extends BaseConverter {
    readonly securityRequirements: OpenAPIV3_1.SecurityRequirementObject[] | undefined;
    readonly securitySchemes: OpenAPIV3_1.ComponentsObject['securitySchemes'];
    readonly safeParse: boolean;
    private constructor();
    private convert;
    private addSecurityParameters;
    static convert(securityRequirements: OpenAPIV3_1.SecurityRequirementObject[] | undefined, securitySchemes: OpenAPIV3_1.ComponentsObject['securitySchemes'], safeParse?: boolean): SecurityOption[];
}
