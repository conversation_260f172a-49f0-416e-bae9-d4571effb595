/**
 * @import {ListContent} from 'mdast'
 */
/**
 * Infer whether list items are spread.
 *
 * @param {Readonly<Array<Readonly<ListContent>>>} children
 *   List items.
 * @returns {boolean}
 *   Whether one or more list items are spread.
 */
export function listItemsSpread(children: Readonly<Array<Readonly<ListContent>>>): boolean;
import type { ListContent } from 'mdast';
//# sourceMappingURL=list-items-spread.d.ts.map