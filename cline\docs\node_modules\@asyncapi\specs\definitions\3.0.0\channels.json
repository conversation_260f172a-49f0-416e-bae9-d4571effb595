{"type": "object", "description": "An object containing all the Channel Object definitions the Application MUST use during runtime.", "additionalProperties": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/channel.json"}]}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/channels.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/channels.json"}