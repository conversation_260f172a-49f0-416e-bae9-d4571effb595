{"type": "object", "description": "Relative paths to the individual topics. They must be relative to the 'baseTopic'.", "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.1.0/vendorExtension.json"}, "^[^.]": {"$ref": "http://asyncapi.com/definitions/1.1.0/topicItem.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.1.0/topics.json"}