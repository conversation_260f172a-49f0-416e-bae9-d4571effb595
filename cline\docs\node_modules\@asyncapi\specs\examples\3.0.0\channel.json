[{"address": "users.{userId}", "title": "Users channel", "description": "This channel is used to exchange messages about user events.", "messages": {"userSignedUp": {"$ref": "#/components/messages/userSignedUp"}, "userCompletedOrder": {"$ref": "#/components/messages/userCompletedOrder"}}, "parameters": {"userId": {"$ref": "#/components/parameters/userId"}}, "servers": [{"$ref": "#/servers/rabbitmqInProd"}, {"$ref": "#/servers/rabbitmqInStaging"}], "bindings": {"amqp": {"is": "queue", "queue": {"exclusive": true}}}, "tags": [{"name": "user", "description": "User-related messages"}], "externalDocs": {"description": "Find more info here", "url": "https://example.com"}}]