// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface UiServiceDefinition {
  // UI service methods
}

export enum WebviewProviderType {
  VSCODE = "VSCODE",
  EXTERNAL = "EXTERNAL"
}

export interface WebviewProviderTypeRequest {
  type: WebviewProviderType;
}

export interface UIState {
  showWelcome: boolean;
  showSettings: boolean;
  showHistory: boolean;
  showAccount: boolean;
  showMcp: boolean;
  mcpTab?: string;
}

export interface UpdateUIStateRequest {
  state: Partial<UIState>;
}

export enum ClineAsk {
  FOLLOWUP = "FOLLOWUP",
  PLAN_MODE_RESPOND = "PLAN_MODE_RESPOND",
  COMMAND = "COMMAND",
  COMMAND_OUTPUT = "COMMAND_OUTPUT",
  COMPLETION_RESULT = "COMPLETION_RESULT",
  TOOL = "TOOL",
  API_REQ_FAILED = "API_REQ_FAILED",
  RESUME_TASK = "RESUME_TASK",
  RESUME_COMPLETED_TASK = "RESUME_COMPLETED_TASK",
  MISTAKE_LIMIT_REACHED = "MISTAKE_LIMIT_REACHED",
  AUTO_APPROVAL_MAX_REQ_REACHED = "AUTO_APPROVAL_MAX_REQ_REACHED",
  BROWSER_ACTION_LAUNCH = "BROWSER_ACTION_LAUNCH",
  USE_MCP_SERVER = "USE_MCP_SERVER",
  NEW_TASK = "NEW_TASK",
  CONDENSE = "CONDENSE",
  REPORT_BUG = "REPORT_BUG"
}

export enum ClineSay {
  TASK = "TASK",
  ERROR = "ERROR",
  API_REQ_STARTED = "API_REQ_STARTED",
  API_REQ_FINISHED = "API_REQ_FINISHED",
  TEXT = "TEXT",
  COMPLETION_RESULT = "COMPLETION_RESULT",
  USER_FEEDBACK = "USER_FEEDBACK",
  USER_FEEDBACK_DIFF = "USER_FEEDBACK_DIFF",
  API_REQ_RETRIED = "API_REQ_RETRIED",
  COMMAND = "COMMAND",
  COMMAND_OUTPUT = "COMMAND_OUTPUT",
  TOOL = "TOOL",
  BROWSER_ACTION = "BROWSER_ACTION",
  BROWSER_ACTION_RESULT = "BROWSER_ACTION_RESULT",
  INSPECT_SITE_RESULT = "INSPECT_SITE_RESULT",
  MCP_SERVER_REQUEST_STARTED = "MCP_SERVER_REQUEST_STARTED",
  MCP_SERVER_REQUEST_FINISHED = "MCP_SERVER_REQUEST_FINISHED",
  SHELL_INTEGRATION_WARNING = "SHELL_INTEGRATION_WARNING"
}

export enum ClineMessageType {
  ASK = "ASK",
  SAY = "SAY"
}

export interface ClineMessage {
  type: ClineMessageType;
  ask?: ClineAsk;
  say?: ClineSay;
  text?: string;
  images?: string[];
}
