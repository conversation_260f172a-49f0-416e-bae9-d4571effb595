import { PageMetaTags } from '../types/pageMetaTags.js';
import { IconType } from './iconTypes.js';
export type Navigation = NavigationGroup[];
export type NavigationGroup = {
    group: string;
    pages: NavigationEntry[];
    version?: string;
    icon?: string;
    iconType?: IconType;
};
export type NavigationEntry = NavigationGroup | string;
export type DecoratedNavigationGroup = {
    group: string;
    version?: string;
    pages: DecoratedNavigationEntry[];
    icon?: string;
    iconType?: IconType;
};
export type DecoratedNavigationEntry = DecoratedNavigationGroup | DecoratedNavigationPage;
export type DecoratedNavigationPage = PageMetaTags & Required<Pick<PageMetaTags, 'href' | 'title'>>;
export type DecoratedNavigation = DecoratedNavigationGroup[];
