import { visit, EXIT } from 'unist-util-visit';
import { framework } from '../utils/detectFramework.js';
export function retrieveRootContent(rootNode) {
    let rootTagName = 'main';
    switch (framework.vendor) {
        case 'docusaurus':
            rootTagName = 'article';
            break;
        case 'gitbook':
            rootTagName = 'main';
            break;
        case 'readme':
            rootTagName = 'article';
            break;
    }
    let rootSelector = 'break-anywhere';
    switch (framework.vendor) {
        case 'docusaurus':
            rootSelector = '';
            break;
        case 'gitbook':
            rootSelector = '';
            break;
        case 'readme':
            rootSelector = 'rm-Article';
            break;
    }
    let element = undefined;
    visit(rootNode, 'element', function (node) {
        if (node.tagName === rootTagName) {
            if (rootSelector) {
                if (node.properties.className &&
                    Array.isArray(node.properties.className) &&
                    node.properties.className.includes(rootSelector)) {
                    element = node;
                }
            }
            else {
                element = node;
                return EXIT;
            }
        }
    });
    return element;
}
//# sourceMappingURL=retrieve.js.map