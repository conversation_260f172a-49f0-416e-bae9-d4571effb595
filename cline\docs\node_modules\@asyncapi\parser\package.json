{"name": "@asyncapi/parser", "version": "3.4.0", "description": "JavaScript AsyncAPI parser.", "private": false, "bugs": {"url": "https://github.com/asyncapi/parser-js/issues"}, "repository": {"type": "git", "url": "git://github.com/asyncapi/parser-js.git"}, "publishConfig": {"access": "public"}, "license": "Apache-2.0", "homepage": "https://github.com/asyncapi/parser-js", "sideEffects": false, "main": "cjs/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "files": ["/esm", "/cjs", "/browser", "LICENSE", "README.md"], "scripts": {"build": "npm run build:esm && npm run build:cjs && npm run build:browser", "build:esm": "tsc", "build:cjs": "tsc --project ./tsconfig.cjs.json", "build:browser": "webpack", "test": "npm run test:unit && npm run test:browser", "test:unit": "cross-env CI=true jest --coverage --testPathIgnorePatterns=test/browser/*", "test:browser": "npm run build:browser && cross-env CI=true jest -- ./test/browser/*", "lint": "eslint --max-warnings 0 --config ../../.eslintrc --ignore-path ../../.eslintignore .", "lint:fix": "eslint --max-warnings 0 --config ../../.eslintrc --ignore-path ../../.eslintignore . --fix", "generate:readme:toc": "markdown-toc -i \"../../README.md\"", "generate:assets": "npm run generate:readme:toc", "bump:version": "npm --no-git-tag-version --allow-same-version version $VERSION", "prepublishOnly": "npm run generate:assets"}, "dependencies": {"@asyncapi/specs": "^6.8.0", "@openapi-contrib/openapi-schema-to-json-schema": "~3.2.0", "@stoplight/json": "3.21.0", "@stoplight/json-ref-readers": "^1.2.2", "@stoplight/json-ref-resolver": "^3.1.5", "@stoplight/spectral-core": "^1.18.3", "@stoplight/spectral-functions": "^1.7.2", "@stoplight/spectral-parsers": "^1.0.2", "@stoplight/spectral-ref-resolver": "^1.0.3", "@stoplight/types": "^13.12.0", "@types/json-schema": "^7.0.11", "@types/urijs": "^1.19.19", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "ajv-formats": "^2.1.1", "avsc": "^5.7.5", "js-yaml": "^4.1.0", "jsonpath-plus": "^10.0.0", "node-fetch": "2.6.7"}, "devDependencies": {"@asyncapi/avro-schema-parser": "^3.0.22", "@jest/types": "^29.0.2", "@swc/core": "^1.2.248", "@swc/jest": "^0.2.22", "@types/jest": "^27.4.1", "@types/js-yaml": "^4.0.5", "@types/node": "^18.16.1", "@types/node-fetch": "^2.6.2", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "browserify": "^16.3.0", "browserify-shim": "^3.8.14", "cross-env": "^7.0.3", "eslint": "^8.23.0", "eslint-plugin-github": "^4.3.7", "eslint-plugin-security": "^1.5.0", "eslint-plugin-sonarjs": "^0.15.0", "jest": "^29.0.2", "markdown-toc": "^1.2.0", "path-browserify": "^1.0.1", "puppeteer": "^17.1.1", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "typescript": "^4.8.2", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.6.1", "webpack-cli": "^4.10.0"}, "browserify": {"transform": ["browserify-shim"]}, "browserify-shim": {"node-fetch": "global:fetch"}}