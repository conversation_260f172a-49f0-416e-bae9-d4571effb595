import { IDiagnostic, JsonPath } from '@stoplight/types';
import type { JSONSchema7 } from 'json-schema';
import type { Resolver } from '@stoplight/spectral-ref-resolver';
export interface IConstructorOpts {
    resolver?: Resolver;
}
export interface IRunOpts {
    ignoreUnknownFormat?: boolean;
}
export interface ISpectralDiagnostic extends IDiagnostic {
    path: JsonPath;
    code: string | number;
    documentationUrl?: string;
}
export declare type IRuleResult = ISpectralDiagnostic;
export interface ISpectralFullResult {
    resolved: unknown;
    results: IRuleResult[];
}
export interface IGivenNode {
    path: JsonPath;
    value: unknown;
}
export declare type JSONSchema = JSONSchema7;
