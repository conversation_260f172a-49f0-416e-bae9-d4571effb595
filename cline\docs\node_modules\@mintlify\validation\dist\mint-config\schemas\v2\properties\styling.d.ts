import { z } from 'zod';
export declare const stylingSchema: z.Zod<PERSON><{
    eyebrows: z.<PERSON><z.<PERSON>od<PERSON><["section", "breadcrumbs"]>>;
    codeblocks: z.<PERSON>ption<PERSON><z.<PERSON>od<PERSON><["system", "dark"]>>;
}, "strip", z.<PERSON>, {
    eyebrows?: "section" | "breadcrumbs" | undefined;
    codeblocks?: "dark" | "system" | undefined;
}, {
    eyebrows?: "section" | "breadcrumbs" | undefined;
    codeblocks?: "dark" | "system" | undefined;
}>;
