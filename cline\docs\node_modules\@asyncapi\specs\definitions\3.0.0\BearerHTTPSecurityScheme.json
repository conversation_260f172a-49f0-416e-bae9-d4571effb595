{"type": "object", "required": ["type", "scheme"], "properties": {"scheme": {"type": "string", "description": "The name of the HTTP Authorization scheme to be used in the Authorization header as defined in RFC7235.", "enum": ["bearer"]}, "bearerFormat": {"type": "string", "description": "A hint to the client to identify how the bearer token is formatted. Bearer tokens are usually generated by an authorization server, so this information is primarily for documentation purposes."}, "type": {"type": "string", "description": "The type of the security scheme.", "enum": ["http"]}, "description": {"type": "string", "description": "A short description for security scheme. CommonMark syntax MAY be used for rich text representation."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/BearerHTTPSecurityScheme.json"}