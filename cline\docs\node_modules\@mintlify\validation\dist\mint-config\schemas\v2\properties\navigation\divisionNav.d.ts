import { z } from 'zod';
import { Division } from '../reusable/divisions.js';
import { BaseAnchorSchema } from './anchors.js';
import { BaseDropdownSchema } from './dropdown.js';
import { GroupsConfig, DecoratedGroupsConfig } from './groups.js';
import { BaseLanguageSchema } from './languages.js';
import { PagesConfig, DecoratedPagesConfig } from './pages.js';
import { BaseTabSchema } from './tabs.js';
import { BaseVersionSchema } from './version.js';
type NavigationType = 'default' | 'decorated';
declare const SomeApiSchema: z.ZodObject<{
    openapi: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
        source: z.ZodEffects<z.ZodString, string, string>;
        directory: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        source: string;
        directory?: string | undefined;
    }, {
        source: string;
        directory?: string | undefined;
    }>]>>;
    asyncapi: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
        source: z.ZodEffects<z.ZodString, string, string>;
        directory: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        source: string;
        directory?: string | undefined;
    }, {
        source: string;
        directory?: string | undefined;
    }>]>>;
}, "strip", z.ZodTypeAny, {
    openapi?: string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined;
    asyncapi?: string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined;
}, {
    openapi?: string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined;
    asyncapi?: string | string[] | {
        source: string;
        directory?: string | undefined;
    } | undefined;
}>;
type OtherConfigs<T extends NavigationType> = T extends 'default' ? z.infer<typeof SomeApiSchema> & {
    global?: GlobalNavigation;
} : {
    global?: GlobalNavigation;
};
type BaseNavigation<T extends NavigationType, K extends Division> = {
    languages: K extends 'languages' ? never : LanguageNavigation<T>[];
} | {
    versions: K extends 'versions' ? never : VersionNavigation<T>[];
} | {
    dropdowns: K extends 'dropdowns' ? never : DropdownNavigation<T>[];
} | {
    tabs: K extends 'tabs' ? never : TabNavigation<T>[];
} | {
    anchors: K extends 'anchors' ? never : AnchorNavigation<T>[];
} | {
    groups: T extends 'default' ? GroupsConfig : DecoratedGroupsConfig;
} | {
    pages: T extends 'default' ? PagesConfig : DecoratedPagesConfig;
} | {
    href: string;
};
export type AnchorNavigation<T extends NavigationType> = BaseAnchorSchema & (OtherConfigs<T> | (BaseNavigation<T, 'anchors'> & OtherConfigs<T>));
export type LanguageNavigation<T extends NavigationType> = BaseLanguageSchema & (OtherConfigs<T> | (BaseNavigation<T, 'languages'> & OtherConfigs<T>));
export type VersionNavigation<T extends NavigationType> = BaseVersionSchema & (OtherConfigs<T> | (BaseNavigation<T, 'versions'> & OtherConfigs<T>));
export type DropdownNavigation<T extends NavigationType> = BaseDropdownSchema & (OtherConfigs<T> | (BaseNavigation<T, 'dropdowns'> & OtherConfigs<T>));
export type TabNavigation<T extends NavigationType> = BaseTabSchema & (OtherConfigs<T> | (BaseNavigation<T, 'tabs'> & OtherConfigs<T>));
export type GlobalNavigation = {
    languages?: (BaseLanguageSchema & {
        href: string;
    })[];
    versions?: (BaseVersionSchema & {
        href: string;
    })[];
    tabs?: (BaseTabSchema & {
        href: string;
    })[];
    dropdowns?: (BaseDropdownSchema & {
        href: string;
    })[];
    anchors?: (BaseAnchorSchema & {
        href: string;
    })[];
};
export {};
