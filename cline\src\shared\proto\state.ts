// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface StateServiceDefinition {
  // State service methods
}

export enum PlanActMode {
  PLAN_AND_ACT = "PLAN_AND_ACT",
  PLAN_ONLY = "PLAN_ONLY",
  ACT_ONLY = "ACT_ONLY"
}

export interface TogglePlanActModeRequest {
  mode: PlanActMode;
}

export enum TelemetrySettingEnum {
  ENABLED = "ENABLED",
  DISABLED = "DISABLED",
  NOT_SET = "NOT_SET"
}

export interface TelemetrySettingRequest {
  setting: TelemetrySettingEnum;
}

export interface UpdateSettingsRequest {
  settings: any; // Replace with proper settings type
}

export interface ResetStateRequest {}

export interface TerminalProfile {
  name: string;
  path: string;
  args?: string[];
  env?: { [key: string]: string };
}
