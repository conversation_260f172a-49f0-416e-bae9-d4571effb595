// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface WebServiceDefinition {
  // Web service methods
}

export interface WebRequest {
  url: string;
  method?: string;
  headers?: { [key: string]: string };
  body?: string;
}

export interface WebResponse {
  status: number;
  headers: { [key: string]: string };
  body: string;
}

export interface BrowserAction {
  type: string;
  target?: string;
  value?: string;
  coordinates?: {
    x: number;
    y: number;
  };
}

export interface BrowserActionRequest {
  action: BrowserAction;
}

export interface BrowserActionResponse {
  success: boolean;
  error?: string;
  screenshot?: string;
}
