{"version": 3, "file": "render-border.js", "sourceRoot": "", "sources": ["../src/render-border.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,WAAW,CAAC;AACjC,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,QAAQ,MAAM,eAAe,CAAC;AAIrC,MAAM,YAAY,GAAG,CACpB,CAAS,EACT,CAAS,EACT,IAAa,EACb,MAAc,EACP,EAAE;IACT,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,gBAAgB,EAAE,CAAC;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAS,CAAC,iBAAiB,EAAE,CAAC;QAClD,MAAM,GAAG,GACR,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,QAAQ;YACzC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;YAClC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;QAE3B,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;QAC3E,MAAM,iBAAiB,GACtB,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;QACxD,MAAM,eAAe,GACpB,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;QACtD,MAAM,gBAAgB,GACrB,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;QAEvD,MAAM,iBAAiB,GACtB,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAE3D,MAAM,oBAAoB,GACzB,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAE9D,MAAM,kBAAkB,GACvB,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAE5D,MAAM,mBAAmB,GACxB,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAE7D,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC;QACrD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC;QAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC;QACvD,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC;QAEzD,MAAM,YAAY,GACjB,KAAK,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9D,IAAI,SAAS,GAAG,aAAa;YAC5B,CAAC,CAAC,QAAQ,CACR,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC5B,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EACtC,cAAc,EACd,YAAY,CACZ;YACF,CAAC,CAAC,SAAS,CAAC;QAEb,IAAI,aAAa,IAAI,iBAAiB,EAAE,CAAC;YACxC,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,oBAAoB,GAAG,MAAM,CAAC;QAElC,IAAI,aAAa,EAAE,CAAC;YACnB,oBAAoB,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACtB,oBAAoB,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,UAAU,GAAG,CAChB,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,EAAE,YAAY,CAAC,GAAG,IAAI,CACxD,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAE/B,IAAI,kBAAkB,EAAE,CAAC;YACxB,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,WAAW,GAAG,CACjB,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,gBAAgB,EAAE,YAAY,CAAC,GAAG,IAAI,CAC1D,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAE/B,IAAI,mBAAmB,EAAE,CAAC;YACzB,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,YAAY,GAAG,gBAAgB;YAClC,CAAC,CAAC,QAAQ,CACR,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC/B,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EACzC,iBAAiB,EACjB,YAAY,CACZ;YACF,CAAC,CAAC,SAAS,CAAC;QAEb,IAAI,gBAAgB,IAAI,oBAAoB,EAAE,CAAC;YAC9C,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxC,CAAC;QAED,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC,IAAI,SAAS,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,EAAC,YAAY,EAAE,EAAE,EAAC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,cAAc,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,UAAU,EAAE,EAAC,YAAY,EAAE,EAAE,EAAC,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACrB,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,WAAW,EAAE;gBACrD,YAAY,EAAE,EAAE;aAChB,CAAC,CAAC;QACJ,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,YAAY,EAAE,EAAC,YAAY,EAAE,EAAE,EAAC,CAAC,CAAC;QACnE,CAAC;IACF,CAAC;AACF,CAAC,CAAC;AAEF,eAAe,YAAY,CAAC"}