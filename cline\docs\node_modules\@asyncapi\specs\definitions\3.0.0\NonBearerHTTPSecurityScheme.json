{"not": {"type": "object", "properties": {"scheme": {"type": "string", "description": "A short description for security scheme.", "enum": ["bearer"]}}}, "type": "object", "required": ["scheme", "type"], "properties": {"scheme": {"type": "string", "description": "The name of the HTTP Authorization scheme to be used in the Authorization header as defined in RFC7235."}, "description": {"type": "string", "description": "A short description for security scheme."}, "type": {"type": "string", "description": "The type of the security scheme.", "enum": ["http"]}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/NonBearerHTTPSecurityScheme.json"}