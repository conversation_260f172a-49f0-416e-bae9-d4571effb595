{"version": 3, "file": "rule.js", "sourceRoot": "", "sources": ["../../src/ruleset/rule.ts"], "names": [], "mappings": ";;;;;AAAA,mCAAkC;AAElC,0CAAoD;AACpD,0CAAgD;AAEhD,+CAAiF;AAIjF,iDAA8C;AAC9C,uCAAoC;AACpC,mCAAuC;AAyBvC,MAAa,IAAI;IAaf,YACkB,IAAY,EACZ,UAA0B,EAC1B,KAAc;;QAFd,SAAI,GAAJ,IAAI,CAAQ;QACZ,eAAU,GAAV,UAAU,CAAgB;QAC1B,UAAK,GAAL,KAAK,CAAS;QAbhC,iCAA+B;QAG/B,gCAAkB;QAIlB,6BAAoB;QACpB,8BAAkB;QAOhB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,KAAK,KAAK,CAAC;QACpD,oCAAA,IAAI,iBAAY,IAAI,CAAC,WAAW,MAAA,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,MAAA,UAAU,CAAC,WAAW,mCAAI,IAAI,CAAC;QAClD,IAAI,CAAC,OAAO,GAAG,MAAA,UAAU,CAAC,OAAO,mCAAI,IAAI,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,MAAA,UAAU,CAAC,gBAAgB,mCAAI,IAAI,CAAC;QAC5D,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,KAAK,KAAK,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,SAAS,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAChF,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,MAAA,UAAU,CAAC,UAAU,mCAAI,IAAI,CAAC;IAClD,CAAC;IAID,IAAW,OAAO;QAChB,OAAO,oCAAA,IAAI,qBAAS,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,IAAW,OAAO,CAAC,OAAgB;QACjC,oCAAA,IAAI,iBAAY,OAAO,MAAA,CAAC;IAC1B,CAAC;IAEM,MAAM,CAAC,SAAS,CAAC,IAAW,EAAE,QAAuC;QAC1E,OAAO,QAAQ,KAAK,KAAK,IAAI,CAAC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;IAChF,CAAC;IAEM,oBAAoB,CAAC,MAAc,EAAE,IAAc;QACxD,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;QAED,MAAM,cAAc,GAAG,IAAA,eAAQ,EAAC,IAAA,cAAO,EAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,CAAC;QAC/E,MAAM,iBAAiB,GAA2C,EAAE,CAAC;QAErE,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;YACpE,IAAI,IAAA,qBAAS,EAAC,cAAc,EAAE,MAAM,CAAC,EAAE;gBACrC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAClC;SACF;QAED,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;QAED,IAAI,QAAQ,GAAuB,IAAI,CAAC,QAAQ,CAAC;QACjD,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,MAAM,OAAO,GAAG,IAAA,oBAAa,EAAC,IAAI,CAAC,CAAC;QAEpC,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE;YAChD,KAAK,MAAM,CAAC,YAAY,EAAE,gBAAgB,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE;gBACzE,IACE,YAAY,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM;oBAC5C,CAAC,OAAO,KAAK,YAAY,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC,EACpE;oBACA,cAAc,GAAG,YAAY,CAAC;oBAC9B,QAAQ,GAAG,gBAAgB,CAAC;iBAC7B;aACF;SACF;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,IAAW,QAAQ;QACjB,OAAO,oCAAA,IAAI,sBAAU,CAAC;IACxB,CAAC;IAED,IAAW,QAAQ,CAAC,QAAwE;QAC1F,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE;YACvB,oCAAA,IAAI,kBAAa,iCAAsB,MAAA,CAAC;SACzC;aAAM;YACL,oCAAA,IAAI,kBAAa,IAAA,gCAAqB,EAAC,QAAQ,CAAC,MAAA,CAAC;SAClD;IACH,CAAC;IAED,IAAW,IAAI;QACb,OAAO,oCAAA,IAAI,kBAAM,CAAC;IACpB,CAAC;IAED,IAAW,IAAI,CAAC,IAA4B;QAC1C,oCAAA,IAAI,cAAS,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAA,CAAC;IACnD,CAAC;IAED,IAAW,KAAK;QACd,OAAO,oCAAA,IAAI,mBAAO,CAAC;IACrB,CAAC;IAED,IAAW,KAAK,CAAC,KAA8B;QAC7C,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAC3D,oCAAA,IAAI,eAAU,IAAI,CAAC,KAAK,CAAC,iBAAiB;YACxC,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAA,oBAAY,EAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,iBAAQ,CAAC,MAAA,CAAC;IACjG,CAAC;IAEM,kBAAkB,CAAC,OAA2B;QACnD,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB;YACjC,CAAC,CAAC,oCAAA,IAAI,mBAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAA,oBAAY,EAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9E,CAAC,CAAC,oCAAA,IAAI,mBAAO,CAAC;IAClB,CAAC;IAEM,aAAa,CAAC,OAA2B;QAC9C,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QAED,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,OAAO,KAAK,CAAC;SACd;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YACjC,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACvB,OAAO,IAAI,CAAC;aACb;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,KAAK;QACV,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1D,CAAC;IAEM,MAAM;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC3B,GAAG,IAAI;gBACP,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;aAC7B,CAAC,CAAC;YACH,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;YAC7F,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;YACpB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;CACF;AAjKD,oBAiKC"}