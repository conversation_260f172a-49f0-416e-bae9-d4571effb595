{"description": "The Schema Object allows the definition of input and output data types. These types can be objects, but also primitives and arrays.", "allOf": [{"$ref": "http://json-schema.org/draft-07/schema#"}, {"patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "properties": {"additionalProperties": {"anyOf": [{"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}, {"type": "boolean"}], "default": {}}, "items": {"anyOf": [{"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}, {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}}], "default": {}}, "allOf": {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}}, "oneOf": {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}}, "anyOf": {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}}, "not": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}, "properties": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}, "default": {}}, "patternProperties": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}, "default": {}}, "propertyNames": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}, "contains": {"$ref": "http://asyncapi.com/definitions/2.6.0/schema.json"}, "discriminator": {"type": "string", "description": "Adds support for polymorphism. The discriminator is the schema property name that is used to differentiate between other schema that inherit this schema. "}, "externalDocs": {"description": "Additional external documentation for this schema.", "$ref": "http://asyncapi.com/definitions/2.6.0/externalDocs.json"}, "deprecated": {"type": "boolean", "default": false, "description": "Specifies that a schema is deprecated and SHOULD be transitioned out of usage"}}}], "example": {"$ref": "http://asyncapi.com/examples/2.6.0/schema.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/schema.json"}