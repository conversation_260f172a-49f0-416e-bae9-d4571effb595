import { zodToJsonSchema } from 'zod-to-json-schema';
import { mintConfigSchema } from './mint-config/schemas/v1/config.js';
import { docsConfigSchema } from './mint-config/schemas/v2/index.js';
import { colorsSchema } from './mint-config/schemas/v2/properties/colors.js';
import { baseLanguageSchema, languageSchema, languagesSchema, pageSchema, } from './mint-config/schemas/v2/properties/index.js';
import { anchorsSchema, anchorSchema, baseAnchorSchema, } from './mint-config/schemas/v2/properties/navigation/anchors.js';
import { baseDropdownSchema, dropdownSchema, dropdownsSchema, } from './mint-config/schemas/v2/properties/navigation/dropdown.js';
import { baseGroupSchema, groupSchema, groupsSchema, } from './mint-config/schemas/v2/properties/navigation/groups.js';
import { navigationSchema } from './mint-config/schemas/v2/properties/navigation/index.js';
import { pagesSchema, pageOrGroupSchema, } from './mint-config/schemas/v2/properties/navigation/pages.js';
import { baseTabSchema, tabSchema, tabsSchema, } from './mint-config/schemas/v2/properties/navigation/tabs.js';
import { baseVersionSchema, versionSchema, versionsSchema, } from './mint-config/schemas/v2/properties/navigation/version.js';
import { hiddenSchema } from './mint-config/schemas/v2/properties/reusable/hidden.js';
import { iconSchema } from './mint-config/schemas/v2/properties/reusable/icon.js';
import { openApiSchema } from './mint-config/schemas/v2/properties/reusable/openapi.js';
export * from './openapi/types/endpoint.js';
export * from './openapi/OpenApiToEndpointConverter.js';
export { stripComponents } from './openapi/stripComponents.js';
export { SchemaConverter } from './openapi/SchemaConverter.js';
export { generateExampleFromSchema } from './openapi/generateExampleFromSchema.js';
export { generateFirstIncrementalSchema, generateNextIncrementalSchema, } from './openapi/IncrementalEvaluator.js';
export * from './mint-config/validateConfig.js';
export { formatIssue } from './mint-config/formatIssue.js';
export { upgradeToDocsConfig } from './mint-config/upgrades/upgradeToDocsConfig.js';
export { convertMintDecoratedNavToDocsDecoratedNav } from './mint-config/upgrades/convertMintDecoratedNavToDocsDecoratedNav.js';
export const mintConfigJsonSchema = zodToJsonSchema(mintConfigSchema, 'Schema');
export const docsConfigJsonSchema = zodToJsonSchema(docsConfigSchema, {
    definitions: {
        anchorSchema,
        anchorsSchema,
        baseAnchorSchema,
        baseDropdownSchema,
        baseGroupSchema,
        baseLanguageSchema,
        baseTabSchema,
        baseVersionSchema,
        colorsSchema,
        dropdownSchema,
        dropdownsSchema,
        groupSchema,
        groupsSchema,
        hiddenSchema,
        iconSchema,
        languageSchema,
        languagesSchema,
        navigationSchema,
        openApiSchema,
        pageOrGroupSchema,
        pageSchema,
        pagesSchema,
        tabSchema,
        tabsSchema,
        versionSchema,
        versionsSchema,
    },
});
export * from './mint-config/schemas/v2/properties/index.js';
export * from './types/index.js';
export * from './chat-config/index.js';
