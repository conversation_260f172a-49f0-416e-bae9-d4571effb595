import { Root } from 'mdast';
import type { ImportSpecifier } from '../../../types/mdx/snippets/import.js';
/**
 *
 * @param importSpecifier The name of the imported component we want to replace
 * @param destinationPageContent The content of the page we want to inject the import into
 * @param importedFileContent The content of the file we are importing from
 * @returns
 */
export declare const resolveImport: (importSpecifier: ImportSpecifier, destinationPageContent: Root, importedFileContent: Root, exportMap: Record<string, string>) => Promise<Root | undefined>;
