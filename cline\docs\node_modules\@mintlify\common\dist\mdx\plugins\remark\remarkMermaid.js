import { visit } from 'unist-util-visit';
const REACT_COMPONENT_NAME = 'Mermaid';
export const remarkMermaid = () => (tree) => {
    const codeblocks = [];
    visit(tree, 'code', (node, index, parent) => {
        if (node.lang === 'mermaid' && parent && typeof index === 'number') {
            codeblocks.push([node, index, parent]);
        }
    });
    if (codeblocks.length !== 0) {
        for (const [node, index, parent] of codeblocks) {
            parent.children.splice(index, 1, {
                type: 'mdxJsxFlowElement',
                name: REACT_COMPONENT_NAME,
                attributes: [
                    {
                        type: 'mdxJsxAttribute',
                        name: 'chart',
                        value: {
                            type: 'mdxJsxAttributeValueExpression',
                            data: {
                                estree: {
                                    body: [
                                        {
                                            type: 'ExpressionStatement',
                                            expression: {
                                                type: 'TemplateLiteral',
                                                expressions: [],
                                                quasis: [
                                                    {
                                                        type: 'TemplateElement',
                                                        value: { raw: node.value },
                                                        tail: true,
                                                    },
                                                ],
                                            },
                                        },
                                    ],
                                    type: 'Program',
                                    sourceType: 'module',
                                },
                            },
                            value: node.value,
                        },
                    },
                ],
                children: [],
            });
        }
    }
};
