import { z } from 'zod';
import { AnchorNavigation } from './divisionNav.js';
export declare const baseAnchorSchema: z.ZodObject<{
    anchor: z.ZodString;
    icon: z.ZodOptional<z.<PERSON>od<PERSON>n<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.Zod<PERSON>ptional<z.Zod<PERSON>num<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.<PERSON>od<PERSON>ptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    color: z.ZodOptional<z.ZodObject<{
        light: z.ZodOptional<z.ZodString>;
        dark: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        light?: string | undefined;
        dark?: string | undefined;
    }>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    anchor: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    hidden?: boolean | undefined;
}, {
    anchor: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    hidden?: boolean | undefined;
}>;
export type BaseAnchorSchema = z.infer<typeof baseAnchorSchema>;
export declare const nonRecursiveAnchorSchema: z.ZodObject<{
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    color: z.ZodOptional<z.ZodObject<{
        light: z.ZodOptional<z.ZodString>;
        dark: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodTypeAny, {
        light?: string | undefined;
        dark?: string | undefined;
    }, {
        light?: string | undefined;
        dark?: string | undefined;
    }>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    anchor: z.ZodString;
    href: z.ZodString;
}, "strip", z.ZodTypeAny, {
    href: string;
    anchor: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    hidden?: boolean | undefined;
}, {
    href: string;
    anchor: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
    color?: {
        light?: string | undefined;
        dark?: string | undefined;
    } | undefined;
    hidden?: boolean | undefined;
}>;
export declare const anchorSchema: z.ZodType<AnchorNavigation<'default'>>;
export declare const decoratedAnchorSchema: z.ZodType<AnchorNavigation<'decorated'>>;
export declare const anchorsSchema: z.ZodArray<z.ZodType<AnchorNavigation<"default">, z.ZodTypeDef, AnchorNavigation<"default">>, "many">;
export declare const decoratedAnchorsSchema: z.ZodArray<z.ZodType<AnchorNavigation<"decorated">, z.ZodTypeDef, AnchorNavigation<"decorated">>, "many">;
export type AnchorsConfig = z.infer<typeof anchorsSchema>;
export type AnchorConfig = z.infer<typeof anchorSchema>;
export type DecoratedAnchorConfig = z.infer<typeof decoratedAnchorSchema>;
export type DecoratedAnchorsConfig = z.infer<typeof decoratedAnchorsSchema>;
