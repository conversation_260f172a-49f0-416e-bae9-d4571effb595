{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/jms/0.0.1/channel.json", "title": "Channel Schema", "description": "This object contains configuration for describing a JMS queue, or FIFO queue as an AsyncAPI channel. This objects only contains configuration that can not be provided in the AsyncAPI standard channel object.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"destination": {"type": "string", "description": "The destination (queue) name for this channel. SHOULD only be specified if the channel name differs from the actual destination name, such as when the channel name is not a valid destination name according to the JMS Provider. Defaults to the channel name."}, "destinationType": {"type": "string", "enum": ["queue", "fifo-queue"], "default": "queue", "description": "The type of destination. SHOULD be specified to document the messaging model (point-to-point, or strict message ordering) supported by this channel."}, "bindingVersion": {"type": "string", "enum": ["0.0.1"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"destination": "user-signed-up", "destinationType": "fifo-queue", "bindingVersion": "0.0.1"}]}