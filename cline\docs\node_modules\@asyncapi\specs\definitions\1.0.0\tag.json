{"type": "object", "additionalProperties": false, "required": ["name"], "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/1.0.0/externalDocs.json"}}, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.0.0/vendorExtension.json"}}, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.0.0/tag.json"}