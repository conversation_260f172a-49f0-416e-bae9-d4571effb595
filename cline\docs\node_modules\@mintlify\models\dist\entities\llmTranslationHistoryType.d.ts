import { LocaleType } from '../mintconfig/localization.js';
import { QueueUpdateStatus } from '../types/index.js';
export type LlmTranslationHistoryType = {
    _id: string;
    deploymentId: string;
    createdAt: Date;
    endedAt?: Date;
    status: QueueUpdateStatus;
    error?: string;
    summary?: string;
    logs?: string[];
    subdomain: string;
    sourceLanguage: LocaleType;
    targetLanguage: LocaleType;
    targetLanguages: LocaleType[];
    sourceTree: string;
    githubUrl: string;
    documentCount: number;
    incremental?: boolean;
};
