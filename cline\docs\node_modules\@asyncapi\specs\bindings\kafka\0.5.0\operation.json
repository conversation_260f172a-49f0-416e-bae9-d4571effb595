{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/kafka/0.5.0/operation.json", "title": "Operation Schema", "description": "This object contains information about the operation representation in Kafka.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"groupId": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json", "description": "Id of the consumer group."}, "clientId": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json", "description": "Id of the consumer inside a consumer group."}, "bindingVersion": {"type": "string", "enum": ["0.5.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"groupId": {"type": "string", "enum": ["myGroupId"]}, "clientId": {"type": "string", "enum": ["myClientId"]}, "bindingVersion": "0.5.0"}]}