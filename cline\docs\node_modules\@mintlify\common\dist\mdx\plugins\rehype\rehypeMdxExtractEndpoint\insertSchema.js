export const insertSchema = ({ locationAttr, node, schema, deepestSchema, parentSchema, nodeToSchema, endpoint, requestContentType, }) => {
    var _a, _b, _c;
    if (parentSchema === undefined) {
        switch (locationAttr.location) {
            case 'body': {
                const { name } = locationAttr;
                const bodySchema = (_a = endpoint.request.body[requestContentType]) === null || _a === void 0 ? void 0 : _a.schemaArray[0];
                if (!bodySchema) {
                    const newBodySchema = name === null
                        ? schema
                        : {
                            type: 'object',
                            properties: {
                                [name]: [schema],
                            },
                        };
                    endpoint.request.body[requestContentType] = {
                        schemaArray: [newBodySchema],
                        examples: {},
                    };
                }
                else if (bodySchema.type === 'object') {
                    // attempting to insert a nameless schema into an object
                    if (name === null) {
                        return false;
                    }
                    bodySchema.properties[name] = [schema];
                }
                else {
                    // attempting to insert a subschema into a non-object
                    return false;
                }
                break;
            }
            case 'response': {
                const { name } = locationAttr;
                const responseObjectSchema = (_c = (_b = endpoint.response['200']) === null || _b === void 0 ? void 0 : _b['application/json']) === null || _c === void 0 ? void 0 : _c.schemaArray[0];
                if (responseObjectSchema) {
                    responseObjectSchema.properties[name] = [schema];
                }
                else {
                    endpoint.response['200'] = {
                        'application/json': {
                            schemaArray: [
                                {
                                    type: 'object',
                                    properties: {
                                        [name]: [schema],
                                    },
                                },
                            ],
                            examples: {},
                        },
                    };
                }
                break;
            }
            case 'auth': {
                const { name } = locationAttr;
                if (!endpoint.request.security[0]) {
                    endpoint.request.security = [
                        {
                            title: 'Security',
                            parameters: {
                                cookie: {},
                                header: { [name]: { type: 'apiKey' } },
                                query: {},
                            },
                        },
                    ];
                }
                else {
                    endpoint.request.security[0].parameters.header[name] = { type: 'apiKey' };
                }
                break;
            }
            case 'cookie':
            case 'header':
            case 'path':
            case 'query': {
                const { name, location } = locationAttr;
                endpoint.request.parameters[location][name] = { schema: [schema] };
                break;
            }
        }
    }
    else {
        const { name, location } = locationAttr;
        if (location === 'auth') {
            console.error('complex types are not allowed in the auth section');
            return false;
        }
        if (parentSchema.type !== 'object') {
            console.error(`cannot add property "${name}" to non-object`);
            return false;
        }
        if (name === null) {
            console.error(`Cannot add nameless property to object`);
            return false;
        }
        parentSchema.properties[name] = [schema];
    }
    nodeToSchema.set(node, deepestSchema);
    return true;
};
