import { YAMLScalar } from './yamlAST';
export declare function parseYamlBoolean(input: string): boolean;
export declare function parseYamlInteger(input: string): number;
export declare function parseYamlBigInteger(input: string): number | bigint;
export declare function parseYamlFloat(input: string): number;
export declare enum ScalarType {
    null = 0,
    bool = 1,
    int = 2,
    float = 3,
    string = 4
}
export declare function determineScalarType(node: YAMLScalar): ScalarType;
