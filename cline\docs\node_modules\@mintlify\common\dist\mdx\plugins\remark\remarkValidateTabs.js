import { visit } from 'unist-util-visit';
export const remarkValidateTabs = () => (tree) => {
    visit(tree, 'mdxJsxFlowElement', (node, _idx, parent) => {
        if (node.name === 'Tab') {
            const parentElement = parent;
            if (!parentElement || parentElement.name !== 'Tabs') {
                console.warn('Please ensure that all <Tab> components are direct children of <Tabs>.');
            }
        }
    });
};
