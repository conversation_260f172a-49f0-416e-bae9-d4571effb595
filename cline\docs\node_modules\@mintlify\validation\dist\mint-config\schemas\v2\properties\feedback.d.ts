import { z } from 'zod';
export declare const feedbackSchema: z.ZodObject<{
    thumbs: z.<PERSON>od<PERSON>ptional<z.ZodBoolean>;
    edits: z.<PERSON>odOptional<z.ZodBoolean>;
    issues: z.<PERSON>odOptional<z.ZodBoolean>;
}, "strip", z.<PERSON>ny, {
    thumbs?: boolean | undefined;
    edits?: boolean | undefined;
    issues?: boolean | undefined;
}, {
    thumbs?: boolean | undefined;
    edits?: boolean | undefined;
    issues?: boolean | undefined;
}>;
