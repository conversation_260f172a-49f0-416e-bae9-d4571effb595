import { join } from 'path';

import { addMdx } from './extension.js';
import { toFilename } from './file.js';
import { log } from './log.js';

export function createFilename(
  rootPath: string = process.cwd(),
  filename: string | URL,
  title?: string
): string | undefined {
  if (typeof filename === 'string' && filename.startsWith('http')) {
    const url = new URL(filename);
    filename = url.pathname;
  } else if (typeof filename === 'object') {
    filename = (filename as URL).pathname;
  } else {
    filename = filename as string;
  }

  if (filename === '') {
    log(`Invalid file name provided: ${filename}`, 'error');
    return undefined;
  }

  return join(rootPath, addMdx(filename || toFilename(title ?? '')));
}
