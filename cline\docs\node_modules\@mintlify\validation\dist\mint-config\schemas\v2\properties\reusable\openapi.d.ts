import { z } from 'zod';
export declare const openapiStringSchema: z.ZodEffects<z.ZodString, string, string>;
export declare const openapiObjSchema: z.ZodObject<{
    source: z.ZodEffects<z.ZodString, string, string>;
    directory: z.ZodOptional<z.ZodString>;
}, "strict", z.ZodTypeAny, {
    source: string;
    directory?: string | undefined;
}, {
    source: string;
    directory?: string | undefined;
}>;
export declare const openApiSchema: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
    source: z.ZodEffects<z.ZodString, string, string>;
    directory: z.ZodOptional<z.ZodString>;
}, "strict", z.ZodTypeAny, {
    source: string;
    directory?: string | undefined;
}, {
    source: string;
    directory?: string | undefined;
}>]>;
export type OpenApiObjConfig = z.infer<typeof openapiObjSchema>;
export type OpenApiConfig = z.infer<typeof openApiSchema>;
