import { z } from 'zod';
export const metatagsSchema = z
    .record(z.string(), z.string().nonempty())
    .optional()
    .describe('Meta tags added to every page. Must be a valid key-value pair');
export const seoSchema = z
    .object({
    metatags: metatagsSchema.optional(),
    indexing: z
        .enum(['navigable', 'all'])
        .optional()
        .describe('Specify which pages to be indexed by search engines. Setting `navigable` indexes pages that are set in navigation, `all` indexes all pages. Defaults to `navigable`.'),
})
    .describe('SEO indexing configurations');
