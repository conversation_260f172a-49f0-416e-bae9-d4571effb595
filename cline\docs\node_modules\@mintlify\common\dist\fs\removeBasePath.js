import { join } from 'path';
import { createPathArr } from './createPathArr.js';
export const removeBasePath = (path, basePath) => {
    if (path.startsWith('https://') || path.startsWith('http://'))
        return path;
    const pathArr = createPathArr(path);
    if (!basePath) {
        return join(...pathArr);
    }
    const basePathArr = createPathArr(basePath);
    const isMatch = basePathArr.every((dir, i) => dir === pathArr[i]);
    if (isMatch) {
        pathArr.splice(0, basePathArr.length);
        return join(...pathArr);
    }
    return join(...pathArr);
};
