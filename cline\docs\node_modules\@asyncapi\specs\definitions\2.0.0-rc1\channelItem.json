{"type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/specificationExtension.json"}}, "minProperties": 1, "properties": {"$ref": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/ReferenceObject.json"}, "parameters": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/parameters.json"}, "publish": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/operation.json"}, "subscribe": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc1/operation.json"}, "deprecated": {"type": "boolean", "default": false}, "protocolInfo": {"type": "object", "additionalProperties": {"type": "object"}}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc1/channelItem.json"}