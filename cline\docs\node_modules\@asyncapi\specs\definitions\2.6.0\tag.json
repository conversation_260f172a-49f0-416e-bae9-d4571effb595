{"type": "object", "description": "Allows adding meta data to a single tag.", "additionalProperties": false, "required": ["name"], "properties": {"name": {"type": "string", "description": "The name of the tag."}, "description": {"type": "string", "description": "A short description for the tag."}, "externalDocs": {"description": "Additional external documentation for this tag.", "$ref": "http://asyncapi.com/definitions/2.6.0/externalDocs.json"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/tag.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/tag.json"}