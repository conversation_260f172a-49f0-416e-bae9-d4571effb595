import { removePosition } from 'unist-util-remove-position';
import { visit, SKIP } from 'unist-util-visit';
export const remarkMdxInjectSnippets = (snippetTreeMap) => (tree, file) => {
    visit(tree, (node, index, parent) => {
        if (parent && index != null && node.type === 'mdxJsxFlowElement') {
            const mdxJsxFlowElement = node;
            if (mdxJsxFlowElement.name === 'Snippet') {
                const fileAttr = mdxJsxFlowElement.attributes.find((attr) => attr.type === 'mdxJsxAttribute' && attr.name === 'file');
                const name = fileAttr === null || fileAttr === void 0 ? void 0 : fileAttr.value;
                if (typeof name === 'string') {
                    const snippet = snippetTreeMap[name];
                    if (snippet) {
                        const fragment = structuredClone(snippet);
                        removePosition(fragment);
                        parent.children.splice(index, 1, ...fragment.children);
                        return [SKIP, index];
                    }
                    else {
                        file.message('Cannot expand missing snippet `' + name + '`', node, 'remark-mdx-inject-snippets');
                    }
                }
            }
        }
    });
};
