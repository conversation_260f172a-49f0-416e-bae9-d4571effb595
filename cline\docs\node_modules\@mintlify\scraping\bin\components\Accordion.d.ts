import type { Element } from 'hast';
import type { <PERSON><PERSON><PERSON><PERSON>, HastNodeIndex, HastNodeParent } from '../types/hast.js';
export declare function gitBookScrapeAccordion(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
export declare function readmeScrapeAccordion(node: HastNode, index: HastNodeIndex, parent: HastNodeParent): Element | undefined;
export declare function docusaurusScrapeAccordion(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
