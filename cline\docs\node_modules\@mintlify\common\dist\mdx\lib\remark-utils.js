export const createMdxJsxAttribute = (key, value) => {
    return {
        type: 'mdxJsxAttribute',
        name: key,
        // we want to cast here to avoid the type checker failing on number/boolean values
        // which will get serialized properly, but just aren't added to the type
        value: value,
    };
};
export function getUnicodeId(title) {
    return encodeURIComponent(title.toLowerCase().trim().replace(/\s+/g, '-'));
}
const EMPTY_CHILDREN = [];
export const getTableOfContentsTitle = (node, index = 1, children = EMPTY_CHILDREN) => {
    var _a;
    if ('name' in node && node.name === 'Update') {
        const labelAttr = node.attributes.find((attr) => 'name' in attr && attr.name === 'label' && typeof attr.value === 'string');
        return (_a = labelAttr === null || labelAttr === void 0 ? void 0 : labelAttr.value) !== null && _a !== void 0 ? _a : '';
    }
    const lastChild = children[index - 1];
    const isText = node.type === 'text';
    const isInlineCode = node.type === 'inlineCode';
    const isLastChildMdx = (lastChild === null || lastChild === void 0 ? void 0 : lastChild.type) !== 'mdxJsxFlowElement';
    const isLastChildSmall = lastChild &&
        'value' in lastChild &&
        typeof lastChild.value === 'string' &&
        !lastChild.value.startsWith('<small');
    if (isInlineCode || (isText && (isLastChildMdx || isLastChildSmall))) {
        return node.value;
    }
    if ('children' in node && Array.isArray(node.children)) {
        let title = '';
        node.children.forEach((subNode, index, children) => {
            title += getTableOfContentsTitle(subNode, index, children);
        });
        return title;
    }
    return '';
};
