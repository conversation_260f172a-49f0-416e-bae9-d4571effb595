import { MintConfig } from '@mintlify/models';
import { DataSchema, DocsConfig, HttpMethod, SecurityOption, Server } from '@mintlify/validation';
import type { MdxJsxFlowElementHast } from 'mdast-util-mdx-jsx';
import type { FieldLocation } from '../../../lib/mdx-utils.js';
export declare const parseApiString: (apiString: string, config?: MintConfig | DocsConfig) => {
    servers?: Server[];
    path: string;
    method: HttpMethod;
};
export declare const parseAuthMethod: (authMethodString: string | undefined, config?: MintConfig | DocsConfig) => SecurityOption[];
export type LocationAttr = {
    location: FieldLocation;
    name: string;
} | {
    location: 'body';
    name: null;
};
type ParseFieldType = {
    locationAttr: LocationAttr;
    schema: DataSchema;
    deepestSchema: DataSchema;
};
export declare const parseField: (node: MdxJsxFlowElementHast) => ParseFieldType | undefined;
export declare const parseDescription: (node: MdxJsxFlowElementHast) => string | undefined;
type ParseTypeStringType = {
    schema: DataSchema;
    deepestSchema: DataSchema;
};
export declare const parseTypeString: (typeString: string) => ParseTypeStringType;
export {};
