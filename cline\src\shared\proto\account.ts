// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface AccountServiceDefinition {
  // Account service methods
}

export interface UserInfo {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  plan?: string;
  credits?: number;
}

export interface UserOrganization {
  id: string;
  name: string;
  role: string;
  plan?: string;
}

export interface UserOrganizationUpdateRequest {
  organizationId: string;
  updates: Partial<UserOrganization>;
}

export interface AccountInfo {
  user: UserInfo;
  organizations: UserOrganization[];
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  user?: UserInfo;
  error?: string;
}
