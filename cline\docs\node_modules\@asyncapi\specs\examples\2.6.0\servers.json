[{"development": {"url": "development.gigantic-server.com", "description": "Development server", "protocol": "amqp", "protocolVersion": "0.9.1", "tags": [{"name": "env:development", "description": "This environment is meant for developers to run their own tests"}]}, "staging": {"url": "staging.gigantic-server.com", "description": "Staging server", "protocol": "amqp", "protocolVersion": "0.9.1", "tags": [{"name": "env:staging", "description": "This environment is a replica of the production environment"}]}, "production": {"url": "api.gigantic-server.com", "description": "Production server", "protocol": "amqp", "protocolVersion": "0.9.1", "tags": [{"name": "env:production", "description": "This environment is the live environment available for final users"}]}}]