import { redirectsSchema } from '../../../v1/basics.js';
import { $schemaSchema } from '../../properties/$schema.js';
import { apiSchema } from '../../properties/api.js';
import { appearanceSchema } from '../../properties/appearance.js';
import { backgroundSchema } from '../../properties/background.js';
import { bannerSchema } from '../../properties/banner.js';
import { colorsSchema } from '../../properties/colors.js';
import { contextualSchema } from '../../properties/contextual.js';
import { descriptionSchema } from '../../properties/description.js';
import { errorsSchema } from '../../properties/errors.js';
import { faviconSchema } from '../../properties/favicon.js';
import { fontsSchema } from '../../properties/font.js';
import { footerSchema } from '../../properties/footer.js';
import { iconsSchema } from '../../properties/icons.js';
import { integrationsSchema } from '../../properties/integrations.js';
import { logoSchema } from '../../properties/logo.js';
import { nameSchema } from '../../properties/name.js';
import { navbarSchema } from '../../properties/navbar.js';
import { navigationSchema } from '../../properties/navigation/index.js';
import { searchSchema } from '../../properties/search.js';
import { seoSchema } from '../../properties/seo.js';
import { stylingSchema } from '../../properties/styling.js';
import { thumbnailsSchema } from '../../properties/thumbnails.js';
export const standardConfigSchema = {
    $schema: $schemaSchema,
    name: nameSchema,
    description: descriptionSchema.optional(),
    colors: colorsSchema,
    logo: logoSchema.optional(),
    favicon: faviconSchema.optional(),
    api: apiSchema.optional(),
    appearance: appearanceSchema.optional(),
    background: backgroundSchema.optional(),
    navbar: navbarSchema.optional(),
    navigation: navigationSchema,
    footer: footerSchema.optional(),
    search: searchSchema.optional(),
    seo: seoSchema.optional(),
    fonts: fontsSchema.optional(),
    icons: iconsSchema.optional(),
    styling: stylingSchema.optional(),
    redirects: redirectsSchema.optional(),
    integrations: integrationsSchema.optional(),
    banner: bannerSchema.optional(),
    errors: errorsSchema.optional(),
    contextual: contextualSchema.optional(),
    thumbnails: thumbnailsSchema.optional(),
};
