{"version": 3, "file": "function.js", "sourceRoot": "", "sources": ["../../src/ruleset/function.ts"], "names": [], "mappings": ";;;;AAAA,2DAAuC;AACvC,2EAAqC;AACrC,yEAAmC;AAMnC,kEAAgF;AAEhF,8CAA4D;AAE5D,mCAAkC;AAClC,qDAAsD;AAEtD,MAAM,GAAG,GAAG,IAAI,aAAG,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;AACxG,IAAA,oBAAS,EAAC,GAAG,CAAC,CAAC;AACf,IAAA,qBAAU,EAAC,GAAG,CAAC,CAAC;AAEhB,MAAa,8BAA+B,SAAQ,8BAAsB;IACxE,YAAY,EAAU,EAAE,KAAkB;QACxC,KAAK,CACH,0BAA0B,EAC1B,8BAA8B,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,CAAC,EACtD,8BAA8B,CAAC,OAAO,CAAC,KAAK,CAAC,CAC9C,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,OAAO,CAAC,KAAkB;QACvC,MAAM,IAAI,GAAa;YACrB,iBAAiB;YACjB,GAAG,CAAC,KAAK,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC7E,CAAC;QAEF,QAAQ,KAAK,CAAC,OAAO,EAAE;YACrB,KAAK,sBAAsB,CAAC,CAAC;gBAC3B,MAAM,kBAAkB,GAAI,KAAmC,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAC1F,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC9B,MAAM;aACP;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,EAAU,EAAE,KAAkB;;QACxD,QAAQ,KAAK,CAAC,OAAO,EAAE;YACrB,KAAK,MAAM,CAAC,CAAC;gBACX,MAAM,IAAI,GAAG,IAAA,4BAAS,EAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,6BAAU,CAAC,GAAG,CAAC,CAAC;gBAC/E,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAE3G,OAAO,IAAI,EAAE,uBAAuB,IAAI,8CAA8C,MAAM,EAAE,CAAC;aAChG;YAED,KAAK,UAAU,CAAC,CAAC;gBACf,MAAM,eAAe,GAAI,KAAuB,CAAC,MAAM,CAAC,eAAe,CAAC;gBACxE,MAAM,mBAAmB,GACvB,KAAK,CAAC,YAAY,KAAK,EAAE;oBACvB,CAAC,CAAC,eAAe;oBACjB,CAAC,CAAC,IAAA,4BAAS,EAAC,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,eAAe,CAAC,EAAE,6BAAU,CAAC,GAAG,CAAC,CAAC;gBAE9F,OAAO,IAAI,EAAE,0BAA0B,mBAAmB,UAAU,CAAC;aACtE;YAED,KAAK,sBAAsB,CAAC,CAAC;gBAC3B,MAAM,kBAAkB,GAAI,KAAmC,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAC1F,MAAM,sBAAsB,GAC1B,KAAK,CAAC,YAAY,KAAK,EAAE;oBACvB,CAAC,CAAC,kBAAkB;oBACpB,CAAC,CAAC,IAAA,4BAAS,EAAC,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,EAAE,6BAAU,CAAC,GAAG,CAAC,CAAC;gBAEjG,OAAO,IAAI,EAAE,gCAAgC,sBAAsB,UAAU,CAAC;aAC/E;YAED,KAAK,MAAM,CAAC,CAAC;gBACX,MAAM,IAAI,GAAG,IAAA,4BAAS,EAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,6BAAU,CAAC,GAAG,CAAC,CAAC;gBAC/E,MAAM,MAAM,GAAI,KAAmB,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,6BAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEpF,OAAO,IAAI,EAAE,uBAAuB,IAAI,+CAA+C,MAAM,EAAE,CAAC;aACjG;YACD;gBACE,OAAO,MAAA,KAAK,CAAC,OAAO,mCAAI,eAAe,CAAC;SAC3C;IACH,CAAC;CACF;AAjED,wEAiEC;AA8BD,MAAM,yBAAyB,GAAG,CAAC,CAAU,EAAW,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC;AAEtE,SAAgB,qBAAqB,CACnC,EACE,KAAK,EACL,mBAAmB,GAAG,KAAK,EAC3B,OAAO,GAKR,EACD,EAAyB;IAEzB,MAAM,eAAe,GAAG,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC5F,MAAM,aAAa,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAUlE,MAAM,SAAS,GAA2B,UACxC,KAAK,EACL,OAAO,EACP,GAAG,IAAI;;QAEP,IAAI,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAG,KAAK,CAAC,MAAK,KAAK,EAAE;YACpC,IAAI,mBAAmB,EAAE;gBACvB,OAAO;oBACL;wBACE,OAAO,EAAE,MAAA,MAAA,MAAA,aAAa,CAAC,MAAM,0CAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,KAAK,cAAc,CAAC,0CAAE,OAAO,mCAAI,eAAe;qBAC3G;iBACF,CAAC;aACH;YAED,OAAO;SACR;QAED,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAE7B,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAE9D,MAAM,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;IAChC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAU;QACxC,IAAI,IAAA,iBAAQ,EAAC,CAAC,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAAE,OAAO;QAE5C,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE;YACtB,IAAI,IAAA,iBAAQ,EAAC,CAAC,CAAC;gBAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClC,OAAO;SACR;QAED,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,MAAM,IAAI,8BAAsB,CAC9B,0BAA0B,EAC1B,IAAI,EAAE,CAAC,IAAI,IAAI,WAAW,wCAAwC,EAClE,CAAC,iBAAiB,CAAC,CACpB,CAAC;SACH;aAAM,IACL,QAAQ,IAAI,eAAe;YAC3B,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC;YACrC,eAAe,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EACjC;YACA,MAAM,IAAI,cAAc,CACtB,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,8BAA8B,CAAC,EAAE,CAAC,IAAI,IAAI,WAAW,EAAE,KAAK,CAAC,CAAC,CACvG,CAAC;SACH;aAAM;YACL,MAAM,IAAI,8BAAsB,CAC9B,0BAA0B,EAC1B,yBAAyB,EAAE,CAAC,IAAI,IAAI,WAAW,0BAA0B,EACzE,CAAC,iBAAiB,CAAC,CACpB,CAAC;SACH;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,EAAE;QAC3C,UAAU,EAAE,KAAK;QACjB,KAAK,EAAE;YACL,KAAK;YACL,OAAO;SACR;KACF,CAAC,CAAC;IAEH,OAAO,SAA+C,CAAC;AACzD,CAAC;AAxFD,sDAwFC"}