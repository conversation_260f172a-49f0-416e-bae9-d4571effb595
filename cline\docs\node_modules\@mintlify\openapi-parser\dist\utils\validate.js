import { makeFilesystem } from './makeFilesystem.js';
import { Validator } from '../lib/Validator/Validator.js';

/**
 * Validates an OpenAPI schema.
 */
async function validate(value, options) {
    const filesystem = makeFilesystem(value);
    const validator = new Validator();
    const result = await validator.validate(filesystem, options);
    return {
        ...result,
        specification: validator.specification,
        version: validator.version,
    };
}

export { validate };
