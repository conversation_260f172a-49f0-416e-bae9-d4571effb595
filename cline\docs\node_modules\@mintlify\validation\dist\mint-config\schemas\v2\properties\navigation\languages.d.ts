import { z } from 'zod';
import { LanguageNavigation } from './divisionNav.js';
export declare const baseLanguageSchema: z.ZodObject<{
    language: z.<PERSON>od<PERSON><["en", "cn", "zh", "zh-Hans", "zh-Hant", "es", "fr", "fr-CA", "ja", "jp", "pt", "pt-BR", "de", "ko", "it", "ru", "id", "ar", "tr", "hi"]>;
    default: z.Zod<PERSON>ptional<z.ZodBoolean>;
    hidden: z.<PERSON>od<PERSON>ptional<z.ZodBoolean>;
}, "strip", z.<PERSON>ny, {
    language: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi";
    default?: boolean | undefined;
    hidden?: boolean | undefined;
}, {
    language: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi";
    default?: boolean | undefined;
    hidden?: boolean | undefined;
}>;
export type BaseLanguageSchema = z.infer<typeof baseLanguageSchema>;
export declare const nonRecursiveLanguageSchema: z.ZodObject<{
    default: z.ZodOptional<z.ZodBoolean>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    language: z.ZodEnum<["en", "cn", "zh", "zh-Hans", "zh-Hant", "es", "fr", "fr-CA", "ja", "jp", "pt", "pt-BR", "de", "ko", "it", "ru", "id", "ar", "tr", "hi"]>;
    href: z.ZodString;
}, "strip", z.ZodTypeAny, {
    href: string;
    language: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi";
    default?: boolean | undefined;
    hidden?: boolean | undefined;
}, {
    href: string;
    language: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi";
    default?: boolean | undefined;
    hidden?: boolean | undefined;
}>;
export declare const languageSchema: z.ZodType<LanguageNavigation<'default'>>;
export declare const decoratedLanguageSchema: z.ZodType<LanguageNavigation<'decorated'>>;
export declare const languagesSchema: z.ZodArray<z.ZodType<LanguageNavigation<"default">, z.ZodTypeDef, LanguageNavigation<"default">>, "many">;
export declare const decoratedLanguagesSchema: z.ZodArray<z.ZodType<LanguageNavigation<"decorated">, z.ZodTypeDef, LanguageNavigation<"decorated">>, "many">;
export type LanguagesConfig = z.infer<typeof languagesSchema>;
export type LanguageConfig = z.infer<typeof languageSchema>;
export type DecoratedLanguagesConfig = z.infer<typeof decoratedLanguagesSchema>;
export type DecoratedLanguageConfig = z.infer<typeof decoratedLanguageSchema>;
