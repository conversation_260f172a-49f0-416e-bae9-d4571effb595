import { z } from 'zod';
export declare const metatagsSchema: z.<PERSON><z.ZodR<PERSON>ord<z.ZodString, z.ZodString>>;
export declare const seoSchema: z.ZodObject<{
    metatags: z.ZodOptional<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>>;
    indexing: z.Zod<PERSON>ptional<z.ZodEnum<["navigable", "all"]>>;
}, "strip", z.Zod<PERSON>ny, {
    metatags?: Record<string, string> | undefined;
    indexing?: "all" | "navigable" | undefined;
}, {
    metatags?: Record<string, string> | undefined;
    indexing?: "all" | "navigable" | undefined;
}>;
