import type { LoadPlugin } from '../../utils/load/index.js';
export declare const fetchUrlsDefaultConfiguration: {
    limit: number;
};
export declare const fetchUrls: (customConfiguration?: {
    /**
     * Limit the number of requests. Set to `false` to disable the limit.
     */
    limit?: number | false;
    /**
     * Fetch function to use instead of the global fetch. Use this to intercept requests.
     */
    fetch?: (url: string) => Promise<Response>;
}) => LoadPlugin;
//# sourceMappingURL=fetchUrls.d.ts.map