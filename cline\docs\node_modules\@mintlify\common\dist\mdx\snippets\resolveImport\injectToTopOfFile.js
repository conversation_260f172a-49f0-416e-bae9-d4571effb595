import matter from 'gray-matter';
const { test: hasFrontmatter } = matter;
/**
 * Injects content to the top of a file, but below the frontmatter
 */
export const injectToTopOfFile = (content, contentToInject) => {
    if (!hasFrontmatter(content))
        return contentToInject + `\n` + content;
    const { data: frontmatterData, content: contentWithoutFrontmatter } = matter(content);
    return matter.stringify(`\n` + contentToInject + `\n` + contentWithoutFrontmatter, frontmatterData);
};
/**
 * Injects nodes to the top of a MDX/Markdown AST (Root), but below the frontmatter (YAML node).
 * Modifies the tree in place.
 * @param tree The Root node of the document.
 * @param nodesToInject An array of RootContent nodes to inject.
 */
export const injectToTopOfFileOfTree = (tree, nodesToInject) => {
    const firstChild = tree.children[0];
    if ((firstChild === null || firstChild === void 0 ? void 0 : firstChild.type) === 'yaml') {
        // Insert after the frontmatter (yaml node)
        tree.children.splice(1, 0, ...nodesToInject);
    }
    else {
        // Insert at the very beginning
        tree.children.unshift(...nodesToInject);
    }
};
