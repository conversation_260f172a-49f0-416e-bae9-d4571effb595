"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagsV2 = exports.TagV2 = exports.ServersV2 = exports.ServerV2 = exports.ServerVariablesV2 = exports.ServerVariableV2 = exports.SecuritySchemesV2 = exports.SecuritySchemeV2 = exports.SchemasV2 = exports.SchemaV2 = exports.OperationsV2 = exports.OperationV2 = exports.OperationTraitsV2 = exports.OperationTraitV2 = exports.OAuthFlowsV2 = exports.OAuthFlowV2 = exports.MessagesV2 = exports.MessageV2 = exports.MessageTraitsV2 = exports.MessageTraitV2 = exports.MessageExamplesV2 = exports.MessageExampleV2 = exports.LicenseV2 = exports.InfoV2 = exports.ExternalDocumentationV2 = exports.ExtensionsV2 = exports.ExtensionV2 = exports.CorrelationIdV2 = exports.ContactV2 = exports.ComponentsV2 = exports.ChannelsV2 = exports.ChannelV2 = exports.ChannelParametersV2 = exports.ChannelParameterV2 = exports.BindingsV2 = exports.BindingV2 = exports.AsyncAPIDocumentV2 = void 0;
var asyncapi_1 = require("./asyncapi");
Object.defineProperty(exports, "AsyncAPIDocumentV2", { enumerable: true, get: function () { return asyncapi_1.AsyncAPIDocument; } });
var binding_1 = require("./binding");
Object.defineProperty(exports, "BindingV2", { enumerable: true, get: function () { return binding_1.Binding; } });
var bindings_1 = require("./bindings");
Object.defineProperty(exports, "BindingsV2", { enumerable: true, get: function () { return bindings_1.Bindings; } });
var channel_parameter_1 = require("./channel-parameter");
Object.defineProperty(exports, "ChannelParameterV2", { enumerable: true, get: function () { return channel_parameter_1.ChannelParameter; } });
var channel_parameters_1 = require("./channel-parameters");
Object.defineProperty(exports, "ChannelParametersV2", { enumerable: true, get: function () { return channel_parameters_1.ChannelParameters; } });
var channel_1 = require("./channel");
Object.defineProperty(exports, "ChannelV2", { enumerable: true, get: function () { return channel_1.Channel; } });
var channels_1 = require("./channels");
Object.defineProperty(exports, "ChannelsV2", { enumerable: true, get: function () { return channels_1.Channels; } });
var components_1 = require("./components");
Object.defineProperty(exports, "ComponentsV2", { enumerable: true, get: function () { return components_1.Components; } });
var contact_1 = require("./contact");
Object.defineProperty(exports, "ContactV2", { enumerable: true, get: function () { return contact_1.Contact; } });
var correlation_id_1 = require("./correlation-id");
Object.defineProperty(exports, "CorrelationIdV2", { enumerable: true, get: function () { return correlation_id_1.CorrelationId; } });
var extension_1 = require("./extension");
Object.defineProperty(exports, "ExtensionV2", { enumerable: true, get: function () { return extension_1.Extension; } });
var extensions_1 = require("./extensions");
Object.defineProperty(exports, "ExtensionsV2", { enumerable: true, get: function () { return extensions_1.Extensions; } });
var external_docs_1 = require("./external-docs");
Object.defineProperty(exports, "ExternalDocumentationV2", { enumerable: true, get: function () { return external_docs_1.ExternalDocumentation; } });
var info_1 = require("./info");
Object.defineProperty(exports, "InfoV2", { enumerable: true, get: function () { return info_1.Info; } });
var license_1 = require("./license");
Object.defineProperty(exports, "LicenseV2", { enumerable: true, get: function () { return license_1.License; } });
var message_example_1 = require("./message-example");
Object.defineProperty(exports, "MessageExampleV2", { enumerable: true, get: function () { return message_example_1.MessageExample; } });
var message_examples_1 = require("./message-examples");
Object.defineProperty(exports, "MessageExamplesV2", { enumerable: true, get: function () { return message_examples_1.MessageExamples; } });
var message_trait_1 = require("./message-trait");
Object.defineProperty(exports, "MessageTraitV2", { enumerable: true, get: function () { return message_trait_1.MessageTrait; } });
var message_traits_1 = require("./message-traits");
Object.defineProperty(exports, "MessageTraitsV2", { enumerable: true, get: function () { return message_traits_1.MessageTraits; } });
var message_1 = require("./message");
Object.defineProperty(exports, "MessageV2", { enumerable: true, get: function () { return message_1.Message; } });
var messages_1 = require("./messages");
Object.defineProperty(exports, "MessagesV2", { enumerable: true, get: function () { return messages_1.Messages; } });
var oauth_flow_1 = require("./oauth-flow");
Object.defineProperty(exports, "OAuthFlowV2", { enumerable: true, get: function () { return oauth_flow_1.OAuthFlow; } });
var oauth_flows_1 = require("./oauth-flows");
Object.defineProperty(exports, "OAuthFlowsV2", { enumerable: true, get: function () { return oauth_flows_1.OAuthFlows; } });
var operation_trait_1 = require("./operation-trait");
Object.defineProperty(exports, "OperationTraitV2", { enumerable: true, get: function () { return operation_trait_1.OperationTrait; } });
var operation_traits_1 = require("./operation-traits");
Object.defineProperty(exports, "OperationTraitsV2", { enumerable: true, get: function () { return operation_traits_1.OperationTraits; } });
var operation_1 = require("./operation");
Object.defineProperty(exports, "OperationV2", { enumerable: true, get: function () { return operation_1.Operation; } });
var operations_1 = require("./operations");
Object.defineProperty(exports, "OperationsV2", { enumerable: true, get: function () { return operations_1.Operations; } });
var schema_1 = require("./schema");
Object.defineProperty(exports, "SchemaV2", { enumerable: true, get: function () { return schema_1.Schema; } });
var schemas_1 = require("./schemas");
Object.defineProperty(exports, "SchemasV2", { enumerable: true, get: function () { return schemas_1.Schemas; } });
var security_scheme_1 = require("./security-scheme");
Object.defineProperty(exports, "SecuritySchemeV2", { enumerable: true, get: function () { return security_scheme_1.SecurityScheme; } });
var security_schemes_1 = require("./security-schemes");
Object.defineProperty(exports, "SecuritySchemesV2", { enumerable: true, get: function () { return security_schemes_1.SecuritySchemes; } });
var server_variable_1 = require("./server-variable");
Object.defineProperty(exports, "ServerVariableV2", { enumerable: true, get: function () { return server_variable_1.ServerVariable; } });
var server_variables_1 = require("./server-variables");
Object.defineProperty(exports, "ServerVariablesV2", { enumerable: true, get: function () { return server_variables_1.ServerVariables; } });
var server_1 = require("./server");
Object.defineProperty(exports, "ServerV2", { enumerable: true, get: function () { return server_1.Server; } });
var servers_1 = require("./servers");
Object.defineProperty(exports, "ServersV2", { enumerable: true, get: function () { return servers_1.Servers; } });
var tag_1 = require("./tag");
Object.defineProperty(exports, "TagV2", { enumerable: true, get: function () { return tag_1.Tag; } });
var tags_1 = require("./tags");
Object.defineProperty(exports, "TagsV2", { enumerable: true, get: function () { return tags_1.Tags; } });
