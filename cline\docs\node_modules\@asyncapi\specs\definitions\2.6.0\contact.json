{"type": "object", "description": "Contact information for the exposed API.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The identifying name of the contact person/organization."}, "url": {"type": "string", "description": "The URL pointing to the contact information.", "format": "uri"}, "email": {"type": "string", "description": "The email address of the contact person/organization.", "format": "email"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/contact.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/contact.json"}