import type { MintConfigType, DecoratedNavigation } from '@mintlify/models';
import { DocsConfig } from '../schemas/v2/index.js';
import { DecoratedNavigationConfig } from '../schemas/v2/properties/navigation/index.js';
type Input = ({
    type: 'mint';
    mintConfig: MintConfigType;
} | {
    type: 'docs';
    docsConfig: DocsConfig;
}) & {
    decoratedNavigation: DecoratedNavigation;
    ignoreUnknownPages?: boolean;
};
export declare const convertMintDecoratedNavToDocsDecoratedNav: (param: Input) => DecoratedNavigationConfig;
export {};
