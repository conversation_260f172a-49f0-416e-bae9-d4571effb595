{"type": "object", "required": ["type", "name", "in"], "properties": {"type": {"type": "string", "enum": ["httpApiKey"]}, "name": {"type": "string"}, "in": {"type": "string", "enum": ["header", "query", "cookie"]}, "description": {"type": "string"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.5.0/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.5.0/APIKeyHTTPSecurityScheme.json"}