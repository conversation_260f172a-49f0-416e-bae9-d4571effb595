{"type": "object", "required": ["type", "in"], "properties": {"type": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "in": {"type": "string", "enum": ["user", "password"]}, "description": {"type": "string"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.1.0/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.1.0/apiKey.json"}