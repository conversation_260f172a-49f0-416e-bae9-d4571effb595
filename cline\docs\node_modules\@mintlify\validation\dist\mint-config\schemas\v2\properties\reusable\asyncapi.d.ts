import { z } from 'zod';
export declare const asyncapiStringSchema: z.ZodEffects<z.ZodString, string, string>;
export declare const asyncapiObjSchema: z.ZodObject<{
    source: z.ZodEffects<z.ZodString, string, string>;
    directory: z.ZodOptional<z.ZodString>;
}, "strict", z.ZodTypeAny, {
    source: string;
    directory?: string | undefined;
}, {
    source: string;
    directory?: string | undefined;
}>;
export declare const asyncApiSchema: z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodArray<z.ZodEffects<z.ZodString, string, string>, "many">, z.ZodObject<{
    source: z.ZodEffects<z.ZodString, string, string>;
    directory: z.ZodOptional<z.ZodString>;
}, "strict", z.ZodTypeAny, {
    source: string;
    directory?: string | undefined;
}, {
    source: string;
    directory?: string | undefined;
}>]>;
export type AsyncApiObjConfig = z.infer<typeof asyncapiObjSchema>;
export type AsyncApiConfig = z.infer<typeof asyncApiSchema>;
