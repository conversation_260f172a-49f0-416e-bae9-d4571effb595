// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface FileServiceDefinition {
  // File service methods
}

export interface FileSearchRequest {
  query: string;
  path?: string;
  includeHidden?: boolean;
}

export interface RelativePathsRequest {
  paths: string[];
}

export interface FileInfo {
  path: string;
  name: string;
  isDirectory: boolean;
  size?: number;
  lastModified?: string;
}

export interface FileSearchResponse {
  files: FileInfo[];
  hasMore: boolean;
}

export interface ReadFileRequest {
  path: string;
}

export interface WriteFileRequest {
  path: string;
  content: string;
}

export interface DeleteFileRequest {
  path: string;
}

export interface CreateDirectoryRequest {
  path: string;
}
