import { z } from 'zod';
export declare const contextualOptions: readonly ["copy", "view", "chatgpt", "claude", "perplexity"];
export type ContextualOption = (typeof contextualOptions)[number];
export declare const contextualHrefPresetValues: readonly ["$page", "$path"];
export type ContextualHrefPresetValue = (typeof contextualHrefPresetValues)[number];
export declare const DetailedContextualHrefSchema: z.ZodObject<{
    base: z.ZodString;
    query: z.ZodOptional<z.ZodArray<z.ZodObject<{
        key: z.ZodString;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        key: string;
    }, {
        value: string;
        key: string;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    base: string;
    query?: {
        value: string;
        key: string;
    }[] | undefined;
}, {
    base: string;
    query?: {
        value: string;
        key: string;
    }[] | undefined;
}>;
export declare const customContextualOption: z.ZodObject<{
    title: z.ZodString;
    description: z.ZodString;
    icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
        style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
        name: z.ZodEffects<z.ZodString, string, string>;
        library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }, {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    }>]>>;
    href: z.ZodUnion<[z.ZodString, z.ZodObject<{
        base: z.ZodString;
        query: z.ZodOptional<z.ZodArray<z.ZodObject<{
            key: z.ZodString;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            key: string;
        }, {
            value: string;
            key: string;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        base: string;
        query?: {
            value: string;
            key: string;
        }[] | undefined;
    }, {
        base: string;
        query?: {
            value: string;
            key: string;
        }[] | undefined;
    }>]>;
}, "strip", z.ZodTypeAny, {
    href: (string | {
        base: string;
        query?: {
            value: string;
            key: string;
        }[] | undefined;
    }) & (string | {
        base: string;
        query?: {
            value: string;
            key: string;
        }[] | undefined;
    } | undefined);
    title: string;
    description: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
}, {
    href: (string | {
        base: string;
        query?: {
            value: string;
            key: string;
        }[] | undefined;
    }) & (string | {
        base: string;
        query?: {
            value: string;
            key: string;
        }[] | undefined;
    } | undefined);
    title: string;
    description: string;
    icon?: string | {
        name: string;
        style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
        library?: "fontawesome" | "lucide" | undefined;
    } | undefined;
}>;
export declare const contextualSchema: z.ZodObject<{
    options: z.ZodEffects<z.ZodArray<z.ZodUnion<[z.ZodEnum<["copy", "view", "chatgpt", "claude", "perplexity"]>, z.ZodObject<{
        title: z.ZodString;
        description: z.ZodString;
        icon: z.ZodOptional<z.ZodUnion<[z.ZodEffects<z.ZodString, string, string>, z.ZodObject<{
            style: z.ZodOptional<z.ZodEnum<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
            name: z.ZodEffects<z.ZodString, string, string>;
            library: z.ZodOptional<z.ZodEnum<["fontawesome", "lucide"]>>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }, {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        }>]>>;
        href: z.ZodUnion<[z.ZodString, z.ZodObject<{
            base: z.ZodString;
            query: z.ZodOptional<z.ZodArray<z.ZodObject<{
                key: z.ZodString;
                value: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                value: string;
                key: string;
            }, {
                value: string;
                key: string;
            }>, "many">>;
        }, "strip", z.ZodTypeAny, {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        }, {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        }>]>;
    }, "strip", z.ZodTypeAny, {
        href: (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        }) & (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        } | undefined);
        title: string;
        description: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    }, {
        href: (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        }) & (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        } | undefined);
        title: string;
        description: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    }>]>, "many">, ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
        href: (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        }) & (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        } | undefined);
        title: string;
        description: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    })[], ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
        href: (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        }) & (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        } | undefined);
        title: string;
        description: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    })[]>;
}, "strip", z.ZodTypeAny, {
    options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
        href: (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        }) & (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        } | undefined);
        title: string;
        description: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    })[];
}, {
    options: ("copy" | "view" | "chatgpt" | "claude" | "perplexity" | {
        href: (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        }) & (string | {
            base: string;
            query?: {
                value: string;
                key: string;
            }[] | undefined;
        } | undefined);
        title: string;
        description: string;
        icon?: string | {
            name: string;
            style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
            library?: "fontawesome" | "lucide" | undefined;
        } | undefined;
    })[];
}>;
export type CustomContextualOption = z.infer<typeof customContextualOption>;
export type ContextualConfig = z.infer<typeof contextualSchema>;
export type DetailedContextualHref = z.infer<typeof DetailedContextualHrefSchema>;
