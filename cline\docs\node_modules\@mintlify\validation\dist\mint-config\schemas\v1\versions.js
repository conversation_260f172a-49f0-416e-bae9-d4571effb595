import { locales } from '@mintlify/models';
import { z } from 'zod';
const versionSchema = z.union([
    z.string(),
    z
        .object({
        name: z.string().nonempty(),
        url: z.string().url().nonempty().optional(),
        default: z.literal(true).optional(),
        locale: z.enum(locales).optional(),
    })
        .strict(),
]);
export const versionsSchema = z
    .array(versionSchema)
    .min(1)
    .refine((versions) => versions.filter((v) => typeof v === 'object' && v.default).length <= 1, 'Only one version can be set as default');
