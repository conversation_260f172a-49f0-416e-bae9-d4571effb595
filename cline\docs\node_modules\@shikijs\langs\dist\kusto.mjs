const lang = Object.freeze(JSON.parse("{\"displayName\":\"Kusto\",\"fileTypes\":[\"csl\",\"kusto\",\"kql\"],\"name\":\"kusto\",\"patterns\":[{\"match\":\"\\\\b(by|from|of|to|step|with)\\\\b\",\"name\":\"keyword.other.operator.kusto\"},{\"match\":\"\\\\b(let|set|alias|declare|pattern|query_parameters|restrict|access|set)\\\\b\",\"name\":\"keyword.control.kusto\"},{\"match\":\"\\\\b(and|or|has_all|has_any|matches|regex)\\\\b\",\"name\":\"keyword.other.operator.kusto\"},{\"captures\":{\"1\":{\"name\":\"support.function.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#Strings\"}]}},\"match\":\"\\\\b(cluster|database)(?:\\\\s*\\\\(\\\\s*(.+?)\\\\s*\\\\))?(?!\\\\w)\",\"name\":\"meta.special.database.kusto\"},{\"match\":\"\\\\b(external_table|materialized_view|materialize|table|toscalar)\\\\b\",\"name\":\"support.function.kusto\"},{\"match\":\"(?<!\\\\w)(!?between)\\\\b\",\"name\":\"keyword.other.operator.kusto\"},{\"captures\":{\"1\":{\"name\":\"support.function.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#Numeric\"}]},\"3\":{\"patterns\":[{\"include\":\"#Numeric\"}]}},\"match\":\"\\\\b(binary_(?:and|or|shift_left|shift_right|xor))(?:\\\\s*\\\\(\\\\s*(\\\\w+)\\\\s*,\\\\s*(\\\\w+)\\\\s*\\\\))?(?!\\\\w)\",\"name\":\"meta.scalar.bitwise.kusto\"},{\"captures\":{\"1\":{\"name\":\"support.function.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#Numeric\"}]}},\"match\":\"\\\\b(bi(?:nary_not|tset_count_ones))(?:\\\\s*\\\\(\\\\s*(\\\\w+)\\\\s*\\\\))?(?!\\\\w)\",\"name\":\"meta.scalar.bitwise.kusto\"},{\"match\":\"(?<!\\\\w)(!?in~?)(?!\\\\w)\",\"name\":\"keyword.other.operator.kusto\"},{\"match\":\"(?<!\\\\w)(!?(?:contains|endswith|hasprefix|hassuffix|has|startswith)(?:_cs)?)(?!\\\\w)\",\"name\":\"keyword.other.operator.kusto\"},{\"captures\":{\"1\":{\"name\":\"support.function.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#DateTimeTimeSpanDataTypes\"},{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#DateTimeTimeSpanFunctions\"},{\"include\":\"#Numeric\"}]},\"3\":{\"patterns\":[{\"include\":\"#DateTimeTimeSpanDataTypes\"},{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#DateTimeTimeSpanFunctions\"},{\"include\":\"#Numeric\"}]},\"4\":{\"patterns\":[{\"include\":\"#DateTimeTimeSpanDataTypes\"},{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#DateTimeTimeSpanFunctions\"},{\"include\":\"#Numeric\"}]}},\"match\":\"\\\\b(range)\\\\s*\\\\((?:\\\\s*(\\\\w+(?:\\\\(.*?\\\\))?)\\\\s*,\\\\s*(\\\\w+(?:\\\\(.*?\\\\))?)\\\\s*,?\\\\s*{0,1}(\\\\w+(?:\\\\(.*?\\\\))?)?\\\\s*\\\\))?(?!\\\\w)\",\"name\":\"meta.scalar.function.range.kusto\"},{\"match\":\"\\\\b(abs|acos|around|array_concat|array_iff|array_index_of|array_length|array_reverse|array_rotate_left|array_rotate_right|array_shift_left|array_shift_right|array_slice|array_sort_asc|array_sort_desc|array_split|array_sum|asin|assert|atan2?|bag_has_key|bag_keys|bag_merge|bag_remove_keys|base64_decode_toarray|base64_decode_tostring|base64_decode_toguid|base64_encode_fromarray|base64_encode_tostring|base64_encode_fromguid|beta_cdf|beta_inv|beta_pdf|bin_at|bin_auto|case|ceiling|coalesce|column_ifexists|convert_angle|convert_energy|convert_force|convert_length|convert_mass|convert_speed|convert_temperature|convert_volume|cos|cot|countof|current_cluster_endpoint|current_database|current_principal_details|current_principal_is_member_of|current_principal|cursor_after|cursor_before_or_at|cursor_current|current_cursor|dcount_hll|degrees|dynamic_to_json|estimate_data_size|exp10|exp2?|extent_id|extent_tags|extract_all|extract_json|extractjson|extract|floor|format_bytes|format_ipv4_mask|format_ipv4|gamma|gettype|gzip_compress_to_base64_string|gzip_decompress_from_base64_string|has_any_index|has_any_ipv4_prefix|has_any_ipv4|has_ipv4_prefix|has_ipv4|hash_combine|hash_many|hash_md5|hash_sha1|hash_sha256|hash_xxhash64|hash|iff|iif|indexof_regex|indexof|ingestion_time|ipv4_compare|ipv4_is_in_range|ipv4_is_in_any_range|ipv4_is_match|ipv4_is_private|ipv4_netmask_suffix|ipv6_compare|ipv6_is_match|isascii|isempty|isfinite|isinf|isnan|isnotempty|notempty|isnotnull|notnull|isnull|isutf8|jaccard_index|log10|log2|loggamma|log|make_string|max_of|min_of|new_guid|not|bag_pack|pack_all|pack_array|pack_dictionary|pack|parse_command_line|parse_csv|parse_ipv4_mask|parse_ipv4|parse_ipv6_mask|parse_ipv6|parse_path|parse_urlquery|parse_url|parse_user_agent|parse_version|parse_xml|percentile_tdigest|percentile_array_tdigest|percentrank_tdigest|pi|pow|radians|rand|rank_tdigest|regex_quote|repeat|replace_regex|replace_string|reverse|round|set_difference|set_has_element|set_intersect|set_union|sign|sin|split|sqrt|strcat_array|strcat_delim|strcmp|strcat|string_size|strlen|strrep|substring|tan|to_utf8|tobool|todecimal|todouble|toreal|toguid|tohex|toint|tolong|tolower|tostring|toupper|translate|treepath|trim_end|trim_start|trim|unixtime_microseconds_todatetime|unixtime_milliseconds_todatetime|unixtime_nanoseconds_todatetime|unixtime_seconds_todatetime|url_decode|url_encode_component|url_encode|welch_test|zip|zlib_compress_to_base64_string|zlib_decompress_from_base64_string)\\\\b\",\"name\":\"support.function.kusto\"},{\"captures\":{\"1\":{\"name\":\"support.function.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#DateTimeTimeSpanDataTypes\"},{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#DateTimeTimeSpanFunctions\"},{\"include\":\"#Numeric\"}]},\"3\":{\"patterns\":[{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#Numeric\"}]}},\"match\":\"\\\\b(bin)(?:\\\\s*\\\\(\\\\s*(.+?)\\\\s*,\\\\s*(.+?)\\\\s*\\\\))?(?!\\\\w)\",\"name\":\"meta.scalar.function.bin.kusto\"},{\"match\":\"\\\\b(count)\\\\s*\\\\(\\\\s*\\\\)(?!\\\\w)\",\"name\":\"support.function.kusto\"},{\"match\":\"\\\\b(arg_max|arg_min|avgif|avg|binary_all_and|binary_all_or|binary_all_xor|buildschema|countif|dcount|dcountif|hll|hll_merge|make_bag_if|make_bag|make_list_with_nulls|make_list_if|make_list|make_set_if|make_set|maxif|max|minif|min|percentilesw_array|percentiles_array|percentilesw|percentilew|percentiles?|stdevif|stdevp?|sumif|sum|take_anyif|take_any|tdigest_merge|merge_tdigest|tdigest|varianceif|variancep?)\\\\b\",\"name\":\"support.function.kusto\"},{\"match\":\"\\\\b(geo_(?:distance_2points|distance_point_to_line|distance_point_to_polygon|intersects_2lines|intersects_2polygons|intersects_line_with_polygon|intersection_2lines|intersection_2polygons|intersection_line_with_polygon|line_centroid|line_densify|line_length|line_simplify|polygon_area|polygon_centroid|polygon_densify|polygon_perimeter|polygon_simplify|polygon_to_s2cells|point_in_circle|point_in_polygon|point_to_geohash|point_to_h3cell|point_to_s2cell|geohash_to_central_point|geohash_neighbors|geohash_to_polygon|s2cell_to_central_point|s2cell_neighbors|s2cell_to_polygon|h3cell_to_central_point|h3cell_neighbors|h3cell_to_polygon|h3cell_parent|h3cell_children|h3cell_level|h3cell_rings|simplify_polygons_array|union_lines_array|union_polygons_array))\\\\b\",\"name\":\"support.function.kusto\"},{\"match\":\"\\\\b(next|prev|row_cumsum|row_number|row_rank|row_window_session)\\\\b\",\"name\":\"support.function.kusto\"},{\"match\":\"\\\\.(create-or-alter|replace)\",\"name\":\"keyword.control.kusto\"},{\"match\":\"(?<=let )[^\\\\n]+(?=\\\\W*=)\",\"name\":\"entity.function.name.lambda.kusto\"},{\"match\":\"\\\\b(folder|docstring|skipvalidation)\\\\b\",\"name\":\"keyword.other.operator.kusto\"},{\"match\":\"\\\\b(function)\\\\b\",\"name\":\"storage.type.kusto\"},{\"match\":\"\\\\b(bool|decimal|dynamic|guid|int|long|real|string)\\\\b\",\"name\":\"storage.type.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"name\":\"variable.other.kusto\"}},\"match\":\"\\\\b(as)\\\\s+(\\\\w+)\\\\b\",\"name\":\"meta.query.as.kusto\"},{\"match\":\"\\\\b(datatable)(?=\\\\W*\\\\()\",\"name\":\"keyword.other.query.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"name\":\"keyword.other.operator.kusto\"}},\"match\":\"\\\\b(facet)(?:\\\\s+(by))?\\\\b\",\"name\":\"meta.query.facet.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"name\":\"entity.name.function.kusto\"}},\"match\":\"\\\\b(invoke)(?:\\\\s+(\\\\w+))?\\\\b\",\"name\":\"meta.query.invoke.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"name\":\"keyword.other.operator.kusto\"},\"3\":{\"name\":\"variable.other.column.kusto\"}},\"match\":\"\\\\b(order)(?:\\\\s+(by)\\\\s+(\\\\w+))?\\\\b\",\"name\":\"meta.query.order.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"name\":\"variable.other.column.kusto\"},\"3\":{\"name\":\"keyword.other.operator.kusto\"},\"4\":{\"patterns\":[{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#DateTimeTimeSpanFunctions\"},{\"include\":\"#Numeric\"}]},\"5\":{\"name\":\"keyword.other.operator.kusto\"},\"6\":{\"patterns\":[{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#DateTimeTimeSpanFunctions\"},{\"include\":\"#Numeric\"}]},\"7\":{\"name\":\"keyword.other.operator.kusto\"},\"8\":{\"patterns\":[{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#DateTimeTimeSpanFunctions\"},{\"include\":\"#Numeric\"}]}},\"match\":\"\\\\b(range)\\\\s+(\\\\w+)\\\\s+(from)\\\\s+(\\\\w+(?:\\\\(\\\\w*\\\\))?)\\\\s+(to)\\\\s+(\\\\w+(?:\\\\(\\\\w*\\\\))?)\\\\s+(step)\\\\s+(\\\\w+(?:\\\\(\\\\w*\\\\))?)\\\\b\",\"name\":\"meta.query.range.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#Numeric\"}]}},\"match\":\"\\\\b(sample)(?:\\\\s+(\\\\d+))?(?![-\\\\w])\",\"name\":\"meta.query.sample.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#Numeric\"}]},\"3\":{\"name\":\"keyword.other.operator.kusto\"},\"4\":{\"name\":\"variable.other.column.kusto\"}},\"match\":\"\\\\b(sample-distinct)(?:\\\\s+(\\\\d+)\\\\s+(of)\\\\s+(\\\\w+))?\\\\b\",\"name\":\"meta.query.sample-distinct.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"name\":\"keyword.other.operator.kusto\"}},\"match\":\"\\\\b(sort)(?:\\\\s+(by))?\\\\b\",\"name\":\"meta.query.sort.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#Numeric\"}]}},\"match\":\"\\\\b(take|limit)\\\\s+(\\\\d+)\\\\b\",\"name\":\"meta.query.take.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#Numeric\"}]},\"3\":{\"name\":\"keyword.other.operator.kusto\"},\"4\":{\"name\":\"variable.other.column.kusto\"}},\"match\":\"\\\\b(top)(?:\\\\s+(\\\\d+)\\\\s+(by)\\\\s+(\\\\w+))?(?![-\\\\w])\\\\b\",\"name\":\"meta.query.top.kusto\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.query.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#Numeric\"}]},\"3\":{\"name\":\"keyword.other.operator.kusto\"},\"4\":{\"name\":\"variable.other.column.kusto\"},\"5\":{\"name\":\"keyword.other.operator.kusto\"},\"6\":{\"name\":\"variable.other.column.kusto\"}},\"match\":\"\\\\b(top-hitters)(?:\\\\s+(\\\\d+)\\\\s+(of)\\\\s+(\\\\w+)(?:\\\\s+(by)\\\\s+(\\\\w+))?)?\\\\b\",\"name\":\"meta.query.top-hitters.kusto\"},{\"match\":\"\\\\b(consume|count|distinct|evaluate|extend|externaldata|find|fork|getschema|join|lookup|make-series|mv-apply|mv-expand|project-away|project-keep|project-rename|project-reorder|project|parse|parse-where|parse-kv|partition|print|reduce|render|scan|search|serialize|shuffle|summarize|top-nested|union|where)\\\\b\",\"name\":\"keyword.other.query.kusto\"},{\"match\":\"\\\\b(active_users_count|activity_counts_metrics|activity_engagement|new_activity_metrics|activity_metrics|autocluster|azure_digital_twins_query_request|bag_unpack|basket|cosmosdb_sql_request|dcount_intersect|diffpatterns|funnel_sequence_completion|funnel_sequence|http_request_post|http_request|infer_storage_schema|ipv4_lookup|mysql_request|narrow|pivot|preview|rolling_percentile|rows_near|schema_merge|session_count|sequence_detect|sliding_window_counts|sql_request)\\\\b\",\"name\":\"support.function.kusto\"},{\"match\":\"\\\\b(on|kind|hint\\\\.remote|hint\\\\.strategy)\\\\b\",\"name\":\"keyword.other.operator.kusto\"},{\"match\":\"(\\\\$(?:left|right))\\\\b\",\"name\":\"keyword.other.kusto\"},{\"match\":\"\\\\b(innerunique|inner|leftouter|rightouter|fullouter|leftanti|anti|leftantisemi|rightanti|rightantisemi|leftsemi|rightsemi|broadcast)\\\\b\",\"name\":\"keyword.other.kusto\"},{\"match\":\"\\\\b(series_(?:abs|acos|add|asin|atan|cos|decompose|decompose_anomalies|decompose_forecast|divide|equals|exp|fft|fill_backward|fill_const|fill_forward|fill_linear|fir|fit_2lines_dynamic|fit_2lines|fit_line_dynamic|fit_line|fit_poly|greater_equals|greater|ifft|iir|less_equals|less|multiply|not_equals|outliers|pearson_correlation|periods_detect|periods_validate|pow|seasonal|sign|sin|stats|stats_dynamic|subtract|tan))\\\\b\",\"name\":\"support.function.kusto\"},{\"match\":\"\\\\b(bag|array)\\\\b\",\"name\":\"keyword.other.operator.kusto\"},{\"match\":\"\\\\b(asc|desc|nulls first|nulls last)\\\\b\",\"name\":\"keyword.other.kusto\"},{\"match\":\"\\\\b(regex|simple|relaxed)\\\\b\",\"name\":\"keyword.other.kusto\"},{\"match\":\"\\\\b(anomalychart|areachart|barchart|card|columnchart|ladderchart|linechart|piechart|pivotchart|scatterchart|stackedareachart|timechart|timepivot)\\\\b\",\"name\":\"support.function.kusto\"},{\"include\":\"#Strings\"},{\"match\":\"\\\\{.*?}\",\"name\":\"string.other.kusto\"},{\"match\":\"//.*\",\"name\":\"comment.line.kusto\"},{\"include\":\"#TimeSpanLiterals\"},{\"include\":\"#DateTimeTimeSpanFunctions\"},{\"include\":\"#DateTimeTimeSpanDataTypes\"},{\"include\":\"#Numeric\"},{\"match\":\"\\\\b(true|false|null)\\\\b\",\"name\":\"constant.language.kusto\"},{\"match\":\"\\\\b(anyif|any|array_strcat|base64_decodestring|base64_encodestring|make_dictionary|makelist|makeset|mvexpand|todynamic|parse_json|replace|weekofyear)(?=\\\\W*\\\\(|\\\\b)\",\"name\":\"invalid.deprecated.kusto\"}],\"repository\":{\"DateTimeTimeSpanDataTypes\":{\"patterns\":[{\"match\":\"\\\\b(datetime|timespan|time)\\\\b\",\"name\":\"storage.type.kusto\"}]},\"DateTimeTimeSpanFunctions\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"support.function.kusto\"},\"2\":{\"patterns\":[{\"include\":\"#DateTimeTimeSpanDataTypes\"}]},\"3\":{\"patterns\":[{\"include\":\"#Strings\"}]}},\"match\":\"\\\\b(format_datetime)(?:\\\\s*\\\\(\\\\s*(.+?)\\\\s*,\\\\s*([\\\"'].*?[\\\"'])\\\\s*\\\\))?(?!\\\\w)\",\"name\":\"meta.scalar.function.format_datetime.kusto\"},{\"match\":\"\\\\b(ago|datetime_add|datetime_diff|datetime_local_to_utc|datetime_part|datetime_utc_to_local|dayofmonth|dayofweek|dayofyear|endofday|endofmonth|endofweek|endofyear|format_timespan|getmonth|getyear|hourofday|make_datetime|make_timespan|monthofyear|now|startofday|startofmonth|startofweek|startofyear|todatetime|totimespan|week_of_year)(?=\\\\W*\\\\()\",\"name\":\"support.function.kusto\"}]},\"Escapes\":{\"patterns\":[{\"match\":\"(\\\\\\\\[\\\"'\\\\\\\\])\",\"name\":\"constant.character.escape.kusto\"}]},\"Numeric\":{\"patterns\":[{\"match\":\"\\\\b((0([Xx])\\\\h*)|(([0-9]+\\\\.?[0-9]*+)|(\\\\.[0-9]+))(([Ee])([-+])?[0-9]+)?)([Ll]|UL|ul|[FUfu]|ll|LL|ull|ULL)?(?=\\\\b|\\\\w)\",\"name\":\"constant.numeric.kusto\"}]},\"Strings\":{\"patterns\":[{\"begin\":\"([@h]?\\\")\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.string.kusto\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.kusto\"}},\"name\":\"string.quoted.double.kusto\",\"patterns\":[{\"include\":\"#Escapes\"}]},{\"begin\":\"([@h]?')\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.string.kusto\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.kusto\"}},\"name\":\"string.quoted.single.kusto\",\"patterns\":[{\"include\":\"#Escapes\"}]},{\"begin\":\"([@h]?```)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.string.kusto\"}},\"end\":\"```\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.kusto\"}},\"name\":\"string.quoted.multi.kusto\",\"patterns\":[{\"include\":\"#Escapes\"}]}]},\"TimeSpanLiterals\":{\"patterns\":[{\"match\":\"[-+]?(?:\\\\d*\\\\.)?\\\\d+(?:microseconds?|ticks?|seconds?|ms|[dhms])\\\\b\",\"name\":\"constant.numeric.kusto\"}]}},\"scopeName\":\"source.kusto\",\"aliases\":[\"kql\"]}"))

export default [
lang
]
