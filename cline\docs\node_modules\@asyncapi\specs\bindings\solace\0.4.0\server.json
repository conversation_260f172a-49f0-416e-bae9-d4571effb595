{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/solace/0.4.0/server.json", "title": "Solace server bindings object", "description": "This object contains server connection information about the Solace broker. This object contains additional connectivity information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"msgVpn": {"type": "string", "description": "The name of the Virtual Private Network to connect to on the Solace broker."}, "clientName": {"type": "string", "minLength": 1, "maxLength": 160, "description": "A unique client name to use to register to the appliance. If specified, it must be a valid Topic name, and a maximum of 160 bytes in length when encoded as UTF-8."}, "bindingVersion": {"type": "string", "enum": ["0.4.0"], "description": "The version of this binding."}}, "examples": [{"msgVpn": "ProdVPN", "bindingVersion": "0.4.0"}]}