import { OpenAPIV3, OpenAPIV3_1 } from 'openapi-types';
export type DocumentV3 = OpenAPIV3.Document | OpenAPIV3_1.Document;
export type Endpoint<D extends DataSchemaArray | IncrementalDataSchemaArray = DataSchemaArray | IncrementalDataSchemaArray> = {
    title?: string;
    description?: string;
    path: string;
    method: HttpMethod;
    servers?: Server[];
    request: RequestSchema<D>;
    response: ResponseSchema<D>;
    deprecated: boolean;
    type?: EndpointType;
    xMcp?: XMcp;
};
export type EndpointType = 'path' | 'webhook';
export declare class EndpointLocation {
    readonly type: EndpointType;
    readonly path: 'paths' | 'webhooks';
    readonly endpointPath: string;
    readonly document: OpenAPIV3_1.Document;
    constructor(document: OpenAPIV3_1.Document, path: string);
    getEndpointPaths(): OpenAPIV3_1.PathItemObject | undefined;
    getEndpoint(): OpenAPIV3_1.PathItemObject | undefined;
}
export type Server = {
    url: string;
    description?: string;
    variables?: {
        [variableName: string]: ServerVariableSchema;
    };
};
export type ServerVariableSchema = ServerVariableStringSchema | ServerVariableStringEnumSchema;
export type ServerVariableStringSchema = {
    type: 'string';
    default: string;
    description?: string;
};
export type ServerVariableStringEnumSchema = {
    type: 'enum<string>';
    enum: string[];
    default: string;
    description?: string;
};
export declare const httpMethods: readonly ["get", "put", "post", "delete", "options", "head", "patch", "trace"];
export type HttpMethod = (typeof httpMethods)[number];
export type RequestSchema<D extends DataSchemaArray | IncrementalDataSchemaArray = DataSchemaArray | IncrementalDataSchemaArray> = {
    security: SecurityOption[];
    parameters: ParameterSections<D>;
    body: BodySchema<D>;
    codeSamples?: CodeSample[];
};
export type CodeSample = {
    label?: string;
    lang: string;
    source: string;
};
export type BodySchema<D extends DataSchemaArray | IncrementalDataSchemaArray = DataSchemaArray | IncrementalDataSchemaArray> = {
    [contentType: string]: ContentSchema<D>;
};
export type ContentSchema<D extends DataSchemaArray | IncrementalDataSchemaArray = DataSchemaArray | IncrementalDataSchemaArray> = {
    schemaArray: D;
    description?: string;
    examples: {
        [title: string]: ExampleSchema;
    };
};
export type ExampleSchema = {
    summary?: string;
    description?: string;
    value: unknown;
};
export type ParameterSchema<D extends DataSchemaArray | IncrementalDataSchemaArray = DataSchemaArray | IncrementalDataSchemaArray> = {
    schema: D;
    style?: string;
    explode?: boolean;
};
export type ParameterGroup<D extends DataSchemaArray | IncrementalDataSchemaArray = DataSchemaArray | IncrementalDataSchemaArray> = {
    [name: string]: ParameterSchema<D>;
};
export type ParameterLocation = 'query' | 'header' | 'cookie' | 'path';
type NonPathParameterLocation = Exclude<ParameterLocation, 'path'>;
export type ParameterSections<D extends DataSchemaArray | IncrementalDataSchemaArray = DataSchemaArray | IncrementalDataSchemaArray> = Record<ParameterLocation, ParameterGroup<D>>;
export type SecurityOption = {
    title: string;
    parameters: SecurityParameterSections;
};
type ApiKeyParameterSchema = {
    type: 'apiKey';
    description?: string;
};
type HttpParameterSchema = {
    type: 'http';
    scheme: 'bearer' | 'basic';
    description?: string;
};
type OAuth2ParameterSchema = {
    type: 'oauth2';
    description?: string;
};
export type SecurityParameterSchema = ApiKeyParameterSchema | HttpParameterSchema | OAuth2ParameterSchema;
export type SecurityParameterGroup = {
    [name: string]: SecurityParameterSchema;
};
export type SecurityParameterSections = Record<NonPathParameterLocation, SecurityParameterGroup>;
export type ResponseSchema<D extends DataSchemaArray | IncrementalDataSchemaArray = DataSchemaArray | IncrementalDataSchemaArray> = {
    [code: string]: BodySchema<D>;
};
export declare const typeList: readonly ["boolean", "string", "number", "integer", "object", "array", "enum<string>", "enum<number>", "enum<integer>", "file", "null", "any"];
export type SchemaType = (typeof typeList)[number];
export type BaseSchema<T> = {
    type: SchemaType;
    title?: string;
    description?: string;
    placeholder?: string;
    default?: T;
    example?: T;
    examples?: T[];
    required?: boolean;
    readOnly?: boolean;
    writeOnly?: boolean;
    deprecated?: boolean;
    refIdentifier?: string;
    isOneOf?: string;
    isAnyOf?: string;
    isAllOf?: string;
};
export type BooleanSchema = {
    type: 'boolean';
} & BaseSchema<boolean>;
export type StringSchema = {
    type: 'string';
    format?: string;
    pattern?: string;
    maxLength?: number;
    minLength?: number;
    const?: string;
} & BaseSchema<string>;
export type NumberSchema = {
    type: 'number' | 'integer';
    multipleOf?: number;
    maximum?: number;
    exclusiveMaximum?: boolean;
    minimum?: number;
    exclusiveMinimum?: boolean;
} & BaseSchema<number>;
type BaseObjectSchema<R> = {
    type: 'object';
    additionalProperties?: boolean | R;
    maxProperties?: number;
    minProperties?: number;
    properties: {
        [key: string]: R;
    };
    requiredProperties?: string[];
} & BaseSchema<Record<string, unknown>>;
type BaseArraySchema<R> = {
    type: 'array';
    items: R;
    maxItems?: number;
    minItems?: number;
    uniqueItems?: boolean;
} & BaseSchema<unknown[]>;
export type StringEnumSchema = {
    type: 'enum<string>';
    enum: string[];
} & BaseSchema<string>;
export type NumberEnumSchema = {
    type: 'enum<number>' | 'enum<integer>';
    enum: number[];
} & BaseSchema<number>;
export type FileSchema = {
    type: 'file';
    contentEncoding?: string;
    contentMediaType?: string;
} & BaseSchema<File>;
export type NullSchema = {
    type: 'null';
} & BaseSchema<null>;
export type AnySchema = {
    type: 'any';
} & BaseSchema<unknown>;
export type ObjectSchema = BaseObjectSchema<DataSchemaArray>;
export type ArraySchema = BaseArraySchema<DataSchemaArray>;
export type DataSchema = BooleanSchema | StringSchema | NumberSchema | ObjectSchema | ArraySchema | StringEnumSchema | NumberEnumSchema | FileSchema | NullSchema | AnySchema;
export type DataSchemaArray = [DataSchema, ...DataSchema[]];
export type IncrementalObjectSchema = BaseObjectSchema<OpenAPIV3_1.SchemaObject>;
export type IncrementalArraySchema = BaseArraySchema<OpenAPIV3_1.SchemaObject>;
export type IncrementalDataSchema = BooleanSchema | StringSchema | NumberSchema | IncrementalObjectSchema | IncrementalArraySchema | StringEnumSchema | NumberEnumSchema | FileSchema | NullSchema | AnySchema;
export type IncrementalDataSchemaArray = [IncrementalDataSchema, ...IncrementalDataSchema[]];
export type XMcp = {
    enabled?: boolean;
    name?: string;
    description?: string;
};
export {};
