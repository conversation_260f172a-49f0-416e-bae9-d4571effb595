{"type": "object", "description": "JSON objects describing reusable channel parameters.", "additionalProperties": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.6.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.6.0/parameter.json"}]}, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/parameters.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/parameters.json"}