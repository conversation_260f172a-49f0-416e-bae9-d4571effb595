import { GithubInstallationType } from '../types/githubInstallationType.js';
import { GitlabInstallationType } from '../types/gitlabInstallationType.js';
import { GrowthDataType } from '../types/growthDataType.js';
import { OrgEntitlements } from './orgEntitlements.js';
export type OrgType = {
    _id: string;
    name: string;
    stytchOrgId: string;
    githubInstallations: GithubInstallationType[];
    gitlabInstallations: GitlabInstallationType[];
    plan?: 'trial' | 'startup' | 'growth' | 'enterprise';
    createdAt?: Date | string;
    growthData?: GrowthDataType;
    slug: string;
    stripe?: {
        customerId: string;
    };
    entitlements?: OrgEntitlements;
};
export type StaticPropsOrgType = Pick<OrgType, 'plan' | 'createdAt'>;
