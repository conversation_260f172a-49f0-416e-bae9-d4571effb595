import { z } from 'zod';
export declare const openApiSchema: z.<PERSON><[z.ZodEffects<z.ZodString, string, string>, z.Zod<PERSON>rray<z.ZodEffects<z.ZodString, string, string>, "many">]>;
export declare const apiSchema: z.ZodObject<{
    baseUrl: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodArray<z.ZodString, "many">]>>;
    auth: z.ZodOptional<z.ZodObject<{
        method: z.ZodOptional<z.ZodEnum<["bearer", "basic", "key", "cobo"]>>;
        name: z.ZodOptional<z.ZodString>;
        inputPrefix: z.ZodOptional<z.ZodString>;
    }, "strict", z.ZodType<PERSON>ny, {
        method?: "key" | "bearer" | "basic" | "cobo" | undefined;
        name?: string | undefined;
        inputPrefix?: string | undefined;
    }, {
        method?: "key" | "bearer" | "basic" | "cobo" | undefined;
        name?: string | undefined;
        inputPrefix?: string | undefined;
    }>>;
    playground: z.ZodOptional<z.ZodObject<{
        mode: z.ZodDefault<z.ZodOptional<z.ZodEnum<["show", "simple", "hide"]>>>;
        disableProxy: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        mode: "show" | "simple" | "hide";
        disableProxy?: boolean | undefined;
    }, {
        mode?: "show" | "simple" | "hide" | undefined;
        disableProxy?: boolean | undefined;
    }>>;
    request: z.ZodOptional<z.ZodObject<{
        example: z.ZodOptional<z.ZodObject<{
            showOptionalParams: z.ZodOptional<z.ZodBoolean>;
            languages: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodEnum<["bash", "python", "javascript", "php", "go", "java"]>, z.ZodString]>, "many">>;
        }, "strip", z.ZodTypeAny, {
            showOptionalParams?: boolean | undefined;
            languages?: string[] | undefined;
        }, {
            showOptionalParams?: boolean | undefined;
            languages?: string[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        example?: {
            showOptionalParams?: boolean | undefined;
            languages?: string[] | undefined;
        } | undefined;
    }, {
        example?: {
            showOptionalParams?: boolean | undefined;
            languages?: string[] | undefined;
        } | undefined;
    }>>;
    maintainOrder: z.ZodOptional<z.ZodBoolean>;
    paramFields: z.ZodOptional<z.ZodObject<{
        expanded: z.ZodOptional<z.ZodEnum<["all", "topLevel", "topLevelOneOfs", "none"]>>;
    }, "strip", z.ZodTypeAny, {
        expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
    }, {
        expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
    }>>;
}, "strict", z.ZodTypeAny, {
    baseUrl?: string | string[] | undefined;
    auth?: {
        method?: "key" | "bearer" | "basic" | "cobo" | undefined;
        name?: string | undefined;
        inputPrefix?: string | undefined;
    } | undefined;
    playground?: {
        mode: "show" | "simple" | "hide";
        disableProxy?: boolean | undefined;
    } | undefined;
    request?: {
        example?: {
            showOptionalParams?: boolean | undefined;
            languages?: string[] | undefined;
        } | undefined;
    } | undefined;
    maintainOrder?: boolean | undefined;
    paramFields?: {
        expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
    } | undefined;
}, {
    baseUrl?: string | string[] | undefined;
    auth?: {
        method?: "key" | "bearer" | "basic" | "cobo" | undefined;
        name?: string | undefined;
        inputPrefix?: string | undefined;
    } | undefined;
    playground?: {
        mode?: "show" | "simple" | "hide" | undefined;
        disableProxy?: boolean | undefined;
    } | undefined;
    request?: {
        example?: {
            showOptionalParams?: boolean | undefined;
            languages?: string[] | undefined;
        } | undefined;
    } | undefined;
    maintainOrder?: boolean | undefined;
    paramFields?: {
        expanded?: "all" | "topLevel" | "topLevelOneOfs" | "none" | undefined;
    } | undefined;
}>;
