// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface BrowserServiceDefinition {
  // Browser service methods
}

export interface UpdateBrowserSettingsRequest {
  settings: BrowserSettings;
}

export interface BrowserSettings {
  enabled: boolean;
  headless: boolean;
  viewport?: {
    width: number;
    height: number;
  };
  userAgent?: string;
  timeout?: number;
}

export interface BrowserSession {
  id: string;
  url: string;
  title?: string;
  status: string;
  createdAt: string;
}

export interface BrowserSessionListResponse {
  sessions: BrowserSession[];
}
