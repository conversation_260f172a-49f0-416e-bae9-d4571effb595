import type { Element } from 'hast';
import type { <PERSON><PERSON><PERSON><PERSON>, HastNodeIndex, HastNodeParent } from '../types/hast.js';
export declare function gitBookScrapeTabs(node: HastNode, _: HastNodeIndex, parent: HastNodeParent): Element | undefined;
export declare function readmeScrapeTabs(node: HastNode, _: HastNodeIndex, __: HastNodeParent): Element | undefined;
export declare function docusaurusScrapeTabs(node: HastNode, _: HastNodeIndex, parent: HastNodeParent): Element | undefined;
