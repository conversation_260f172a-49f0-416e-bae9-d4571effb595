{"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "description": "The type of the security scheme. Valid values", "enum": ["plain"]}, "description": {"type": "string", "description": "A short description for security scheme."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/Sasl.json"}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/SaslPlainSecurityScheme.json"}