import { remove } from 'unist-util-remove';
import { findExportedNodes } from '../../lib/index.js';
import { estreeIsProgram } from '../../utils.js';
export const remarkMdxRemoveUnusedVariables = () => (tree) => {
    const exportedVariables = findExportedNodes(tree, 'Literal', 'JSXElement');
    remove(tree, (node) => {
        if (!(nodeIsMdxFlowExpression(node) || nodeIsMdxTextExpression(node)) || !estreeIsProgram(node))
            return false;
        if (node.data.estree.body[0] == undefined || node.data.estree.body.length > 1)
            return false;
        if (node.data.estree.body[0].type !== 'ExpressionStatement')
            return false;
        if (node.data.estree.body[0].expression.type !== 'Identifier')
            return false;
        if (exportedVariables.includes(node.data.estree.body[0].expression.name))
            return false;
        if (node.data.estree.body[0].expression.name === 'user')
            return false;
        return true;
    });
};
const nodeIsMdxFlowExpression = (node) => node.type === 'mdxFlowExpression';
const nodeIsMdxTextExpression = (node) => node.type === 'mdxTextExpression';
