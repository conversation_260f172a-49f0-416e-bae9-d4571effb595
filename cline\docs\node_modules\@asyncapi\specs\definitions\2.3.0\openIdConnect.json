{"type": "object", "required": ["type", "openIdConnectUrl"], "properties": {"type": {"type": "string", "enum": ["openIdConnect"]}, "description": {"type": "string"}, "openIdConnectUrl": {"type": "string", "format": "uri"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.3.0/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.3.0/openIdConnect.json"}