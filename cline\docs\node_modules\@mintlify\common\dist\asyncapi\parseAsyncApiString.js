export const parseAsyncApiString = (str) => {
    const components = str.trim().split(/\s+/);
    let filename;
    let channelId;
    if (components.length > 2) {
        return undefined;
    }
    else if (components[0] && components[1]) {
        [filename, channelId] = components;
    }
    else if (components[0]) {
        channelId = components[0];
    }
    else {
        return undefined;
    }
    return {
        filename,
        channelId,
    };
};
