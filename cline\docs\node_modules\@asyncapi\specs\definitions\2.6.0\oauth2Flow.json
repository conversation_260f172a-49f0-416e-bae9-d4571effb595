{"type": "object", "description": "Configuration details for a supported OAuth Flow", "properties": {"authorizationUrl": {"type": "string", "format": "uri", "description": "The authorization URL to be used for this flow. This MUST be in the form of an absolute URL."}, "tokenUrl": {"type": "string", "format": "uri", "description": "The token URL to be used for this flow. This MUST be in the form of an absolute URL."}, "refreshUrl": {"type": "string", "format": "uri", "description": "The URL to be used for obtaining refresh tokens. This MUST be in the form of an absolute URL."}, "scopes": {"description": "The available scopes for the OAuth2 security scheme. A map between the scope name and a short description for it.", "$ref": "http://asyncapi.com/definitions/2.6.0/oauth2Scopes.json"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "additionalProperties": false, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/oauth2Flow.json"}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/oauth2Flow.json"}