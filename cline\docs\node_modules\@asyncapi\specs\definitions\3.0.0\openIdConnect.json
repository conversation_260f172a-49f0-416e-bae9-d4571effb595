{"type": "object", "required": ["type", "openIdConnectUrl"], "properties": {"type": {"type": "string", "description": "The type of the security scheme.", "enum": ["openIdConnect"]}, "description": {"type": "string", "description": "A short description for security scheme. CommonMark syntax MAY be used for rich text representation."}, "openIdConnectUrl": {"type": "string", "format": "uri", "description": "OpenId Connect URL to discover OAuth2 configuration values. This MUST be in the form of an absolute URL."}, "scopes": {"type": "array", "description": "List of the needed scope names. An empty array means no scopes are needed.", "items": {"type": "string"}}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/openIdConnect.json"}