import { z } from 'zod';
export declare const redirectsSchema: z.<PERSON><z.<PERSON><PERSON><PERSON><PERSON><z.ZodObject<{
    source: z.ZodString;
    destination: z.ZodString;
    permanent: z.Zod<PERSON>ptional<z.ZodBoolean>;
}, "strip", z.<PERSON>ny, {
    source: string;
    destination: string;
    permanent?: boolean | undefined;
}, {
    source: string;
    destination: string;
    permanent?: boolean | undefined;
}>, "many">, {
    source: string;
    destination: string;
    permanent?: boolean | undefined;
}[], {
    source: string;
    destination: string;
    permanent?: boolean | undefined;
}[]>;
