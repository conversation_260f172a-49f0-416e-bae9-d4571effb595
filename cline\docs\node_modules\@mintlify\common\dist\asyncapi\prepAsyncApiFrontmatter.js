import { parse } from 'path';
import { parseAsyncApiString } from '../asyncapi/parseAsyncApiString.js';
import { normalizeRelativePath } from '../fs/normalizeRelativePath.js';
import { schemaFileFrontmatterIsOriginalFileLocation, isRemoteSchemaUrl, } from '../schema/common.js';
export function prepAsyncApiFrontmatter(currPath, asyncApiFrontmatter) {
    if (!asyncApiFrontmatter)
        return undefined;
    const asyncApiObj = parseAsyncApiString(asyncApiFrontmatter);
    if ((asyncApiObj === null || asyncApiObj === void 0 ? void 0 : asyncApiObj.filename) && schemaFileFrontmatterIsOriginalFileLocation(asyncApiObj.filename)) {
        if (isRemoteSchemaUrl(asyncApiObj.filename)) {
            return `${asyncApiObj.filename} ${asyncApiObj.channelId}`;
        }
        const currDir = parse(currPath).dir;
        const newAsyncApiFilename = normalizeRelativePath(currDir, asyncApiObj.filename);
        return `${newAsyncApiFilename} ${asyncApiObj.channelId}`;
    }
    return asyncApiFrontmatter;
}
