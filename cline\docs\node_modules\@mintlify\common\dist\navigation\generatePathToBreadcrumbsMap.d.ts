import { Navigation, NavigationGroup } from '@mintlify/models';
import { GroupConfig } from '@mintlify/validation';
export declare function generatePathToBreadcrumbsMap(navigation: Navigation): Map<any, any>;
export declare function generatePathToBreadcrumbsMapRecursive<T extends NavigationGroup | GroupConfig>(navGroup: T, currentBreadcrumbs: string[], map: Map<string, string[]>, depth?: number): void;
