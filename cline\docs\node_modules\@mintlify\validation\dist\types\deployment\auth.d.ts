import { UserInfo } from '../userInfo.js';
import { authEntitlements, userAuthEntitlements } from './deploymentEntitlements.js';
export type UserAuth = (SharedSessionAuth | JwtAuth | OAuth) & {
    invalidatedAt?: number;
};
export type UserAuthType = UserAuth['type'];
export type Auth = (JwtAuth | OAuthWithClientSecret | PasswordAuth | MintlifyAuth) & {
    partial: boolean;
};
export type AuthType = Auth['type'];
export type PreviewDeploymentAuth = MintlifyAuth;
export type SharedSessionAuth = {
    type: 'shared-session';
    apiUrl: string;
    loginUrl?: string;
};
export type JwtAuthSigningKey = {
    id: string;
    publicKey: string;
    createdAt: number;
};
export type JwtAuth = {
    type: 'jwt';
    signingKeys: JwtAuthSigningKey[];
    loginUrl: string;
};
type OAuthBase = {
    authorizationUrl: string;
    clientId: string;
    scopes: string[];
    tokenUrl: string;
    /** Additional query values to add to the initial authorization request. */
    additionalAuthorizationParams?: Record<string, string>;
};
export type OAuth = OAuthBase & {
    type: 'oauth';
    apiUrl: string;
};
export type OAuthWithClientSecret = OAuthBase & {
    type: 'oauth-with-client-secret';
    encryptedClientSecret: string;
    apiUrl?: string;
};
export type PasswordAuthMetadata = {
    id: string;
    value: {
        type: 'encrypted';
        encryptedValue: string;
    };
    createdAt: number;
};
export type PasswordAuth = {
    type: 'password';
    passwords: PasswordAuthMetadata[];
};
export type MintlifyAuth = {
    type: 'mintlify';
};
export type ClientMiddlewareJwt = {
    subdomain?: string;
    customDomain?: string;
    userInfo: UserInfo;
};
export declare function AuthEntitlementToAuthType(entitlement: (typeof authEntitlements)[number] | (typeof userAuthEntitlements)[number]): AuthType | UserAuthType;
export {};
