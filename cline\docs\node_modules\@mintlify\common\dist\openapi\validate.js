var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { validate as scalarValidate } from '@mintlify/openapi-parser';
export const validate = (document) => __awaiter(void 0, void 0, void 0, function* () {
    const result = yield safeValidate(document);
    if ('errorMessage' in result) {
        throw new Error(result.errorMessage);
    }
    return result;
});
export const safeValidate = (document) => __awaiter(void 0, void 0, void 0, function* () {
    const result = yield scalarValidate(document);
    const { errors, schema, valid, version } = result;
    if (version === '2.0')
        return Object.assign(Object.assign({}, result), { errorMessage: 'OpenAPI versions prior to 3.0 not supported', valid: false });
    if (!valid) {
        const errorMessages = errors
            ? errors
                .map((error) => {
                const path = 'path' in error
                    ? String(error.path)
                        .replace('/paths/', '')
                        .replaceAll('~1', '/')
                        .replaceAll('~2', '~')
                    : 'Unknown path';
                return `${path}: ${error.message}`;
            })
                .join('\n')
            : 'An unknown error occurred when trying to validate your OpenAPI spec. Please pass it through an OpenAPI validator and try again.';
        const errorMessage = `Failed to validate OpenAPI schema:${errorMessages}`;
        return Object.assign(Object.assign({}, result), { errorMessage });
    }
    if (!schema)
        return Object.assign(Object.assign({}, result), { errorMessage: 'Schema failed to validate. Please try again.' });
    return result;
});
