import { z } from 'zod';
export declare const iconSchema: <PERSON><PERSON><[z.Zod<PERSON>ffects<z.ZodString, string, string>, z.ZodObject<{
    style: z.<PERSON>od<PERSON>ptional<z.Zod<PERSON>num<["brands", "duotone", "light", "regular", "sharp-duotone-solid", "sharp-light", "sharp-regular", "sharp-solid", "sharp-thin", "solid", "thin"]>>;
    name: z.<PERSON><z.ZodString, string, string>;
    library: z.<PERSON>od<PERSON>ptional<z.ZodEnum<["fontawesome", "lucide"]>>;
}, "strip", z.<PERSON>odType<PERSON>ny, {
    name: string;
    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    library?: "fontawesome" | "lucide" | undefined;
}, {
    name: string;
    style?: "brands" | "duotone" | "light" | "regular" | "sharp-duotone-solid" | "sharp-light" | "sharp-regular" | "sharp-solid" | "sharp-thin" | "solid" | "thin" | undefined;
    library?: "fontawesome" | "lucide" | undefined;
}>]>;
export type IconConfig = z.infer<typeof iconSchema>;
