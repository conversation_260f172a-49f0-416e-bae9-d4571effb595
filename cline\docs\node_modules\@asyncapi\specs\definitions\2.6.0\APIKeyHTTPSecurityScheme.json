{"type": "object", "required": ["type", "name", "in"], "properties": {"type": {"type": "string", "description": "The type of the security scheme. ", "enum": ["httpApiKey"]}, "name": {"type": "string", "description": "The name of the header, query or cookie parameter to be used."}, "in": {"type": "string", "description": "The location of the API key. ", "enum": ["header", "query", "cookie"]}, "description": {"type": "string", "description": "A short description for security scheme."}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.6.0/specificationExtension.json"}}, "example": {"$ref": "http://asyncapi.com/examples/2.6.0/APIKeyHTTPSecurityScheme.json"}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.6.0/APIKeyHTTPSecurityScheme.json"}