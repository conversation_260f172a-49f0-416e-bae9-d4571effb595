import { z } from 'zod';
import { asyncApiSchema } from '../reusable/asyncapi.js';
import { hiddenSchema } from '../reusable/hidden.js';
import { hrefSchema } from '../reusable/href.js';
import { openApiSchema } from '../reusable/openapi.js';
import { anchorsSchema, decoratedAnchorsSchema } from './anchors.js';
import { decoratedDropdownsSchema, dropdownsSchema } from './dropdown.js';
import { globalSchema } from './global.js';
import { decoratedGroupsSchema, groupsSchema } from './groups.js';
import { decoratedLanguagesSchema, languagesSchema } from './languages.js';
import { decoratedPagesSchema, pagesSchema } from './pages.js';
import { decoratedTabsSchema, tabsSchema } from './tabs.js';
export const baseVersionSchema = z.object({
    version: z.string().nonempty().describe('The name of the version'),
    default: z.boolean().optional().describe('Whether this version is the default version'),
    hidden: hiddenSchema.optional(),
});
export const nonRecursiveVersionSchema = baseVersionSchema.extend({ href: hrefSchema });
export const versionSchema = z.union([
    baseVersionSchema.extend({
        href: hrefSchema,
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseVersionSchema.extend({
        languages: z.lazy(() => languagesSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseVersionSchema.extend({
        tabs: z.lazy(() => tabsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseVersionSchema.extend({
        dropdowns: z.lazy(() => dropdownsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseVersionSchema.extend({
        anchors: z.lazy(() => anchorsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseVersionSchema.extend({
        groups: z.lazy(() => groupsSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseVersionSchema.extend({
        pages: z.lazy(() => pagesSchema),
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
    baseVersionSchema.extend({
        openapi: openApiSchema.optional(),
        asyncapi: asyncApiSchema.optional(),
        global: z.lazy(() => globalSchema).optional(),
    }),
]);
export const decoratedVersionSchema = baseVersionSchema.and(z.union([
    z.object({ href: hrefSchema }),
    z.lazy(() => z.object({ languages: decoratedLanguagesSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ tabs: decoratedTabsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ dropdowns: decoratedDropdownsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ anchors: decoratedAnchorsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ groups: decoratedGroupsSchema, global: globalSchema.optional() })),
    z.lazy(() => z.object({ pages: decoratedPagesSchema, global: globalSchema.optional() })),
]));
export const versionsSchema = z
    .array(versionSchema)
    .min(1, 'At least one version must be specified')
    .describe('Organizing by versions');
export const decoratedVersionsSchema = z
    .array(decoratedVersionSchema)
    .min(1, 'At least one version must be specified')
    .describe('Organizing by versions');
