{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.2.0/avroSchema_v1.json", "definitions": {"avroSchema": {"title": "<PERSON><PERSON><PERSON>", "description": "Root Schema", "oneOf": [{"$ref": "#/definitions/types"}]}, "types": {"title": "Avro Types", "description": "Allowed Avro types", "oneOf": [{"$ref": "#/definitions/primitiveType"}, {"$ref": "#/definitions/primitiveTypeWithMetadata"}, {"$ref": "#/definitions/customTypeReference"}, {"$ref": "#/definitions/avroRecord"}, {"$ref": "#/definitions/avroEnum"}, {"$ref": "#/definitions/avroArray"}, {"$ref": "#/definitions/avroMap"}, {"$ref": "#/definitions/avroFixed"}, {"$ref": "#/definitions/avroUnion"}]}, "primitiveType": {"title": "Primitive Type", "description": "Basic type primitives.", "type": "string", "enum": ["null", "boolean", "int", "long", "float", "double", "bytes", "string"]}, "primitiveTypeWithMetadata": {"title": "Primitive Type With Metadata", "description": "A primitive type with metadata attached.", "type": "object", "properties": {"type": {"$ref": "#/definitions/primitiveType"}}, "required": ["type"]}, "customTypeReference": {"title": "Custom Type", "description": "Reference to a ComplexType", "not": {"$ref": "#/definitions/primitiveType"}, "type": "string", "pattern": "^[A-Za-z_][A-Za-z0-9_]*(\\.[A-Za-z_][A-Za-z0-9_]*)*$"}, "avroUnion": {"title": "Union", "description": "A Union of types", "type": "array", "items": {"$ref": "#/definitions/avroSchema"}, "minItems": 1}, "avroField": {"title": "Field", "description": "A field within a Record", "type": "object", "properties": {"name": {"$ref": "#/definitions/name"}, "type": {"$ref": "#/definitions/types"}, "doc": {"type": "string"}, "default": true, "order": {"enum": ["ascending", "descending", "ignore"]}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/name"}}}, "required": ["name", "type"]}, "avroRecord": {"title": "Record", "description": "A Record", "type": "object", "properties": {"type": {"type": "string", "const": "record"}, "name": {"$ref": "#/definitions/name"}, "namespace": {"$ref": "#/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/name"}}, "fields": {"type": "array", "items": {"$ref": "#/definitions/avroField"}}}, "required": ["type", "name", "fields"]}, "avroEnum": {"title": "Enum", "description": "An enumeration", "type": "object", "properties": {"type": {"type": "string", "const": "enum"}, "name": {"$ref": "#/definitions/name"}, "namespace": {"$ref": "#/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/name"}}, "symbols": {"type": "array", "items": {"$ref": "#/definitions/name"}}}, "required": ["type", "name", "symbols"]}, "avroArray": {"title": "Array", "description": "An array", "type": "object", "properties": {"type": {"type": "string", "const": "array"}, "name": {"$ref": "#/definitions/name"}, "namespace": {"$ref": "#/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/name"}}, "items": {"$ref": "#/definitions/types"}}, "required": ["type", "items"]}, "avroMap": {"title": "Map", "description": "A map of values", "type": "object", "properties": {"type": {"type": "string", "const": "map"}, "name": {"$ref": "#/definitions/name"}, "namespace": {"$ref": "#/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/name"}}, "values": {"$ref": "#/definitions/types"}}, "required": ["type", "values"]}, "avroFixed": {"title": "Fixed", "description": "A fixed sized array of bytes", "type": "object", "properties": {"type": {"type": "string", "const": "fixed"}, "name": {"$ref": "#/definitions/name"}, "namespace": {"$ref": "#/definitions/namespace"}, "doc": {"type": "string"}, "aliases": {"type": "array", "items": {"$ref": "#/definitions/name"}}, "size": {"type": "number"}}, "required": ["type", "name", "size"]}, "name": {"type": "string", "pattern": "^[A-Za-z_][A-Za-z0-9_]*$"}, "namespace": {"type": "string", "pattern": "^([A-Za-z_][A-Za-z0-9_]*(\\.[A-Za-z_][A-Za-z0-9_]*)*)*$"}}, "description": "Json-Schema definition for Avro AVSC files.", "oneOf": [{"$ref": "#/definitions/avroSchema"}], "title": "Avro Schema Definition"}