import { z } from 'zod';
export declare const nameSchema: z.ZodString;
export declare const logoSchema: z.<PERSON><[z.ZodString, z.ZodObject<{
    light: z.ZodString;
    dark: z.ZodString;
    href: z.ZodOptional<z.ZodString>;
}, "strip", z.Zod<PERSON>ype<PERSON>ny, {
    light: string;
    dark: string;
    href?: string | undefined;
}, {
    light: string;
    dark: string;
    href?: string | undefined;
}>]>;
export declare const modeToggleSchema: z.ZodObject<{
    default: z.ZodOptional<z.ZodEnum<["light", "dark"]>>;
    isHidden: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.Zod<PERSON>ype<PERSON>ny, {
    default?: "light" | "dark" | undefined;
    isHidden?: boolean | undefined;
}, {
    default?: "light" | "dark" | undefined;
    isHidden?: boolean | undefined;
}>;
export declare const eyebrowSchema: z.ZodObject<{
    display: z.ZodOptional<z.ZodEnum<["section", "breadcrumbs"]>>;
}, "strip", z.ZodType<PERSON>, {
    display?: "section" | "breadcrumbs" | undefined;
}, {
    display?: "section" | "breadcrumbs" | undefined;
}>;
export declare const isWhiteLabeledSchema: z.ZodBoolean;
export declare const metadataSchema: z.ZodRecord<z.ZodString, z.ZodString>;
export declare const codeBlockSchema: z.ZodObject<{
    mode: z.ZodOptional<z.ZodEnum<["auto", "dark"]>>;
}, "strip", z.ZodTypeAny, {
    mode?: "dark" | "auto" | undefined;
}, {
    mode?: "dark" | "auto" | undefined;
}>;
export declare const feedbackSchema: z.ZodObject<{
    thumbsRating: z.ZodOptional<z.ZodBoolean>;
    suggestEdit: z.ZodOptional<z.ZodBoolean>;
    raiseIssue: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    thumbsRating?: boolean | undefined;
    suggestEdit?: boolean | undefined;
    raiseIssue?: boolean | undefined;
}, {
    thumbsRating?: boolean | undefined;
    suggestEdit?: boolean | undefined;
    raiseIssue?: boolean | undefined;
}>;
export declare const searchSchema: z.ZodObject<{
    prompt: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodEnum<["side", "top"]>>;
}, "strip", z.ZodTypeAny, {
    prompt?: string | undefined;
    location?: "side" | "top" | undefined;
}, {
    prompt?: string | undefined;
    location?: "side" | "top" | undefined;
}>;
export declare const redirectsSchema: z.ZodEffects<z.ZodArray<z.ZodObject<{
    source: z.ZodString;
    destination: z.ZodString;
    permanent: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    source: string;
    destination: string;
    permanent?: boolean | undefined;
}, {
    source: string;
    destination: string;
    permanent?: boolean | undefined;
}>, "many">, {
    source: string;
    destination: string;
    permanent?: boolean | undefined;
}[], {
    source: string;
    destination: string;
    permanent?: boolean | undefined;
}[]>;
export declare const ctaButtonSchema: z.ZodUnion<[z.ZodObject<{
    type: z.ZodOptional<z.ZodLiteral<"link">>;
    name: z.ZodString;
    url: z.ZodString;
    style: z.ZodOptional<z.ZodEnum<["pill", "roundedRectangle"]>>;
    arrow: z.ZodOptional<z.ZodBoolean>;
}, "strict", z.ZodTypeAny, {
    name: string;
    url: string;
    type?: "link" | undefined;
    style?: "pill" | "roundedRectangle" | undefined;
    arrow?: boolean | undefined;
}, {
    name: string;
    url: string;
    type?: "link" | undefined;
    style?: "pill" | "roundedRectangle" | undefined;
    arrow?: boolean | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"github">;
    url: z.ZodString;
}, "strict", z.ZodTypeAny, {
    type: "github";
    url: string;
}, {
    type: "github";
    url: string;
}>]>;
