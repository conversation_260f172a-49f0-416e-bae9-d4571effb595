{"version": 3, "file": "mark.js", "sourceRoot": "", "sources": ["../../src/mark.ts"], "names": [], "mappings": "AACA,YAAY,CAAC;AAGb,mCAAoC;AAEpC,MAAM,IAAI;IAER,YAAmB,IAAW,EAAS,MAAa,EAAS,QAAe,EAAS,IAAW,EAAS,MAAa;QAAnG,SAAI,GAAJ,IAAI,CAAO;QAAS,WAAM,GAAN,MAAM,CAAO;QAAS,aAAQ,GAAR,QAAQ,CAAO;QAAS,SAAI,GAAJ,IAAI,CAAO;QAAS,WAAM,GAAN,MAAM,CAAO;IACtH,CAAC;IAMD,UAAU,CAAC,SAAc,CAAC,EAAE,YAAiB,EAAE;QAC/C,IAAI,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO,IAAI,CAAC;SACb;QAED,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;QACrB,SAAS,GAAG,SAAS,IAAI,EAAE,CAAC;QAE5B,IAAI,GAAG,EAAE,CAAC;QACV,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEtB,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,0BAA0B,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;YAC5F,KAAK,IAAI,CAAC,CAAC;YACX,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC/C,IAAI,GAAG,OAAO,CAAC;gBACf,KAAK,IAAI,CAAC,CAAC;gBACX,MAAM;aACP;SACF;QAED,IAAI,GAAG,EAAE,CAAC;QACV,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEpB,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,0BAA0B,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;YACrG,GAAG,IAAI,CAAC,CAAC;YACT,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC7C,IAAI,GAAG,OAAO,CAAC;gBACf,GAAG,IAAI,CAAC,CAAC;gBACT,MAAM;aACP;SACF;QAED,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAExC,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI;YAC5D,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;IAC3E,CAAC;IAED,QAAQ,CAAE,UAAgB,IAAI;QAC9B,IAAI,OAAO,EAAE,KAAK,GAAG,EAAE,CAAC;QAExB,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACpC;QAED,KAAK,IAAI,UAAU,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAExE,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAE5B,IAAI,OAAO,EAAE;gBACX,KAAK,IAAI,KAAK,GAAG,OAAO,CAAC;aAC1B;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CAEA;AACD,iBAAS,IAAI,CAAA"}