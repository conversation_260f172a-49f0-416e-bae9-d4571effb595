import type { Root, RootContent } from 'mdast';
/**
 * Injects content to the top of a file, but below the frontmatter
 */
export declare const injectToTopOfFile: (content: string, contentToInject: string) => string;
/**
 * Injects nodes to the top of a MDX/Markdown AST (Root), but below the frontmatter (YAML node).
 * Modifies the tree in place.
 * @param tree The Root node of the document.
 * @param nodesToInject An array of RootContent nodes to inject.
 */
export declare const injectToTopOfFileOfTree: (tree: Root, nodesToInject: RootContent[]) => void;
