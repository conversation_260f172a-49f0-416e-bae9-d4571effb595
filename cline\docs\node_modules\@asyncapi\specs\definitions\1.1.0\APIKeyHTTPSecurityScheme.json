{"type": "object", "required": ["type", "name", "in"], "properties": {"type": {"type": "string", "enum": ["httpApiKey"]}, "name": {"type": "string"}, "in": {"type": "string", "enum": ["header", "query", "cookie"]}, "description": {"type": "string"}}, "patternProperties": {"^x-": {}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.1.0/APIKeyHTTPSecurityScheme.json"}