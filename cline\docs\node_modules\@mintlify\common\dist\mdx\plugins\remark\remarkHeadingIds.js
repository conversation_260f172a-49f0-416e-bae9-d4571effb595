import { slugifyWithCounter } from '@sindresorhus/slugify';
import { visit } from 'unist-util-visit';
import { slugify } from '../../../slugify.js';
import { createMdxJsxAttribute } from '../../lib/remark-utils.js';
import { getTableOfContentsTitle } from '../../lib/remark-utils.js';
export const HEADING_LEVELS = [1, 2, 3, 4];
export const remarkHeadingIds = () => (tree) => {
    const slugifyFn = slugifyWithCounter();
    visit(tree, 'heading', (node, _, parent) => {
        if (HEADING_LEVELS.includes(node.depth)) {
            const title = getTableOfContentsTitle(node);
            const slug = slugify(title, slugifyFn);
            const mdxJsxAttributes = [
                createMdxJsxAttribute('level', node.depth),
                createMdxJsxAttribute('id', slug),
                createMdxJsxAttribute('isAtRootLevel', (parent === null || parent === void 0 ? void 0 : parent.type) === 'root'),
            ];
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            node.attributes = mdxJsxAttributes;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            node.type = 'mdxJsxFlowElement';
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            node.name = 'Heading';
        }
    });
};
