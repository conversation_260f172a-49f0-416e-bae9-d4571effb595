{"description": "The Schema Object allows the definition of input and output data types. These types can be objects, but also primitives and arrays. This object is a superset of the JSON Schema Specification Draft 07. The empty schema (which allows any instance to validate) MAY be represented by the boolean value true and a schema which allows no instance to validate MAY be represented by the boolean value false.", "allOf": [{"$ref": "http://json-schema.org/draft-07/schema#"}, {"patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"additionalProperties": {"anyOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}, {"type": "boolean"}], "default": {}}, "items": {"anyOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}, {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}}], "default": {}}, "allOf": {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}}, "oneOf": {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}}, "anyOf": {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}}, "not": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}, "properties": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}, "default": {}}, "patternProperties": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}, "default": {}}, "propertyNames": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}, "contains": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json"}, "discriminator": {"type": "string", "description": "Adds support for polymorphism. The discriminator is the schema property name that is used to differentiate between other schema that inherit this schema. The property name used MUST be defined at this schema and it MUST be in the required property list. When used, the value MUST be the name of this schema or any schema that inherits it. See Composition and Inheritance for more details."}, "externalDocs": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/3.0.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/3.0.0/externalDocs.json"}]}, "deprecated": {"type": "boolean", "description": "Specifies that a schema is deprecated and SHOULD be transitioned out of usage. Default value is false.", "default": false}}}], "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/schema.json"}