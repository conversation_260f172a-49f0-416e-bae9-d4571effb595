import { z } from 'zod';
export declare const versionsSchema: z.<PERSON><z.<PERSON>od<PERSON>y<z.<PERSON><[z.ZodString, z.ZodObject<{
    name: z.ZodString;
    url: z.<PERSON>od<PERSON>ptional<z.ZodString>;
    default: z.<PERSON>ptional<z.<PERSON>odLiteral<true>>;
    locale: z.<PERSON><z.<PERSON>od<PERSON><["en", "cn", "zh", "zh-Hans", "zh-Hant", "es", "fr", "fr-CA", "ja", "jp", "pt", "pt-BR", "de", "ko", "it", "ru", "id", "ar", "tr", "hi"]>>;
}, "strict", z.<PERSON>ny, {
    name: string;
    url?: string | undefined;
    default?: true | undefined;
    locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
}, {
    name: string;
    url?: string | undefined;
    default?: true | undefined;
    locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
}>]>, "many">, (string | {
    name: string;
    url?: string | undefined;
    default?: true | undefined;
    locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
})[], (string | {
    name: string;
    url?: string | undefined;
    default?: true | undefined;
    locale?: "id" | "en" | "cn" | "zh" | "zh-Hans" | "zh-Hant" | "es" | "fr" | "fr-CA" | "ja" | "jp" | "pt" | "pt-BR" | "de" | "ko" | "it" | "ru" | "ar" | "tr" | "hi" | undefined;
})[]>;
