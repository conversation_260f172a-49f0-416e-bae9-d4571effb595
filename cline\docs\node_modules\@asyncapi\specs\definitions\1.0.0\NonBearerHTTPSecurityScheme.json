{"not": {"type": "object", "properties": {"scheme": {"type": "string", "enum": ["bearer"]}}}, "type": "object", "required": ["scheme", "type"], "properties": {"scheme": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["http"]}}, "patternProperties": {"^x-": {}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.0.0/NonBearerHTTPSecurityScheme.json"}