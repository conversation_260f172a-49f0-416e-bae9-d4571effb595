{"type": "object", "description": "Map describing protocol-specific definitions for an operation.", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"http": {"properties": {"bindingVersion": {"enum": ["0.2.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/http/0.3.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/http/0.2.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/http/0.3.0/operation.json"}}]}, "ws": {}, "amqp": {"properties": {"bindingVersion": {"enum": ["0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/amqp/0.3.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/amqp/0.3.0/operation.json"}}]}, "amqp1": {}, "mqtt": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/mqtt/0.2.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/mqtt/0.2.0/operation.json"}}]}, "kafka": {"properties": {"bindingVersion": {"enum": ["0.5.0", "0.4.0", "0.3.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/kafka/0.5.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.5.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/kafka/0.5.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/kafka/0.4.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/kafka/0.3.0/operation.json"}}]}, "anypointmq": {}, "nats": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/nats/0.1.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/nats/0.1.0/operation.json"}}]}, "jms": {}, "sns": {"properties": {"bindingVersion": {"enum": ["0.1.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/sns/0.1.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.1.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/sns/0.1.0/operation.json"}}]}, "sqs": {"properties": {"bindingVersion": {"enum": ["0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/sqs/0.2.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/sqs/0.2.0/operation.json"}}]}, "stomp": {}, "redis": {}, "ibmmq": {}, "solace": {"properties": {"bindingVersion": {"enum": ["0.4.0", "0.3.0", "0.2.0"]}}, "allOf": [{"description": "If no bindingVersion specified, use the latest binding", "if": {"not": {"required": ["bindingVersion"]}}, "then": {"$ref": "http://asyncapi.com/bindings/solace/0.4.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.4.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/solace/0.4.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.3.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/solace/0.3.0/operation.json"}}, {"if": {"required": ["bindingVersion"], "properties": {"bindingVersion": {"const": "0.2.0"}}}, "then": {"$ref": "http://asyncapi.com/bindings/solace/0.2.0/operation.json"}}]}, "googlepubsub": {}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/operationBindingsObject.json"}