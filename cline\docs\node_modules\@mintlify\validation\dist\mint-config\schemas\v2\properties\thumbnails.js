import { z } from 'zod';
export var ThumbnailAppearance;
(function (ThumbnailAppearance) {
    ThumbnailAppearance["Light"] = "light";
    ThumbnailAppearance["Dark"] = "dark";
})(ThumbnailAppearance || (ThumbnailAppearance = {}));
export const thumbnailsSchema = z.object({
    appearance: z
        .enum([ThumbnailAppearance.Light, ThumbnailAppearance.Dark])
        .optional()
        .describe('The appearance of the thumbnail. Defaults to generated by colors.'),
    background: z
        .string()
        .optional()
        .describe('Background image for the thumbnail. Can be an absolute URL or relative path.'),
});
