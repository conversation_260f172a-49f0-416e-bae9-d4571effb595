{"version": 3, "file": "EventManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/session/EventManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAGH,+DAGuC;AACvC,wDAAgD;AAChD,gEAAwD;AACxD,wEAAgE;AAChE,oEAA4D;AAC5D,8DAAsD;AAEtD,iEAAyD;AAGzD,2CAAiD;AACjD,qEAA6D;AAE7D,MAAM,YAAY;IACP,UAAU,GAAG,IAAI,wBAAS,EAAE,CAAC;IAC7B,UAAU,CAAyC;IACnD,MAAM,CAAsC;IAErD,YACE,KAA0C,EAC1C,SAAiD;QAEjD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;IAC5B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AAYD;;GAEG;AACH,MAAM,iBAAiB,GAAiD,IAAI,GAAG,CAC7E,CAAC,CAAC,0BAAY,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,CACnD,CAAC;AAUF,MAAa,YAAa,SAAQ,8BAAmC;IACnE;;;;OAIG;IACH,mBAAmB,GAAG,IAAI,0BAAU,CAGlC,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IACnB;;;OAGG;IACH,aAAa,GAAG,IAAI,GAAG,EAAgC,CAAC;IACxD;;;;OAIG;IACH,gBAAgB,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC7C,oBAAoB,CAAsB;IAC1C,uBAAuB,CAAyB;IAChD;;OAEG;IACH,eAAe,CAGb;IAEF,YAAY,sBAA8C;QACxD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,oBAAoB,GAAG,IAAI,4CAAmB,CAAC,sBAAsB,CAAC,CAAC;QAC5E,IAAI,CAAC,eAAe,GAAG,IAAI,0BAAU,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CACf,SAAkC,EAClC,eAAuD,EACvD,OAAyB;QAEzB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAC,SAAS,EAAE,eAAe,EAAE,OAAO,EAAC,CAAC,CAAC;IAC/D,CAAC;IAED,gBAAgB,CACd,KAA8B,EAC9B,IAAmE;QAEnE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,aAAa,CACX,KAAyB,EACzB,SAAiD;QAEjD,IAAI,CAAC,oBAAoB,CACvB,OAAO,CAAC,OAAO,CAAC;YACd,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,KAAK;SACb,CAAC,EACF,SAAS,EACT,KAAK,CAAC,MAAM,CACb,CAAC;IACJ,CAAC;IAED,oBAAoB,CAClB,KAA0C,EAC1C,SAAiD,EACjD,SAAkC;QAElC,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACxD,MAAM,cAAc,GAClB,IAAI,CAAC,oBAAoB,CAAC,4BAA4B,CACpD,SAAS,EACT,SAAS,CACV,CAAC;QACJ,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC3C,wDAAwD;QACxD,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,yCAA2B;gBAClC,OAAO,EAAE,oCAAe,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC;gBAC1D,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CACb,UAAqC,EACrC,UAAsD,EACtD,OAAwB;QAExB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAA,gCAAoB,EAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED,6CAA6C;QAC7C,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;gBACvB,0DAA0D;gBAC1D,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,oFAAoF;QACpF,qFAAqF;QACrF,4BAA4B;QAC5B,MAAM,sBAAsB,GAAuB,EAAE,CAAC;QAEtD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,sBAAsB,CAAC,IAAI,CACzB,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CACtE,CAAC;gBAEF,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,kBAAkB,CAChD,SAAS,EACT,SAAS,EACT,OAAO,CACR,EAAE,CAAC;oBACF,wCAAwC;oBACxC,IAAI,CAAC,IAAI,yCAA2B;wBAClC,OAAO,EAAE,oCAAe,CAAC,iBAAiB,CACxC,YAAY,CAAC,KAAK,EAClB,OAAO,CACR;wBACD,KAAK,EAAE,SAAS;qBACjB,CAAC,CAAC;oBACH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QAED,8EAA8E;QAC9E,mFAAmF;QACnF,sFAAsF;QACtF,4DAA4D;QAC5D,IAAA,kCAAc,EAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,EAAE,EAAE;YACpE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,WAAW,CACf,UAAqC,EACrC,UAAsD,EACtD,OAAwB;QAExB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAA,gCAAoB,EAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC1E,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,2CAA2C;QAC3C,qCAAqC;QACrC,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAClE,OAAO,MAAM,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC/C,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,YAA0B,EAAE,SAAkC;QACzE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,6CAA6C;YAC7C,OAAO;QACT,CAAC;QACD,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAC1C,SAAS,EACT,YAAY,CAAC,SAAS,CACvB,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,YAAY,EACZ,IAAI,kBAAM,CAAe,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,CAC5D,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxD,qEAAqE;QACrE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,cAAc,CACZ,YAA0B,EAC1B,OAAwB,EACxB,SAAkC;QAElC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,6CAA6C;YAC7C,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,YAAY,CAAC,UAAU,CAC5C,SAAS,EACT,YAAY,CAAC,SAAS,EACtB,OAAO,CACR,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CACvB,cAAc,EACd,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAC1E,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB,CAChB,SAAkC,EAClC,SAAiD,EACjD,OAAwB;QAExB,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACnE,MAAM,cAAc,GAAG,YAAY,CAAC,UAAU,CAC5C,SAAS,EACT,SAAS,EACT,OAAO,CACR,CAAC;QACF,MAAM,iBAAiB,GACrB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;QAEzD,MAAM,MAAM,GACV,IAAI,CAAC,aAAa;aACf,GAAG,CAAC,YAAY,CAAC;YAClB,EAAE,GAAG,EAAE;aACN,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAE/D,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,iFAAiF;YACjF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;iBACvD,MAAM,CACL,CAAC,UAAU,EAAE,EAAE;YACb,oDAAoD;YACpD,UAAU,KAAK,IAAI;gBACnB,mDAAmD;gBACnD,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,UAAU,CAAC,CACtD;iBACA,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAClB,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CACxD;iBACA,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC;CACF;AAtQD,oCAsQC"}