{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/solace/0.2.0/server.json", "title": "Solace server bindings object", "description": "This object contains server connection information about the Solace broker. This object contains additional connectivity information not possible to represent within the core AsyncAPI specification.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"msvVpn": {"type": "string", "description": "The name of the Virtual Private Network to connect to on the Solace broker."}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding."}}, "examples": [{"msgVpn": "ProdVPN", "bindingVersion": "0.2.0"}]}