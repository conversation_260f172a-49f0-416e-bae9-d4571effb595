const schemaPrefix = '#/components/schemas/';
export function findRefsUsed(schema, components = {}) {
    const refsUsed = new Set();
    function traverse(value) {
        if (typeof value === 'object' && value !== null) {
            if ('$ref' in value &&
                typeof value.$ref === 'string' &&
                value.$ref.startsWith(schemaPrefix)) {
                const schemaName = value.$ref.slice(schemaPrefix.length);
                if (!refsUsed.has(schemaName)) {
                    refsUsed.add(schemaName);
                    traverse(components[schemaName]);
                }
            }
            for (const val of Object.values(value)) {
                traverse(val);
            }
        }
    }
    traverse(schema);
    return refsUsed;
}
export function stripComponents(valueContainingRefs, rawDocument) {
    var _a;
    const schemas = (_a = rawDocument.components) === null || _a === void 0 ? void 0 : _a.schemas;
    if (schemas === undefined)
        return {};
    const refsUsed = findRefsUsed(valueContainingRefs, schemas);
    const newEntries = Object.entries(schemas).filter(([schemaName]) => refsUsed.has(schemaName));
    return Object.fromEntries(newEntries);
}
