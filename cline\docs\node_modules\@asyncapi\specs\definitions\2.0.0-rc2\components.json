{"type": "object", "description": "An object to hold a set of reusable objects for different aspects of the AsyncAPI Specification.", "additionalProperties": false, "properties": {"schemas": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/schemas.json"}, "messages": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/messages.json"}, "securitySchemes": {"type": "object", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/SecurityScheme.json"}]}}}, "parameters": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/parameters.json"}, "correlationIds": {"type": "object", "patternProperties": {"^[\\w\\d\\.\\-_]+$": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/correlationId.json"}]}}}, "operationTraits": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/operationTrait.json"}}, "messageTraits": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/messageTrait.json"}}, "serverBindings": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/bindingsObject.json"}}, "channelBindings": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/bindingsObject.json"}}, "operationBindings": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/bindingsObject.json"}}, "messageBindings": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/bindingsObject.json"}}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc2/components.json"}