var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { Parser } from '@asyncapi/parser';
export function validateAsyncApi(str) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const parser = new Parser();
            const { document, diagnostics } = yield parser.parse(str);
            if (diagnostics.length > 0) {
                const errorMessages = diagnostics.map((diagnostic) => diagnostic.message).join('\n');
                return {
                    valid: false,
                    errorMessage: errorMessages,
                    document: undefined,
                };
            }
            return {
                valid: true,
                errorMessage: undefined,
                document,
            };
        }
        catch (error) {
            return {
                valid: false,
                errorMessage: `Error validating AsyncAPI document, ${error}`,
                document: undefined,
            };
        }
    });
}
