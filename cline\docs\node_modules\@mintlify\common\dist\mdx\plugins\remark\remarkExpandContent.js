import { visit } from 'unist-util-visit';
export const remarkExpandContent = () => (tree) => {
    visit(tree, 'mdxJsxFlowElement', (node) => {
        if (node.name === 'Accordion' || node.name === 'Expandable') {
            const defaultOpenAttr = node.attributes.find(isDefaultOpenAttr);
            if (defaultOpenAttr) {
                defaultOpenAttr.value = 'true';
            }
            else {
                node.attributes.push({
                    type: 'mdxJsxAttribute',
                    name: 'defaultOpen',
                    value: 'true',
                });
            }
        }
    });
};
const isDefaultOpenAttr = (attr) => {
    return attr.type === 'mdxJsxAttribute' && attr.name === 'defaultOpen';
};
