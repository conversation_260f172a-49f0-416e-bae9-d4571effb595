import type { Element } from 'hast';
import type { <PERSON><PERSON><PERSON><PERSON>, HastNodeIndex, HastNodeParent } from '../types/hast.js';
export declare function gitBookScrapeAccordionGroup(node: HastNode, index: HastNodeIndex, parent: HastNodeParent): Element | undefined;
export declare function readmeScrapeAccordionGroup(node: HastNode, index: HastNodeIndex, parent: HastNodeParent): Element | undefined;
export declare function docusaurusScrapeAccordionGroup(node: HastNode, index: HastNodeIndex, parent: HastNodeParent): Element | undefined;
