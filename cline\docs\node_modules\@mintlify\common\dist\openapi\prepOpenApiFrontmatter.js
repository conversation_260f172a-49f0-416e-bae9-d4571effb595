import { parse, isAbsolute } from 'path';
import { normalizeRelativePath } from '../fs/normalizeRelativePath.js';
import { schemaFileFrontmatterIsOriginalFileLocation, isRemoteSchemaUrl, } from '../schema/common.js';
import { buildOpenApiMetaTag } from './buildOpenApiMetaTag.js';
import { potentiallyParseOpenApiString } from './parseOpenApiString.js';
export function prepOpenApiFrontmatter(currPath, openapiFrontmatter) {
    if (!openapiFrontmatter)
        return undefined;
    const openapiObj = potentiallyParseOpenApiString(openapiFrontmatter);
    if ((openapiObj === null || openapiObj === void 0 ? void 0 : openapiObj.filename) && schemaFileFrontmatterIsOriginalFileLocation(openapiObj.filename)) {
        if (isRemoteSchemaUrl(openapiObj.filename)) {
            return buildOpenApiMetaTag({
                filePath: openapiObj.filename,
                method: openapiObj.method.toLowerCase(),
                path: openapiObj.endpoint,
            });
        }
        const filenameIsAbsolute = isAbsolute(openapiObj.filename);
        const currDir = filenameIsAbsolute ? '/' : parse(currPath).dir;
        const newOpenApiFilename = normalizeRelativePath(currDir, openapiObj.filename);
        return buildOpenApiMetaTag({
            filePath: newOpenApiFilename,
            method: openapiObj.method.toLowerCase(),
            path: openapiObj.endpoint,
        });
    }
    return openapiFrontmatter;
}
