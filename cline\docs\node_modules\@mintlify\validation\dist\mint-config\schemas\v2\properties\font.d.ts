import { z } from 'zod';
export declare const fontDetailsSchema: z.ZodEffects<z.ZodObject<{
    family: z.ZodString;
    weight: z.ZodOptional<z.ZodNumber>;
    source: z.ZodOptional<z.ZodString>;
    format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
}, "strip", z.ZodType<PERSON>ny, {
    family: string;
    weight?: number | undefined;
    source?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}, {
    family: string;
    weight?: number | undefined;
    source?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}>, {
    family: string;
    weight?: number | undefined;
    source?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}, {
    family: string;
    weight?: number | undefined;
    source?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}>;
export declare const fontsSchema: z.ZodUnion<[z.ZodEffects<z.ZodObject<{
    family: z.ZodString;
    weight: z.ZodOptional<z.ZodNumber>;
    source: z.ZodOptional<z.ZodString>;
    format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
}, "strip", z.ZodTypeAny, {
    family: string;
    weight?: number | undefined;
    source?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}, {
    family: string;
    weight?: number | undefined;
    source?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}>, {
    family: string;
    weight?: number | undefined;
    source?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}, {
    family: string;
    weight?: number | undefined;
    source?: string | undefined;
    format?: "woff" | "woff2" | undefined;
}>, z.ZodObject<{
    heading: z.ZodOptional<z.ZodEffects<z.ZodObject<{
        family: z.ZodString;
        weight: z.ZodOptional<z.ZodNumber>;
        source: z.ZodOptional<z.ZodString>;
        format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
    }, "strip", z.ZodTypeAny, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>>;
    body: z.ZodOptional<z.ZodEffects<z.ZodObject<{
        family: z.ZodString;
        weight: z.ZodOptional<z.ZodNumber>;
        source: z.ZodOptional<z.ZodString>;
        format: z.ZodOptional<z.ZodEnum<["woff", "woff2"]>>;
    }, "strip", z.ZodTypeAny, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }, {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    }>>;
}, "strict", z.ZodTypeAny, {
    heading?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | undefined;
    body?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | undefined;
}, {
    heading?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | undefined;
    body?: {
        family: string;
        weight?: number | undefined;
        source?: string | undefined;
        format?: "woff" | "woff2" | undefined;
    } | undefined;
}>]>;
export type FontsType = z.infer<typeof fontsSchema>;
export type FontDetailsType = z.infer<typeof fontDetailsSchema>;
