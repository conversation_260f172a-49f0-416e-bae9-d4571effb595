export const isMdxJsEsm = (node) => node.type === 'mdxjsEsm';
export function estreeIsProgram(node) {
    var _a;
    return ((_a = node.data) === null || _a === void 0 ? void 0 : _a.estree) != null;
}
export const isImportDeclaration = (bodyChild) => bodyChild.type === 'ImportDeclaration';
export const isExport = (type) => [
    'ExportAllDeclaration', // export * from source
    'ExportDefaultDeclaration', // export default declaration
    'ExportNamedDeclaration', // export { ... } from "source"
].includes(type);
export const isExportNode = (bodyChild) => isExport(bodyChild.type);
export const isMdxJsxFlowElement = (node) => node.type === 'mdxJsxFlowElement';
export const createUniqueVariableName = (variableName, id) => `${variableName}_${id}`;
export const getObjectExpressionStringProperty = (key, attribute) => {
    var _a, _b, _c;
    if (!attribute || !attribute.value || typeof attribute.value !== 'object') {
        return undefined;
    }
    if (((_b = (_a = attribute.value.data) === null || _a === void 0 ? void 0 : _a.estree) === null || _b === void 0 ? void 0 : _b.body.length) === 1) {
        const body = attribute.value.data.estree.body[0];
        if ((body === null || body === void 0 ? void 0 : body.type) === 'ExpressionStatement' && body.expression.type === 'ObjectExpression') {
            const properties = body.expression.properties;
            const property = properties.find((prop) => prop.type === 'Property' && prop.key.type === 'Identifier' && prop.key.name === key);
            if (property && property.type === 'Property' && property.value.type === 'Literal') {
                return (_c = property.value.value) === null || _c === void 0 ? void 0 : _c.toString();
            }
        }
    }
};
export const getArrayExpressionStringProperties = (attribute) => {
    var _a, _b;
    const collection = [];
    if (!attribute || !attribute.value || typeof attribute.value !== 'object') {
        return collection;
    }
    if (((_b = (_a = attribute.value.data) === null || _a === void 0 ? void 0 : _a.estree) === null || _b === void 0 ? void 0 : _b.body.length) === 1) {
        const body = attribute.value.data.estree.body[0];
        if ((body === null || body === void 0 ? void 0 : body.type) === 'ExpressionStatement' && body.expression.type === 'ArrayExpression') {
            body.expression.elements.map((element) => {
                if ((element === null || element === void 0 ? void 0 : element.type) === 'Literal' && typeof element.value === 'string') {
                    collection.push(element.value);
                }
            });
        }
    }
    return collection;
};
