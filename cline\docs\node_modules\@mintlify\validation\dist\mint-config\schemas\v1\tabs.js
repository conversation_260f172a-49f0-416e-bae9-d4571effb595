import { z } from 'zod';
import { openapiAnchorOrTab } from './openapiAnchorOrTab.js';
const tabSchema = z.object({
    name: z.string().trim().nonempty(),
    url: z.string().trim().nonempty(),
    version: z.string().optional(),
    isDefaultHidden: z.boolean().optional(),
    openapi: openapiAnchorOrTab,
});
export const tabsSchema = tabSchema.array();
export const primaryTabSchema = z
    .object({
    name: z.string(),
    isDefaultHidden: z.boolean().optional(),
})
    .strict();
