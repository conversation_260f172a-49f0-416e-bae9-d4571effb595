/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {RowContent, TableRow} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {TableRow}
 *   mdast node.
 */
export function tableRow(state: State, node: Readonly<Element>): TableRow;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { TableRow } from 'mdast';
//# sourceMappingURL=table-row.d.ts.map