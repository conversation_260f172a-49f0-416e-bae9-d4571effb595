{"type": "object", "description": "An object representing a Server.", "required": ["url", "scheme"], "additionalProperties": false, "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.0.0/vendorExtension.json"}}, "properties": {"url": {"type": "string"}, "description": {"type": "string"}, "scheme": {"type": "string", "description": "The transfer protocol.", "enum": ["kafka", "kafka-secure", "amqp", "amqps", "mqtt", "mqtts", "secure-mqtt", "ws", "wss", "stomp", "stomps"]}, "schemeVersion": {"type": "string"}, "variables": {"$ref": "http://asyncapi.com/definitions/1.0.0/serverVariables.json"}}, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.0.0/server.json"}