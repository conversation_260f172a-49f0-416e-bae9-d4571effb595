{"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["userPassword"]}, "description": {"type": "string"}}, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "example": {"$ref": "http://asyncapi.com/examples/3.0.0/userPassword.json"}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/3.0.0/userPassword.json"}