{"version": 3, "file": "ink.js", "sourceRoot": "", "sources": ["../src/ink.tsx"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,KAAuB,MAAM,OAAO,CAAC;AAC5C,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,WAAW,MAAM,cAAc,CAAC;AACvC,OAAO,MAAM,MAAM,UAAU,CAAC;AAC9B,OAAO,QAAQ,MAAM,WAAW,CAAC;AACjC,OAAO,UAAU,MAAM,aAAa,CAAC;AACrC,OAAO,YAAY,MAAM,eAAe,CAAC;AAEzC,OAAO,IAAI,MAAM,aAAa,CAAC;AAC/B,OAAO,UAAU,MAAM,iBAAiB,CAAC;AACzC,OAAO,MAAM,MAAM,eAAe,CAAC;AACnC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAC;AAChC,OAAO,SAA2B,MAAM,iBAAiB,CAAC;AAC1D,OAAO,SAAS,MAAM,gBAAgB,CAAC;AACvC,OAAO,GAAG,MAAM,qBAAqB,CAAC;AAEtC,MAAM,IAAI,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;AAYtB,MAAM,CAAC,OAAO,OAAO,GAAG;IACN,OAAO,CAAU;IACjB,GAAG,CAAY;IACf,YAAY,CAAY;IACzC,iFAAiF;IACzE,WAAW,CAAU;IACrB,UAAU,CAAS;IACV,SAAS,CAAY;IACrB,QAAQ,CAAiB;IAC1C,uEAAuE;IACvE,wFAAwF;IAChF,gBAAgB,CAAS;IACzB,WAAW,CAAiB;IAC5B,cAAc,CAAc;IACnB,iBAAiB,CAAc;IAEhD,YAAY,OAAgB;QAC3B,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAErD,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK;YACrC,CAAC,CAAC,IAAI,CAAC,QAAQ;YACf,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;gBAC5B,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;aACd,CAAC,CAAC;QAEL,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChD,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK;YAChC,CAAC,CAAC,IAAI,CAAC,GAAG;YACV,CAAC,CAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,EAAE;gBAC/B,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;aACd,CAA0B,CAAC;QAE9B,iFAAiF;QACjF,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,iDAAiD;QACjD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,uEAAuE;QACvE,wFAAwF;QACxF,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAE3B,mEAAmE;QACnE,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,eAAe,CAC1C,IAAI,CAAC,QAAQ;QACb,cAAc;QACd,CAAC,EACD,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACJ,CAAC;QAEF,6BAA6B;QAC7B,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAC,UAAU,EAAE,KAAK,EAAC,CAAC,CAAC;QAErE,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,EAAE,CAAC;YACnC,UAAU,CAAC,kBAAkB,CAAC;gBAC7B,UAAU,EAAE,CAAC;gBACb,2CAA2C;gBAC3C,4EAA4E;gBAC5E,OAAO,EAAE,SAAS;gBAClB,mBAAmB,EAAE,KAAK;aAC1B,CAAC,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAE1C,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE;gBAC7B,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,CAAC,CAAC;QACH,CAAC;IACF,CAAC;IAED,OAAO,GAAG,GAAG,EAAE;QACd,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;IACjB,CAAC,CAAC;IAEF,kBAAkB,GAAe,GAAG,EAAE,GAAE,CAAC,CAAC;IAC1C,iBAAiB,GAA6B,GAAG,EAAE,GAAE,CAAC,CAAC;IACvD,eAAe,GAAe,GAAG,EAAE,GAAE,CAAC,CAAC;IAEvC,eAAe,GAAG,GAAG,EAAE;QACtB,qEAAqE;QACrE,mCAAmC;QACnC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;QAExD,IAAI,CAAC,QAAQ,CAAC,QAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAEhD,IAAI,CAAC,QAAQ,CAAC,QAAS,CAAC,eAAe,CACtC,SAAS,EACT,SAAS,EACT,IAAI,CAAC,aAAa,CAClB,CAAC;IACH,CAAC,CAAC;IAEF,QAAQ,GAAe,GAAG,EAAE;QAC3B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACR,CAAC;QAED,MAAM,EAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAC,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEnE,8EAA8E;QAC9E,MAAM,eAAe,GAAG,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC;QAE9D,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,eAAe,EAAE,CAAC;gBACrB,IAAI,CAAC,gBAAgB,IAAI,YAAY,CAAC;YACvC,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,CAAC;YAC1D,OAAO;QACR,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACZ,IAAI,eAAe,EAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACzC,CAAC;YAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YACzB,OAAO;QACR,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC,gBAAgB,IAAI,YAAY,CAAC;QACvC,CAAC;QAED,IAAI,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC9C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CACxB,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAC1D,CAAC;YACF,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YACzB,OAAO;QACR,CAAC;QAED,0FAA0F;QAC1F,IAAI,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACxC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,eAAe,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;IAC1B,CAAC,CAAC;IAEF,MAAM,CAAC,IAAe;QACrB,MAAM,IAAI,GAAG,CACZ,oBAAC,GAAG,IACH,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EACzB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAC3B,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAC3B,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EACrC,MAAM,EAAE,IAAI,CAAC,OAAO,IAEnB,IAAI,CACA,CACN,CAAC;QAEF,UAAU,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9D,CAAC;IAED,aAAa,CAAC,IAAY;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACR,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1E,OAAO;QACR,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO;QACR,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC3B,CAAC;IAED,aAAa,CAAC,IAAY;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACR,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YACnE,OAAO;QACR,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO;QACR,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC3B,CAAC;IAED,wDAAwD;IACxD,OAAO,CAAC,KAA6B;QACpC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACR,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,OAAO,IAAI,CAAC,cAAc,KAAK,UAAU,EAAE,CAAC;YAC/C,IAAI,CAAC,cAAc,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,OAAO,IAAI,CAAC,iBAAiB,KAAK,UAAU,EAAE,CAAC;YAClD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1B,CAAC;QAED,gEAAgE;QAChE,8CAA8C;QAC9C,IAAI,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;QACnD,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,UAAU,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7D,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEtC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3B,CAAC;IACF,CAAC;IAED,KAAK,CAAC,aAAa;QAClB,IAAI,CAAC,WAAW,KAAK,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;YAClC,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAED,KAAK;QACJ,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;IACF,CAAC;IAED,YAAY;QACX,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACxB,OAAO;QACR,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YACnD,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YAED,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACzB,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;gBAEnE,IAAI,CAAC,cAAc,EAAE,CAAC;oBACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC;YACF,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;CACD"}