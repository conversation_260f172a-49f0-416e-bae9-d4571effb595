// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface McpServiceDefinition {
  // MCP service methods
}

export interface McpServer {
  id: string;
  name: string;
  description?: string;
  command: string;
  args: string[];
  env?: { [key: string]: string };
  cwd?: string;
  disabled?: boolean;
  alwaysAllow?: string[];
}

export interface McpServers {
  servers: McpServer[];
}

export interface McpServerConfig {
  command: string;
  args: string[];
  env?: { [key: string]: string };
  cwd?: string;
  disabled?: boolean;
  alwaysAllow?: string[];
}

export interface McpServerInstallRequest {
  name: string;
  config: McpServerConfig;
}

export interface McpServerUninstallRequest {
  name: string;
}

export interface McpServerUpdateRequest {
  name: string;
  config: McpServerConfig;
}

export interface McpServerListResponse {
  servers: { [key: string]: McpServerConfig };
}

export interface McpServerStatusResponse {
  name: string;
  status: string;
  error?: string;
}

export interface AddRemoteMcpServerRequest {
  name: string;
  url: string;
  config?: McpServerConfig;
}

export interface ToggleMcpServerRequest {
  name: string;
  enabled: boolean;
}

export interface ToggleToolAutoApproveRequest {
  serverName: string;
  toolName: string;
  autoApprove: boolean;
}

export interface UpdateMcpTimeoutRequest {
  serverName: string;
  timeout: number;
}

export enum McpServerStatus {
  MCP_SERVER_STATUS_CONNECTED = "MCP_SERVER_STATUS_CONNECTED",
  MCP_SERVER_STATUS_CONNECTING = "MCP_SERVER_STATUS_CONNECTING",
  MCP_SERVER_STATUS_DISCONNECTED = "MCP_SERVER_STATUS_DISCONNECTED"
}

export interface McpTool {
  name: string;
  description?: string;
  inputSchema?: any;
}

export interface McpResource {
  uri: string;
  name?: string;
  description?: string;
  mimeType?: string;
}

export interface McpResourceTemplate {
  uriTemplate: string;
  name?: string;
  description?: string;
  mimeType?: string;
}
