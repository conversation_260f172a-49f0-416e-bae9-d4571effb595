{"type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.5.0/specificationExtension.json"}}, "properties": {"traits": {"type": "array", "items": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.5.0/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.5.0/operationTrait.json"}]}}, "summary": {"type": "string"}, "description": {"type": "string"}, "security": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/2.5.0/SecurityRequirement.json"}}, "tags": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/2.5.0/tag.json"}, "uniqueItems": true}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/2.5.0/externalDocs.json"}, "operationId": {"type": "string"}, "bindings": {"$ref": "http://asyncapi.com/definitions/2.5.0/bindingsObject.json"}, "message": {"$ref": "http://asyncapi.com/definitions/2.5.0/message.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.5.0/operation.json"}