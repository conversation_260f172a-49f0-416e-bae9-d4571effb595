import { z } from 'zod';
const redirectSchema = z.object({
    source: z.string().describe('The source path to redirect from'),
    destination: z.string().describe('The destination path to redirect to. Supports wildcards.'),
    permanent: z
        .boolean()
        .optional()
        .describe('Whether the redirect is a 308 permanent redirect. Otherwise, it is a 307 temporary redirect.'),
});
export const redirectsSchema = z
    .array(redirectSchema)
    .refine((value) => {
    const keys = value.map((obj) => obj.source);
    return new Set(keys).size === keys.length;
}, 'Sources in the array must be unique')
    .describe('URL redirect rules');
