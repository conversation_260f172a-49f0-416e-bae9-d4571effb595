import type { FrontmatterContent, Heading, Parent, Root, RootContent } from 'mdast';
import { MdxJsxFlowElement } from 'mdast-util-mdx-jsx';
import { RSSItemV2 } from '../types/rss.js';
export declare const UPDATE_MAX = 15;
export type UpdateMDXComponent = MdxJsxFlowElement;
export declare const isFrontmatter: (node: RootContent | undefined) => node is FrontmatterContent;
export declare const isUpdate: (node: RootContent | undefined) => node is UpdateMDXComponent;
export declare const isHeading: (node: RootContent | undefined) => node is Heading;
export declare const isNormalMarkdown: (node: RootContent | undefined) => node is RootContent;
export declare const containsUpdates: (tree: Root) => boolean;
export declare const getTags: (node: UpdateMDXComponent) => string[] | undefined;
export declare const getRssPropsData: (updateComponent: UpdateMDXComponent) => {
    rssTitle: string | undefined;
    rssDescription: string | undefined;
};
export declare const getUpdateTitle: (updateComponent: UpdateMDXComponent) => string | undefined;
export declare const getUpdateDescription: (updateComponent: UpdateMDXComponent) => string | undefined;
export declare const compareUpdates: ({ newTree, previousTree, }: {
    newTree: Root;
    previousTree: Root;
}) => UpdateMDXComponent[];
export declare const matchRSSTitle: (node: MdxJsxFlowElement, title: string) => boolean;
export declare const splitChildrenAtHeadings: (children: RootContent[]) => RootContent[][];
export declare const getMarkdownHeadingProps: (heading: Parent) => {
    title: string | undefined;
    anchor: string | undefined;
};
export declare const updateGroupToRSSItemV2: ({ group, date, }: {
    group: RootContent[];
    date?: string;
}) => RSSItemV2 | undefined;
export declare const getNewContent: (newUpdateComponents: UpdateMDXComponent[]) => RSSItemV2[];
export declare const getNewMarkdownUpdates: ({ newTree, previousTree, previousUpdates, }: {
    newTree: Root;
    previousTree: Root;
    previousUpdates: RSSItemV2[];
}) => RSSItemV2[];
