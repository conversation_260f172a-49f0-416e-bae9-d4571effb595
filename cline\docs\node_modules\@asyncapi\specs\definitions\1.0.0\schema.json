{"type": "object", "description": "A deterministic version of a JSON Schema object.", "patternProperties": {"^x-": {"$ref": "http://asyncapi.com/definitions/1.0.0/vendorExtension.json"}}, "properties": {"$ref": {"type": "string"}, "format": {"type": "string"}, "title": {"$ref": "http://json-schema.org/draft-04/schema#/properties/title"}, "description": {"$ref": "http://json-schema.org/draft-04/schema#/properties/description"}, "default": {"$ref": "http://json-schema.org/draft-04/schema#/properties/default"}, "multipleOf": {"$ref": "http://json-schema.org/draft-04/schema#/properties/multipleOf"}, "maximum": {"$ref": "http://json-schema.org/draft-04/schema#/properties/maximum"}, "exclusiveMaximum": {"$ref": "http://json-schema.org/draft-04/schema#/properties/exclusiveMaximum"}, "minimum": {"$ref": "http://json-schema.org/draft-04/schema#/properties/minimum"}, "exclusiveMinimum": {"$ref": "http://json-schema.org/draft-04/schema#/properties/exclusiveMinimum"}, "maxLength": {"$ref": "http://json-schema.org/draft-04/schema#/definitions/positiveInteger"}, "minLength": {"$ref": "http://json-schema.org/draft-04/schema#/definitions/positiveIntegerDefault0"}, "pattern": {"$ref": "http://json-schema.org/draft-04/schema#/properties/pattern"}, "maxItems": {"$ref": "http://json-schema.org/draft-04/schema#/definitions/positiveInteger"}, "minItems": {"$ref": "http://json-schema.org/draft-04/schema#/definitions/positiveIntegerDefault0"}, "uniqueItems": {"$ref": "http://json-schema.org/draft-04/schema#/properties/uniqueItems"}, "maxProperties": {"$ref": "http://json-schema.org/draft-04/schema#/definitions/positiveInteger"}, "minProperties": {"$ref": "http://json-schema.org/draft-04/schema#/definitions/positiveIntegerDefault0"}, "required": {"$ref": "http://json-schema.org/draft-04/schema#/definitions/stringArray"}, "enum": {"$ref": "http://json-schema.org/draft-04/schema#/properties/enum"}, "additionalProperties": {"anyOf": [{"$ref": "http://asyncapi.com/definitions/1.0.0/schema.json"}, {"type": "boolean"}], "default": {}}, "type": {"$ref": "http://json-schema.org/draft-04/schema#/properties/type"}, "items": {"anyOf": [{"$ref": "http://asyncapi.com/definitions/1.0.0/schema.json"}, {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/1.0.0/schema.json"}}], "default": {}}, "allOf": {"type": "array", "minItems": 1, "items": {"$ref": "http://asyncapi.com/definitions/1.0.0/schema.json"}}, "properties": {"type": "object", "additionalProperties": {"$ref": "http://asyncapi.com/definitions/1.0.0/schema.json"}, "default": {}}, "discriminator": {"type": "string"}, "readOnly": {"type": "boolean", "default": false}, "xml": {"$ref": "http://asyncapi.com/definitions/1.0.0/xml.json"}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/1.0.0/externalDocs.json"}, "example": {}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-04/schema#", "id": "http://asyncapi.com/definitions/1.0.0/schema.json"}