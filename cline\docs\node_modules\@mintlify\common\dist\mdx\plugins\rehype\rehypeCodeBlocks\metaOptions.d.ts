export type MetaOptionBase = {
    index: number;
    raw: string;
    key: string | undefined;
    valueStartDelimiter: string;
    valueEndDelimiter: string;
};
export type MetaOptionString = MetaOptionBase & {
    kind: 'string';
    value: string;
};
export type MetaOptionRange = MetaOptionBase & {
    kind: 'range';
    value: string;
};
export type MetaOptionRegExp = MetaOptionBase & {
    kind: 'regexp';
    value: RegExp;
};
export type MetaOptionBoolean = MetaOptionBase & {
    kind: 'boolean';
    value: boolean;
};
export type MetaOption = MetaOptionString | MetaOptionRange | MetaOptionRegExp | MetaOptionBoolean;
export type MetaOptionKind = MetaOption['kind'];
export declare class MetaOptions {
    constructor(input: string, reservedKeys?: string[]);
    parsedOptions: MetaOption[];
    errors: string[] | undefined;
    remainingText: string;
    /**
     * A list of error messages that occurred when parsing the meta string,
     * or `undefined` if no errors occurred.
     */
    get getErrors(): string[] | undefined;
    /**
     * Returns the remaining text after all parsed options are removed.
     * This is useful for extracting filename from meta strings.
     */
    getRemainingText(): string;
    /**
     * Returns a list of meta options, optionally filtered by their key and/or {@link MetaOptionKind}.
     *
     * @param keyOrKeys
     * Allows to filter the options by key. An empty string will return options without a key.
     * A non-empty string will return options with a matching key (case-insensitive).
     * An array of strings will return options with any of the matching keys.
     * If omitted, no key-based filtering will be applied.
     *
     * @param kind
     * Allows to filter the options by {@link MetaOptionKind}.
     * If omitted, no kind-based filtering will be applied.
     */
    list<K extends MetaOptionKind | undefined = undefined>(keyOrKeys?: string | string[], kind?: K): K extends "string" | "boolean" | "range" | "regexp" ? (Extract<MetaOptionString, {
        kind: K;
    }> | Extract<MetaOptionRange, {
        kind: K;
    }> | Extract<MetaOptionRegExp, {
        kind: K;
    }> | Extract<MetaOptionBoolean, {
        kind: K;
    }>)[] : MetaOption[];
    value<K extends MetaOptionKind | undefined = undefined>(key: string, kind?: K): (K extends "string" | "boolean" | "range" | "regexp" ? Extract<MetaOptionString, {
        kind: K;
    }> | Extract<MetaOptionRange, {
        kind: K;
    }> | Extract<MetaOptionRegExp, {
        kind: K;
    }> | Extract<MetaOptionBoolean, {
        kind: K;
    }> : MetaOption)["value"] | undefined;
    /**
     * Returns the last string value with the given key (case-insensitive),
     * or without a key by passing an empty string.
     */
    getString(key: string): string | undefined;
    /**
     * Returns an array of all string values with the given keys (case-insensitive),
     * or without a key by passing an empty string.
     */
    getStrings(keyOrKeys?: string | string[]): string[];
    /**
     * Returns the last range value (`{value}`) with the given key (case-insensitive),
     * or without a key by passing an empty string.
     */
    getRange(key: string): string | undefined;
    /**
     * Returns an array of all range values (`{value}`) with the given keys (case-insensitive),
     * or without a key by passing an empty string.
     */
    getRanges(keyOrKeys?: string | string[]): string[];
    /**
     * Returns the last integer value with the given key (case-insensitive),
     * or without a key by passing an empty string.
     */
    getInteger(key: string): number | undefined;
    /**
     * Returns an array of all integer values with the given keys (case-insensitive),
     * or without a key by passing an empty string.
     */
    getIntegers(keyOrKeys?: string | string[]): number[];
    getStringsWithInteger(keyOrKeys?: string | string[]): number[];
    /**
     * Returns an array of all integers from range values with the given keys (case-insensitive),
     * parsing ranges like "1,3-5" to return [1,3,4,5].
     * Works with range values (`{value}`) that contain comma-separated integers and ranges.
     */
    getRangesWithInteger(keyOrKeys?: string | string[]): number[];
    /**
     * Returns the last RegExp value (`/value/`) with the given key (case-insensitive),
     * or without a key by passing an empty string.
     */
    getRegExp(key: string): RegExp | undefined;
    /**
     * Returns an array of all RegExp values (`/value/`) with the given keys (case-insensitive),
     * or without a key by passing an empty string.
     */
    getRegExps(keyOrKeys?: string | string[]): RegExp[];
    /**
     * Returns the last boolean value with the given key (case-insensitive).
     */
    getBoolean(key: string): boolean | undefined;
}
