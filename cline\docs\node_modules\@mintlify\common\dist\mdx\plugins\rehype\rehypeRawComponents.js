import { fromHtml } from 'hast-util-from-html';
import { visit } from 'unist-util-visit';
export const rehypeRawComponents = () => {
    return (tree) => {
        visit(tree, 'raw', (raw, index, parent) => {
            const rawAst = fromHtml(raw.value, { fragment: true });
            if (parent && index !== undefined) {
                // @ts-expect-error we're passing a component of type Root to parent.children which
                // is fine even though parent.children should only take RootContent, bc `remark`
                // will flatten out Roots into single nodes of type RootContent when serializing
                parent.children[index] = rawAst;
            }
        });
    };
};
