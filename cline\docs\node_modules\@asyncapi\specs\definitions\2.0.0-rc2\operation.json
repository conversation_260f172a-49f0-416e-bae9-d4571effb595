{"type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/specificationExtension.json"}}, "properties": {"traits": {"type": "array", "items": {"oneOf": [{"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/Reference.json"}, {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/operationTrait.json"}]}}, "summary": {"type": "string"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/tag.json"}, "uniqueItems": true}, "externalDocs": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/externalDocs.json"}, "operationId": {"type": "string"}, "bindings": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/bindingsObject.json"}, "message": {"$ref": "http://asyncapi.com/definitions/2.0.0-rc2/message.json"}}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/definitions/2.0.0-rc2/operation.json"}