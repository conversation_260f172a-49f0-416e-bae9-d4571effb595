import { z } from 'zod';
export declare const appearanceSchema: z.ZodObject<{
    default: z.<PERSON>od<PERSON>ptional<z.Zod<PERSON>num<["system", "light", "dark"]>>;
    strict: z.<PERSON>od<PERSON>ptional<z.ZodBoolean>;
}, "strip", z.<PERSON>, {
    default?: "light" | "dark" | "system" | undefined;
    strict?: boolean | undefined;
}, {
    default?: "light" | "dark" | "system" | undefined;
    strict?: boolean | undefined;
}>;
