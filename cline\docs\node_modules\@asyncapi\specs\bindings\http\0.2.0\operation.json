{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/http/0.2.0/operation.json", "title": "HTTP operation bindings object", "description": "This object contains information about the operation representation in HTTP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"method": {"type": "string", "enum": ["GET", "PUT", "POST", "PATCH", "DELETE", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "description": "When 'type' is 'request', this is the HTTP method, otherwise it MUST be ignored. Its value MUST be one of 'GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS', 'CONNECT', and 'TRACE'."}, "query": {"$ref": "http://asyncapi.com/definitions/3.0.0/schema.json", "description": "A Schema object containing the definitions for each query parameter. This schema MUST be of type 'object' and have a properties key."}, "bindingVersion": {"type": "string", "enum": ["0.2.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "examples": [{"query": {"type": "object", "required": ["companyId"], "properties": {"companyId": {"type": "number", "minimum": 1, "description": "The Id of the company."}}, "additionalProperties": false}, "bindingVersion": "0.2.0"}, {"method": "GET", "query": {"type": "object", "required": ["companyId"], "properties": {"companyId": {"type": "number", "minimum": 1, "description": "The Id of the company."}}, "additionalProperties": false}, "bindingVersion": "0.2.0"}]}