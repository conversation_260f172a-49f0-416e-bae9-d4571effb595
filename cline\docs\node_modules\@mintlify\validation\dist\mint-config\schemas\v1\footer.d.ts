import { z } from 'zod';
export declare const footerLinkSchema: z.ZodObject<{
    label: z.ZodString;
    url: z.ZodU<PERSON><[z.ZodString, z.ZodString]>;
}, "strip", z.ZodTypeAny, {
    url: string;
    label: string;
}, {
    url: string;
    label: string;
}>;
export declare const footerLinksColumnSchema: z.ZodObject<{
    title: z.ZodOptional<z.ZodString>;
    links: z.<PERSON><z.ZodObject<{
        label: z.ZodString;
        url: z.ZodU<PERSON>n<[z.ZodString, z.ZodString]>;
    }, "strip", z.ZodTypeAny, {
        url: string;
        label: string;
    }, {
        url: string;
        label: string;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    links: {
        url: string;
        label: string;
    }[];
    title?: string | undefined;
}, {
    links: {
        url: string;
        label: string;
    }[];
    title?: string | undefined;
}>;
export declare const footerSocialsSchema: z.<PERSON>U<PERSON><[z.<PERSON>od<PERSON><PERSON><z.Zod<PERSON>num<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>, z.ZodString>, z.ZodArray<z.ZodObject<{
    type: z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>;
    url: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
    url: string;
}, {
    type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
    url: string;
}>, "many">]>;
export declare const footerSchema: z.ZodObject<{
    socials: z.ZodOptional<z.ZodUnion<[z.ZodRecord<z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>, z.ZodString>, z.ZodArray<z.ZodObject<{
        type: z.ZodEnum<["x", "website", "facebook", "youtube", "discord", "slack", "github", "linkedin", "instagram", "hacker-news", "medium", "telegram", "twitter", "x-twitter", "earth-americas", "bluesky", "threads", "reddit", "podcast"]>;
        url: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }, {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }>, "many">]>>;
    links: z.ZodOptional<z.ZodArray<z.ZodObject<{
        title: z.ZodOptional<z.ZodString>;
        links: z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            url: z.ZodUnion<[z.ZodString, z.ZodString]>;
        }, "strip", z.ZodTypeAny, {
            url: string;
            label: string;
        }, {
            url: string;
            label: string;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        links: {
            url: string;
            label: string;
        }[];
        title?: string | undefined;
    }, {
        links: {
            url: string;
            label: string;
        }[];
        title?: string | undefined;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }[] | undefined;
    links?: {
        links: {
            url: string;
            label: string;
        }[];
        title?: string | undefined;
    }[] | undefined;
}, {
    socials?: Partial<Record<"github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast", string>> | {
        type: "github" | "x" | "website" | "facebook" | "youtube" | "discord" | "slack" | "linkedin" | "instagram" | "hacker-news" | "medium" | "telegram" | "twitter" | "x-twitter" | "earth-americas" | "bluesky" | "threads" | "reddit" | "podcast";
        url: string;
    }[] | undefined;
    links?: {
        links: {
            url: string;
            label: string;
        }[];
        title?: string | undefined;
    }[] | undefined;
}>;
