// Temporary proto types for webview-ui development
// These should be replaced with properly generated proto files

export interface EmptyRequest {}

export interface Boolean {
  value: boolean;
}

export interface StringValue {
  value: string;
}

export interface Int32Value {
  value: number;
}

export interface UInt32Value {
  value: number;
}

export interface Int64Value {
  value: string;
}

export interface UInt64Value {
  value: string;
}

export interface FloatValue {
  value: number;
}

export interface DoubleValue {
  value: number;
}

export interface BytesValue {
  value: Uint8Array;
}

export interface StringRequest {
  value: string;
}

export interface BooleanRequest {
  value: boolean;
}

export interface Int64Request {
  value: string;
}

export interface Int64 {
  value: string;
}

export interface StringArrayRequest {
  values: string[];
}
