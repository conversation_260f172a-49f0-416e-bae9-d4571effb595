{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://asyncapi.com/bindings/amqp/0.3.0/channel.json", "title": "AMQP channel bindings object", "description": "This object contains information about the channel representation in AMQP.", "type": "object", "additionalProperties": false, "patternProperties": {"^x-[\\w\\d\\.\\x2d_]+$": {"$ref": "http://asyncapi.com/definitions/3.0.0/specificationExtension.json"}}, "properties": {"is": {"type": "string", "enum": ["queue", "routing<PERSON>ey"], "description": "Defines what type of channel is it. Can be either 'queue' or 'routingKey' (default)."}, "exchange": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255, "description": "The name of the exchange. It MUST NOT exceed 255 characters long."}, "type": {"type": "string", "enum": ["topic", "direct", "fanout", "default", "headers"], "description": "The type of the exchange. Can be either 'topic', 'direct', 'fanout', 'default' or 'headers'."}, "durable": {"type": "boolean", "description": "Whether the exchange should survive broker restarts or not."}, "autoDelete": {"type": "boolean", "description": "Whether the exchange should be deleted when the last queue is unbound from it."}, "vhost": {"type": "string", "default": "/", "description": "The virtual host of the exchange. Defaults to '/'."}}, "description": "When is=routingKey, this object defines the exchange properties."}, "queue": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255, "description": "The name of the queue. It MUST NOT exceed 255 characters long."}, "durable": {"type": "boolean", "description": "Whether the queue should survive broker restarts or not."}, "exclusive": {"type": "boolean", "description": "Whether the queue should be used only by one connection or not."}, "autoDelete": {"type": "boolean", "description": "Whether the queue should be deleted when the last consumer unsubscribes."}, "vhost": {"type": "string", "default": "/", "description": "The virtual host of the queue. Defaults to '/'."}}, "description": "When is=queue, this object defines the queue properties."}, "bindingVersion": {"type": "string", "enum": ["0.3.0"], "description": "The version of this binding. If omitted, 'latest' MUST be assumed."}}, "oneOf": [{"properties": {"is": {"const": "routing<PERSON>ey"}}, "required": ["exchange"], "not": {"required": ["queue"]}}, {"properties": {"is": {"const": "queue"}}, "required": ["queue"], "not": {"required": ["exchange"]}}], "examples": [{"is": "routing<PERSON>ey", "exchange": {"name": "myExchange", "type": "topic", "durable": true, "autoDelete": false, "vhost": "/"}, "bindingVersion": "0.3.0"}, {"is": "queue", "queue": {"name": "my-queue-name", "durable": true, "exclusive": true, "autoDelete": false, "vhost": "/"}, "bindingVersion": "0.3.0"}]}