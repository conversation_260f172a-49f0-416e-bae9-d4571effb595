import { z } from 'zod';
import { customErrorMap } from './customErrorMap.js';
import { refineMissingVersions } from './refinements/refineMissingVersions.js';
import { mintConfigSchema } from './schemas/v1/config.js';
import { docsConfigSchema } from './schemas/v2/index.js';
import { aggregateWarnings } from './warnings/aggregateWarnings.js';
export function validateMintConfig(value) {
    // save old error map, as this error map is configured globally, and we don't want to overwrite global behavior
    const oldErrorMap = z.getErrorMap();
    z.setErrorMap(customErrorMap);
    const refinedSchema = mintConfigSchema.superRefine(refineMissingVersions);
    const validationResults = refinedSchema.safeParse(value);
    // reset error map
    z.setErrorMap(oldErrorMap);
    if (!validationResults.success)
        return validationResults;
    return Object.assign(Object.assign({}, validationResults), { warnings: aggregateWarnings(validationResults.data) });
}
export function validateDocsConfig(value) {
    const oldErrorMap = z.getErrorMap();
    z.setErrorMap(customErrorMap);
    const validationResults = docsConfigSchema.safeParse(value);
    z.setErrorMap(oldErrorMap);
    if (!validationResults.success)
        return validationResults;
    return Object.assign(Object.assign({}, validationResults), { warnings: [] });
}
